package com.laoshu198838.repository.overdue_debt;

import com.laoshu198838.annotation.DataSource;
import com.laoshu198838.entity.overdue_debt.OverdueDebtAdd;
import java.util.List;
import java.util.Map;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * 一致性检查专用仓库
 * 提供跨表一致性检查的SQL查询
 * 统一的Repository，替代各模块中的重复Repository
 *
 * <AUTHOR>
 */
@Repository
@DataSource("primary")
public interface ConsistencyCheckRepository extends JpaRepository<OverdueDebtAdd, OverdueDebtAdd.OverdueDebtAddKey> {

    /**
     * 获取新增金额汇总数据
     *
     * @param year 年份
     * @return 各表新增金额汇总
     */
    @Query(value =
                   "SELECT 'newAmount' as checkType, " +
                   "       SUM(CASE " +
                   "           WHEN :month = 1 THEN a.`1月` " +
                   "           WHEN :month = 2 THEN a.`2月` " +
                   "           WHEN :month = 3 THEN a.`3月` " +
                   "           WHEN :month = 4 THEN a.`4月` " +
                   "           WHEN :month = 5 THEN a.`5月` " +
                   "           WHEN :month = 6 THEN a.`6月` " +
                   "           WHEN :month = 7 THEN a.`7月` " +
                   "           WHEN :month = 8 THEN a.`8月` " +
                   "           WHEN :month = 9 THEN a.`9月` " +
                   "           WHEN :month = 10 THEN a.`10月` " +
                   "           WHEN :month = 11 THEN a.`11月` " +
                   "           WHEN :month = 12 THEN a.`12月` " +
                   "           ELSE 0 END) as addTableAmount, " +
                   "       (SELECT SUM(l.本月新增债权 + COALESCE(l.互转增加金额, 0)) FROM 诉讼表 l WHERE l.年份 = :year AND l.月份 = :month) as litigationAmount, " +
                   "       (SELECT SUM(n.本月新增债权 + COALESCE(n.互转增加金额, 0)) FROM 非诉讼表 n WHERE n.年份 = :year AND n.月份 = :month) as nonLitigationAmount, " +
                   "       (SELECT SUM(i.本月新增债权) FROM 减值准备表 i WHERE i.年份 = :year AND i.月份 = :month) as impairmentAmount " +
                   "FROM 新增表 a " +
                   "WHERE a.年份 = :year",
           nativeQuery = true)
    Map<String, Object> getNewAmountSummary(@Param("year") int year, @Param("month") int month);

    /**
     * 获取年初至指定月的累计新增金额汇总
     */
    @Query(value =
                   "SELECT 'yearNewAmount' AS checkType, " +
                   "       COALESCE(SUM( " +
                   "         IF(:month >= 1, COALESCE(a.`1月`,0), 0) + " +
                   "         IF(:month >= 2, COALESCE(a.`2月`,0), 0) + " +
                   "         IF(:month >= 3, COALESCE(a.`3月`,0), 0) + " +
                   "         IF(:month >= 4, COALESCE(a.`4月`,0), 0) + " +
                   "         IF(:month >= 5, COALESCE(a.`5月`,0), 0) + " +
                   "         IF(:month >= 6, COALESCE(a.`6月`,0), 0) + " +
                   "         IF(:month >= 7, COALESCE(a.`7月`,0), 0) + " +
                   "         IF(:month >= 8, COALESCE(a.`8月`,0), 0) + " +
                   "         IF(:month >= 9, COALESCE(a.`9月`,0), 0) + " +
                   "         IF(:month >= 10, COALESCE(a.`10月`,0), 0) + " +
                   "         IF(:month >= 11, COALESCE(a.`11月`,0), 0) + " +
                   "         IF(:month >= 12, COALESCE(a.`12月`,0), 0) " +
                   "       ), 0) AS addTableAmount, " +
                   "       COALESCE((SELECT SUM(l.`本月新增债权` + COALESCE(l.`互转增加金额`, 0)) " +
                   "                 FROM `诉讼表` l " +
                   "                 WHERE l.`年份` = :year AND l.`月份` <= :month), 0) AS litigationAmount, " +
                   "       COALESCE((SELECT SUM(n.`本月新增债权` + COALESCE(n.`互转增加金额`, 0)) " +
                   "                 FROM `非诉讼表` n " +
                   "                 WHERE n.`年份` = :year AND n.`月份` <= :month), 0) AS nonLitigationAmount, " +
                   "       COALESCE((SELECT SUM(i.`本月新增债权`) " +
                   "                 FROM `减值准备表` i " +
                   "                 WHERE i.`年份` = :year AND i.`月份` <= :month), 0) AS impairmentAmount " +
                   "FROM `新增表` a " +
                   "WHERE a.`年份` = :year",
           nativeQuery = true)
    Map<String, Object> getYearToDateNewAmountSummary(@Param("year") int year, @Param("month") int month);

    /**
     * 获取处置金额汇总数据
     *
     * @param year 年份
     * @return 各表处置金额汇总
     */
    @Query(value =
                   "SELECT 'disposedAmount' as checkType, " +
                   "       SUM(a.处置金额) as addTableAmount, " +
                   "       (SELECT SUM(l.本月处置债权 + COALESCE(l.互转减少金额, 0)) FROM 诉讼表 l WHERE l.年份 = :year AND l.月份 = :month) as litigationAmount, " +
                   "       (SELECT SUM(n.本月处置债权 + COALESCE(n.互转减少金额, 0)) FROM 非诉讼表 n WHERE n.年份 = :year AND n.月份 = :month) as nonLitigationAmount, " +
                   "       (SELECT SUM(i.本月处置债权) FROM 减值准备表 i WHERE i.年份 = :year AND i.月份 = :month) as impairmentAmount " +
                   "FROM 新增表 a " +
                   "WHERE a.年份 = :year",
           nativeQuery = true)
    Map<String, Object> getDisposedAmountSummary(@Param("year") int year, @Param("month") int month);

    /**
     * 获取年初至指定月的累计处置金额汇总
     */
    @Query(value =
                   "SELECT 'yearDisposedAmount' AS checkType,  " +
                   "       COALESCE(SUM(a.`处置金额`), 0) AS addTableAmount, " +
                   "       COALESCE((SELECT SUM(l.`本月处置债权` + COALESCE(l.`互转减少金额`, 0)) " +
                   "                 FROM `诉讼表` l " +
                   "                 WHERE l.`年份` = :year AND l.`月份` <= :month), 0) AS litigationAmount, " +
                   "       COALESCE((SELECT SUM(n.`本月处置债权` + COALESCE(n.`互转减少金额`, 0)) " +
                   "                 FROM `非诉讼表` n " +
                   "                 WHERE n.`年份` = :year AND n.`月份` <= :month), 0) AS nonLitigationAmount, " +
                   "       COALESCE((SELECT SUM(i.`本月处置债权`) " +
                   "                 FROM `减值准备表` i " +
                   "                 WHERE i.`年份` = :year AND i.`月份` <= :month), 0) AS impairmentAmount " +
                   "FROM `新增表` a " +
                   "WHERE a.`年份` = :year",
           nativeQuery = true)
    Map<String, Object> getYearToDateDisposedAmountSummary(@Param("year") int year, @Param("month") int month);

    /**
     * 获取期末余额汇总数据
     *
     * @param year  年份
     * @param month 月份
     * @return 各表期末余额汇总
     */
    @Query(value =
                   "SELECT 'endingBalance' as checkType, " +
                   "       SUM(a.债权余额) as addTableAmount, " +
                   "       (SELECT SUM(l.本月末债权余额) FROM 诉讼表 l WHERE l.年份 = :year AND l.月份 = :month) as litigationAmount, " +
                   "       (SELECT SUM(n.本月末本金) FROM 非诉讼表 n WHERE n.年份 = :year AND n.月份 = :month) as nonLitigationAmount, " +
                   "       (SELECT SUM(i.本月末债权余额) FROM 减值准备表 i WHERE i.年份 = :year AND i.月份 = :month) as impairmentAmount " +
                   "FROM 新增表 a " +
                   "WHERE a.年份 = :year",
           nativeQuery = true)
    Map<String, Object> getEndingBalanceSummary(@Param("year") int year, @Param("month") int month);

    /**
     * 获取新增金额明细数据
     *
     * @param year 年份
     * @return 各表新增金额明细
     */
    @Query(value =
                   "WITH combined_data AS (" +
                   "    SELECT " +
                   "        a.债务人 as debtor, " +
                   "        a.债权人 as creditor, " +
                   "        a.是否涉诉 as isLitigation, " +
                   "        a.新增金额 as addTableAmount, " +
                   "        NULL as litigationAmount, " +
                   "        NULL as nonLitigationAmount, " +
                   "        NULL as impairmentAmount " +
                   "    FROM 新增表 a " +
                   "    WHERE a.年份 = :year " +
                   "    UNION ALL " +
                   "    SELECT " +
                   "        l.债务人 as debtor, " +
                   "        l.债权人 as creditor, " +
                   "        '是' as isLitigation, " +
                   "        NULL as addTableAmount, " +
                   "        l.本月新增债权 as litigationAmount, " +
                   "        NULL as nonLitigationAmount, " +
                   "        NULL as impairmentAmount " +
                   "    FROM 诉讼表 l " +
                   "    WHERE l.年份 = :year " +
                   "    UNION ALL " +
                   "    SELECT " +
                   "        n.债务人 as debtor, " +
                   "        n.债权人 as creditor, " +
                   "        '否' as isLitigation, " +
                   "        NULL as addTableAmount, " +
                   "        NULL as litigationAmount, " +
                   "        n.本月新增债权 as nonLitigationAmount, " +
                   "        NULL as impairmentAmount " +
                   "    FROM 非诉讼表 n " +
                   "    WHERE n.年份 = :year " +
                   "    UNION ALL " +
                   "    SELECT " +
                   "        i.债务人 as debtor, " +
                   "        i.债权人 as creditor, " +
                   "        i.是否涉诉 as isLitigation, " +
                   "        NULL as addTableAmount, " +
                   "        NULL as litigationAmount, " +
                   "        NULL as nonLitigationAmount, " +
                   "        i.本月新增债权 as impairmentAmount " +
                   "    FROM 减值准备表 i " +
                   "    WHERE i.年份 = :year " +
                   ") " +
                   "SELECT " +
                   "    debtor, " +
                   "    creditor, " +
                   "    isLitigation, " +
                   "    MAX(addTableAmount) as addTableAmount, " +
                   "    MAX(litigationAmount) as litigationAmount, " +
                   "    MAX(nonLitigationAmount) as nonLitigationAmount, " +
                   "    MAX(impairmentAmount) as impairmentAmount, " +
                   "    GREATEST( " +
                   "        COALESCE(ABS(MAX(addTableAmount) - MAX(litigationAmount)), 0), " +
                   "        COALESCE(ABS(MAX(addTableAmount) - MAX(nonLitigationAmount)), 0), " +
                   "        COALESCE(ABS(MAX(addTableAmount) - MAX(impairmentAmount)), 0), " +
                   "        COALESCE(ABS(MAX(litigationAmount) - MAX(nonLitigationAmount)), 0), " +
                   "        COALESCE(ABS(MAX(litigationAmount) - MAX(impairmentAmount)), 0), " +
                   "        COALESCE(ABS(MAX(nonLitigationAmount) - MAX(impairmentAmount)), 0) " +
                   "    ) as maxDifference " +
                   "FROM combined_data " +
                   "GROUP BY debtor, creditor, isLitigation " +
                   "HAVING maxDifference > 0.01 " +
                   "ORDER BY maxDifference DESC",
           nativeQuery = true)
    List<Map<String, Object>> getNewAmountDetail(@Param("year") int year);

    /**
     * 获取处置金额明细数据
     *
     * @param year 年份
     * @return 各表处置金额明细
     */
    @Query(value =
                   "WITH combined_data AS (" +
                   "    SELECT " +
                   "        a.债务人 as debtor, " +
                   "        a.债权人 as creditor, " +
                   "        a.是否涉诉 as isLitigation, " +
                   "        (a.现金处置 + a.分期还款 + a.资产抵债 + a.其他方式) as addTableAmount, " +
                   "        NULL as litigationAmount, " +
                   "        NULL as nonLitigationAmount, " +
                   "        NULL as impairmentAmount " +
                   "    FROM 新增表 a " +
                   "    WHERE a.年份 = :year " +
                   "    UNION ALL " +
                   "    SELECT " +
                   "        l.债务人 as debtor, " +
                   "        l.债权人 as creditor, " +
                   "        '是' as isLitigation, " +
                   "        NULL as addTableAmount, " +
                   "        l.本月处置债权 as litigationAmount, " +
                   "        NULL as nonLitigationAmount, " +
                   "        NULL as impairmentAmount " +
                   "    FROM 诉讼表 l " +
                   "    WHERE l.年份 = :year " +
                   "    UNION ALL " +
                   "    SELECT " +
                   "        n.债务人 as debtor, " +
                   "        n.债权人 as creditor, " +
                   "        '否' as isLitigation, " +
                   "        NULL as addTableAmount, " +
                   "        NULL as litigationAmount, " +
                   "        n.本月处置债权 as nonLitigationAmount, " +
                   "        NULL as impairmentAmount " +
                   "    FROM 非诉讼表 n " +
                   "    WHERE n.年份 = :year " +
                   "    UNION ALL " +
                   "    SELECT " +
                   "        i.债务人 as debtor, " +
                   "        i.债权人 as creditor, " +
                   "        i.是否涉诉 as isLitigation, " +
                   "        NULL as addTableAmount, " +
                   "        NULL as litigationAmount, " +
                   "        NULL as nonLitigationAmount, " +
                   "        i.本月处置债权 as impairmentAmount " +
                   "    FROM 减值准备表 i " +
                   "    WHERE i.年份 = :year " +
                   ") " +
                   "SELECT " +
                   "    debtor, " +
                   "    creditor, " +
                   "    isLitigation, " +
                   "    MAX(addTableAmount) as addTableAmount, " +
                   "    MAX(litigationAmount) as litigationAmount, " +
                   "    MAX(nonLitigationAmount) as nonLitigationAmount, " +
                   "    MAX(impairmentAmount) as impairmentAmount, " +
                   "    GREATEST( " +
                   "        COALESCE(ABS(MAX(addTableAmount) - MAX(litigationAmount)), 0), " +
                   "        COALESCE(ABS(MAX(addTableAmount) - MAX(nonLitigationAmount)), 0), " +
                   "        COALESCE(ABS(MAX(addTableAmount) - MAX(impairmentAmount)), 0), " +
                   "        COALESCE(ABS(MAX(litigationAmount) - MAX(nonLitigationAmount)), 0), " +
                   "        COALESCE(ABS(MAX(litigationAmount) - MAX(impairmentAmount)), 0), " +
                   "        COALESCE(ABS(MAX(nonLitigationAmount) - MAX(impairmentAmount)), 0) " +
                   "    ) as maxDifference " +
                   "FROM combined_data " +
                   "GROUP BY debtor, creditor, isLitigation " +
                   "HAVING maxDifference > 0.01 " +
                   "ORDER BY maxDifference DESC",
           nativeQuery = true)
    List<Map<String, Object>> getDisposedAmountDetail(@Param("year") int year);

    /**
     * 获取期末余额明细数据
     *
     * @param year  年份
     * @param month 月份
     * @return 各表期末余额明细
     */
    @Query(value =
                   "WITH combined_data AS (" +
                   "    SELECT " +
                   "        a.债务人 as debtor, " +
                   "        a.债权人 as creditor, " +
                   "        a.是否涉诉 as isLitigation, " +
                   "        a.债权余额 as addTableAmount, " +
                   "        NULL as litigationAmount, " +
                   "        NULL as nonLitigationAmount, " +
                   "        NULL as impairmentAmount " +
                   "    FROM 新增表 a " +
                   "    WHERE a.年份 = :year " +
                   "    UNION ALL " +
                   "    SELECT " +
                   "        l.债务人 as debtor, " +
                   "        l.债权人 as creditor, " +
                   "        '是' as isLitigation, " +
                   "        NULL as addTableAmount, " +
                   "        l.本月末债权余额 as litigationAmount, " +
                   "        NULL as nonLitigationAmount, " +
                   "        NULL as impairmentAmount " +
                   "    FROM 诉讼表 l " +
                   "    WHERE l.年份 = :year AND l.月份 = :month " +
                   "    UNION ALL " +
                   "    SELECT " +
                   "        n.债务人 as debtor, " +
                   "        n.债权人 as creditor, " +
                   "        '否' as isLitigation, " +
                   "        NULL as addTableAmount, " +
                   "        NULL as litigationAmount, " +
                   "        n.本月末本金 as nonLitigationAmount, " +
                   "        NULL as impairmentAmount " +
                   "    FROM 非诉讼表 n " +
                   "    WHERE n.年份 = :year AND n.月份 = :month " +
                   "    UNION ALL " +
                   "    SELECT " +
                   "        i.债务人 as debtor, " +
                   "        i.债权人 as creditor, " +
                   "        i.是否涉诉 as isLitigation, " +
                   "        NULL as addTableAmount, " +
                   "        NULL as litigationAmount, " +
                   "        NULL as nonLitigationAmount, " +
                   "        i.本月末债权余额 as impairmentAmount " +
                   "    FROM 减值准备表 i " +
                   "    WHERE i.年份 = :year AND i.月份 = :month " +
                   ") " +
                   "SELECT " +
                   "    debtor, " +
                   "    creditor, " +
                   "    isLitigation, " +
                   "    MAX(addTableAmount) as addTableAmount, " +
                   "    MAX(litigationAmount) as litigationAmount, " +
                   "    MAX(nonLitigationAmount) as nonLitigationAmount, " +
                   "    MAX(impairmentAmount) as impairmentAmount, " +
                   "    GREATEST( " +
                   "        COALESCE(ABS(MAX(addTableAmount) - MAX(litigationAmount)), 0), " +
                   "        COALESCE(ABS(MAX(addTableAmount) - MAX(nonLitigationAmount)), 0), " +
                   "        COALESCE(ABS(MAX(addTableAmount) - MAX(impairmentAmount)), 0), " +
                   "        COALESCE(ABS(MAX(litigationAmount) - MAX(nonLitigationAmount)), 0), " +
                   "        COALESCE(ABS(MAX(litigationAmount) - MAX(impairmentAmount)), 0), " +
                   "        COALESCE(ABS(MAX(nonLitigationAmount) - MAX(impairmentAmount)), 0) " +
                   "    ) as maxDifference " +
                   "FROM combined_data " +
                   "GROUP BY debtor, creditor, isLitigation " +
                   "HAVING maxDifference > 0.01 " +
                   "ORDER BY maxDifference DESC",
           nativeQuery = true)
    List<Map<String, Object>> getEndingBalanceDetail(@Param("year") int year, @Param("month") int month);

    /**
     * 获取年初余额汇总数据
     * 实际是获取上一年12月的期末余额作为当前年的年初余额
     *
     * @param year 当前年份
     * @return 各表年初余额汇总
     */
    @Query(value =
                   "SELECT 'initialBalance' as checkType, " +
                   "       (SELECT SUM(a.债权余额) FROM 新增表 a WHERE a.年份 = :year - 1) as addTableAmount, " +
                   "       (SELECT SUM(l.本月末债权余额) FROM 诉讼表 l WHERE l.年份 = :year - 1 AND l.月份 = 12) as litigationAmount, " +
                   "       (SELECT SUM(n.本月末本金) FROM 非诉讼表 n WHERE n.年份 = :year - 1 AND n.月份 = 12) as nonLitigationAmount, " +
                   "       (SELECT SUM(i.本月末债权余额) FROM 减值准备表 i WHERE i.年份 = :year - 1 AND i.月份 = 12) as impairmentAmount " +
                   "FROM dual",
           nativeQuery = true)
    Map<String, Object> getInitialBalanceSummary(@Param("year") int year);
}

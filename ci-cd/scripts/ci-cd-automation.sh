#!/bin/bash

###############################################################################
# 财务管理系统 - CI/CD自动化脚本
# 功能：Git提交后自动构建和部署
###############################################################################

# 配置
SERVER_IP="**********"
SERVER_USER="root"
PROJECT_PATH=$(pwd)
BRANCH="main"

# 颜色
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log() { echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"; }
error() { echo -e "${RED}[ERROR]${NC} $1"; exit 1; }
warning() { echo -e "${YELLOW}[WARN]${NC} $1"; }
info() { echo -e "${BLUE}[INFO]${NC} $1"; }

# Git钩子设置
setup_git_hooks() {
    log "设置Git钩子..."
    
    # 创建post-commit钩子
    cat > .git/hooks/post-commit << 'HOOK'
#!/bin/bash
# 自动部署钩子
echo "检测到代码提交，准备自动部署..."

# 检查是否是主分支
BRANCH=$(git rev-parse --abbrev-ref HEAD)
if [ "$BRANCH" = "main" ] || [ "$BRANCH" = "master" ]; then
    echo "主分支提交，触发自动部署..."
    ./ci-cd/scripts/ci-cd-automation.sh deploy
else
    echo "非主分支提交，跳过自动部署"
fi
HOOK
    
    chmod +x .git/hooks/post-commit
    log "Git钩子设置完成"
}

# 自动化测试
run_tests() {
    log "运行自动化测试..."
    
    # 后端测试
    info "运行后端测试..."
    mvn test || warning "后端测试失败，继续部署..."
    
    # 前端测试
    info "运行前端测试..."
    cd FinancialSystem-web
    npm test -- --watchAll=false || warning "前端测试失败，继续部署..."
    cd ..
    
    log "测试完成"
}

# 构建项目
build_project() {
    log "开始构建项目..."
    
    # 构建后端
    info "构建后端..."
    mvn clean package -DskipTests || error "后端构建失败"
    
    # 构建前端
    info "构建前端..."
    cd FinancialSystem-web
    npm install && npm run build || error "前端构建失败"
    cd ..
    
    log "项目构建完成"
}

# 部署到服务器
deploy_to_server() {
    log "开始部署到服务器 $SERVER_IP..."
    
    # 创建部署包
    info "创建部署包..."
    rm -rf deploy-package
    mkdir -p deploy-package
    
    # 复制后端文件
    cp api-gateway/target/api-gateway-*.jar deploy-package/financial-system.jar
    cp -r api-gateway/src/main/resources/application*.yml deploy-package/
    
    # 复制前端文件
    cp -r FinancialSystem-web/build deploy-package/frontend
    
    # 创建远程部署脚本
    cat > deploy-package/remote-deploy.sh << 'REMOTE'
#!/bin/bash

# 停止旧服务
systemctl stop financial-backend

# 备份
mkdir -p /opt/financial-system/backup
cp /opt/financial-system/financial-system.jar /opt/financial-system/backup/financial-system-$(date +%Y%m%d-%H%M%S).jar

# 部署新版本
cp financial-system.jar /opt/financial-system/
cp application*.yml /opt/financial-system/

# 部署前端
rm -rf /var/www/financial-system/*
cp -r frontend/* /var/www/financial-system/

# 启动服务
systemctl start financial-backend

# 重载Nginx
nginx -s reload

echo "部署完成"
REMOTE
    
    # 上传并执行
    info "上传部署包..."
    scp -r deploy-package/* $SERVER_USER@$SERVER_IP:/tmp/deploy/
    
    info "执行远程部署..."
    ssh $SERVER_USER@$SERVER_IP "
        mkdir -p /tmp/deploy
        cd /tmp/deploy
        chmod +x remote-deploy.sh
        ./remote-deploy.sh
    "
    
    # 清理
    rm -rf deploy-package
    
    log "部署完成"
}

# 回滚功能
rollback() {
    log "执行回滚..."
    
    ssh $SERVER_USER@$SERVER_IP "
        # 获取最新备份
        LATEST_BACKUP=\$(ls -t /opt/financial-system/backup/*.jar | head -1)
        
        if [ -z \"\$LATEST_BACKUP\" ]; then
            echo '没有找到备份文件'
            exit 1
        fi
        
        echo \"回滚到: \$LATEST_BACKUP\"
        
        # 停止服务
        systemctl stop financial-backend
        
        # 恢复备份
        cp \$LATEST_BACKUP /opt/financial-system/financial-system.jar
        
        # 启动服务
        systemctl start financial-backend
        
        echo '回滚完成'
    "
    
    log "回滚完成"
}

# 健康检查
health_check() {
    log "执行健康检查..."
    
    # 等待服务启动
    sleep 10
    
    # 检查后端
    response=$(curl -s -o /dev/null -w "%{http_code}" http://$SERVER_IP:8080/api/health)
    if [ "$response" = "200" ]; then
        info "✅ 后端服务正常"
    else
        warning "❌ 后端服务异常 (HTTP $response)"
        read -p "是否回滚？[y/N]: " confirm
        if [ "$confirm" = "y" ]; then
            rollback
        fi
    fi
    
    # 检查前端
    response=$(curl -s -o /dev/null -w "%{http_code}" http://$SERVER_IP/)
    if [ "$response" = "200" ]; then
        info "✅ 前端服务正常"
    else
        warning "❌ 前端服务异常 (HTTP $response)"
    fi
}

# 监控部署
monitor_deployment() {
    log "监控部署状态..."
    
    # 创建监控脚本
    cat > monitor.sh << 'MONITOR'
#!/bin/bash

while true; do
    clear
    echo "====== 部署监控 ======"
    echo "时间: $(date)"
    echo ""
    
    # 服务状态
    echo "服务状态:"
    ssh root@********** "
        echo -n '  后端: '; systemctl is-active financial-backend
        echo -n '  Nginx: '; systemctl is-active nginx
        echo -n '  MySQL: '; systemctl is-active mysqld
    "
    
    echo ""
    echo "资源使用:"
    ssh root@********** "
        echo '  CPU: ' && top -bn1 | grep 'Cpu(s)' | head -1
        echo '  内存: ' && free -h | grep Mem
        echo '  磁盘: ' && df -h | grep -E '^/dev/'
    "
    
    echo ""
    echo "最近日志:"
    ssh root@********** "journalctl -u financial-backend -n 5 --no-pager"
    
    echo ""
    echo "按 Ctrl+C 退出监控"
    sleep 5
done
MONITOR
    
    chmod +x monitor.sh
    info "监控脚本已创建: ./monitor.sh"
}

# 本地部署配置信息
show_deployment_info() {
    log "显示本地部署配置信息..."
    
    echo ""
    echo "🔧 本地自动部署配置："
    echo "   📍 Git钩子路径: .git/hooks/post-commit"
    echo "   📍 目标服务器: **********"
    echo "   📍 部署脚本: ./ci-cd/deploy/deploy-to-linux.sh"
    echo ""
    echo "🚀 自动部署流程："
    echo "   1. 提交代码到main分支"
    echo "   2. Git钩子自动触发"
    echo "   3. 执行部署脚本"
    echo "   4. 构建并上传到服务器"
    echo "   5. 重启服务完成部署"
    echo ""
    
    log "部署配置信息显示完成"
}

# 主菜单
show_menu() {
    clear
    echo "========================================="
    echo "    财务管理系统 CI/CD 自动化工具"
    echo "========================================="
    echo "1. 设置Git钩子（本地自动部署）"
    echo "2. 运行测试"
    echo "3. 构建项目"
    echo "4. 部署到服务器"
    echo "5. 完整CI/CD流程"
    echo "6. 回滚到上个版本"
    echo "7. 健康检查"
    echo "8. 监控部署"
    echo "9. 查看部署配置"
    echo "0. 退出"
    echo "========================================="
}

# 主函数
main() {
    if [ "$1" = "deploy" ]; then
        # 自动部署模式
        log "自动部署模式启动..."
        run_tests
        build_project
        deploy_to_server
        health_check
        exit 0
    fi
    
    # 交互模式
    while true; do
        show_menu
        read -p "请选择操作 [0-9]: " choice
        
        case $choice in
            1) setup_git_hooks ;;
            2) run_tests ;;
            3) build_project ;;
            4) deploy_to_server ;;
            5) 
                run_tests
                build_project
                deploy_to_server
                health_check
                ;;
            6) rollback ;;
            7) health_check ;;
            8) monitor_deployment ;;
            9) show_deployment_info ;;
            0) 
                log "退出"
                exit 0
                ;;
            *)
                error "无效选项"
                ;;
        esac
        
        echo ""
        read -p "按Enter继续..."
    done
}

# 执行
main "$@"
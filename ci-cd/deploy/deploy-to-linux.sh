#!/bin/bash

###############################################################################
# 财务管理系统 - Linux自动化部署脚本
# 功能：一键部署前后端到Linux服务器（不使用Docker）
###############################################################################

# 配置部分
SERVER_IP="**********"
SERVER_USER="root"
REMOTE_BACKEND_PATH="/opt/financial-system"
REMOTE_FRONTEND_PATH="/var/www/financial-system"
REMOTE_NGINX_CONFIG="/etc/nginx/conf.d/financial-system.conf"
LOCAL_PROJECT_PATH=$(pwd)

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

log_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

# 检查SSH连接
check_ssh_connection() {
    log_info "检查SSH连接到 $SERVER_IP..."
    ssh -o ConnectTimeout=5 $SERVER_USER@$SERVER_IP "echo 'SSH连接成功'" > /dev/null 2>&1
    if [ $? -ne 0 ]; then
        log_error "无法连接到服务器 $SERVER_IP"
    fi
    log_info "SSH连接正常"
}

# 1. 构建前端
build_frontend() {
    log_info "开始构建前端应用..."
    cd $LOCAL_PROJECT_PATH/FinancialSystem-web
    
    # 安装依赖
    log_info "安装前端依赖..."
    npm install || log_error "前端依赖安装失败"
    
    # 构建生产版本
    log_info "构建前端生产版本..."
    npm run build || log_error "前端构建失败"
    
    log_info "前端构建完成"
}

# 2. 构建后端
build_backend() {
    log_info "开始构建后端应用..."
    cd $LOCAL_PROJECT_PATH
    
    # 清理和打包
    log_info "Maven打包后端..."
    mvn clean package -DskipTests || log_error "后端打包失败"
    
    log_info "后端构建完成"
}

# 3. 部署后端
deploy_backend() {
    log_info "部署后端到服务器..."
    
    # 创建远程目录
    ssh $SERVER_USER@$SERVER_IP "mkdir -p $REMOTE_BACKEND_PATH"
    
    # 停止现有服务
    log_info "停止现有后端服务..."
    ssh $SERVER_USER@$SERVER_IP "systemctl stop financial-backend 2>/dev/null || true"
    
    # 上传JAR文件
    log_info "上传JAR文件..."
    scp $LOCAL_PROJECT_PATH/api-gateway/target/api-gateway-*.jar \
        $SERVER_USER@$SERVER_IP:$REMOTE_BACKEND_PATH/financial-system.jar
    
    # 上传配置文件
    log_info "上传配置文件..."
    scp -r $LOCAL_PROJECT_PATH/api-gateway/src/main/resources/application*.yml \
        $SERVER_USER@$SERVER_IP:$REMOTE_BACKEND_PATH/
    
    # 创建systemd服务文件
    log_info "配置systemd服务..."
    ssh $SERVER_USER@$SERVER_IP "cat > /etc/systemd/system/financial-backend.service" << 'EOF'
[Unit]
Description=Financial System Backend Service
After=network.target mysql.service

[Service]
Type=simple
User=root
WorkingDirectory=/opt/financial-system
ExecStart=/usr/bin/java -jar /opt/financial-system/financial-system.jar --spring.config.location=/opt/financial-system/
Restart=on-failure
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=financial-backend

[Install]
WantedBy=multi-user.target
EOF
    
    # 重载systemd并启动服务
    ssh $SERVER_USER@$SERVER_IP "systemctl daemon-reload && \
                                  systemctl enable financial-backend && \
                                  systemctl start financial-backend"
    
    # 等待服务启动
    log_info "等待后端服务启动..."
    sleep 10
    
    # 检查服务状态
    ssh $SERVER_USER@$SERVER_IP "systemctl status financial-backend --no-pager"
    
    log_info "后端部署完成"
}

# 4. 部署前端
deploy_frontend() {
    log_info "部署前端到服务器..."
    
    # 创建前端目录
    ssh $SERVER_USER@$SERVER_IP "mkdir -p $REMOTE_FRONTEND_PATH"
    
    # 上传前端文件
    log_info "上传前端文件..."
    rsync -avz --delete $LOCAL_PROJECT_PATH/FinancialSystem-web/build/ \
        $SERVER_USER@$SERVER_IP:$REMOTE_FRONTEND_PATH/
    
    log_info "前端文件部署完成"
}

# 5. 配置Nginx
configure_nginx() {
    log_info "配置Nginx..."
    
    # 安装Nginx（如果未安装）
    ssh $SERVER_USER@$SERVER_IP "command -v nginx > /dev/null || dnf install -y nginx"
    
    # 创建Nginx配置
    ssh $SERVER_USER@$SERVER_IP "cat > $REMOTE_NGINX_CONFIG" << 'EOF'
server {
    listen 80;
    server_name _;
    
    # 前端静态文件
    root /var/www/financial-system;
    index index.html;
    
    # 前端路由
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://localhost:8080/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 600;
        proxy_send_timeout 600;
        proxy_read_timeout 600;
        send_timeout 600;
    }
    
    # 静态资源缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 1d;
        add_header Cache-Control "public, immutable";
    }
}
EOF
    
    # 测试Nginx配置
    ssh $SERVER_USER@$SERVER_IP "nginx -t"
    
    # 启动或重载Nginx
    ssh $SERVER_USER@$SERVER_IP "systemctl enable nginx && \
                                  systemctl restart nginx"
    
    log_info "Nginx配置完成"
}

# 6. 配置防火墙
configure_firewall() {
    log_info "配置防火墙..."
    
    ssh $SERVER_USER@$SERVER_IP "
        firewall-cmd --permanent --add-service=http
        firewall-cmd --permanent --add-service=https
        firewall-cmd --reload
    " 2>/dev/null || log_warning "防火墙配置失败或未启用"
}

# 7. 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 检查后端API
    log_info "检查后端API..."
    response=$(curl -s -o /dev/null -w "%{http_code}" http://$SERVER_IP:8080/api/health)
    if [ "$response" = "200" ]; then
        log_info "后端API正常 (HTTP $response)"
    else
        log_warning "后端API异常 (HTTP $response)"
    fi
    
    # 检查前端
    log_info "检查前端页面..."
    response=$(curl -s -o /dev/null -w "%{http_code}" http://$SERVER_IP/)
    if [ "$response" = "200" ]; then
        log_info "前端页面正常 (HTTP $response)"
    else
        log_warning "前端页面异常 (HTTP $response)"
    fi
    
    # 显示服务状态
    log_info "服务状态："
    ssh $SERVER_USER@$SERVER_IP "
        echo '后端服务：' && systemctl is-active financial-backend
        echo 'Nginx服务：' && systemctl is-active nginx
        echo 'MySQL服务：' && systemctl is-active mysqld
    "
}

# 8. 创建更新脚本
create_update_script() {
    log_info "创建远程更新脚本..."
    
    ssh $SERVER_USER@$SERVER_IP "cat > /opt/financial-system/update.sh" << 'EOF'
#!/bin/bash
# 快速更新脚本（在服务器上执行）

echo "停止服务..."
systemctl stop financial-backend

echo "备份当前版本..."
cp /opt/financial-system/financial-system.jar /opt/financial-system/financial-system.jar.bak

echo "启动新版本..."
systemctl start financial-backend

echo "检查状态..."
systemctl status financial-backend --no-pager

echo "更新完成！"
echo "如需回滚，执行：mv /opt/financial-system/financial-system.jar.bak /opt/financial-system/financial-system.jar && systemctl restart financial-backend"
EOF
    
    ssh $SERVER_USER@$SERVER_IP "chmod +x /opt/financial-system/update.sh"
    log_info "更新脚本创建完成"
}

# 主执行流程
main() {
    log_info "========================================="
    log_info "财务管理系统自动化部署开始"
    log_info "目标服务器: $SERVER_IP"
    log_info "========================================="
    
    # 检查连接
    check_ssh_connection
    
    # 询问部署选项
    echo ""
    echo "请选择部署选项："
    echo "1) 完整部署（前端+后端）"
    echo "2) 仅部署后端"
    echo "3) 仅部署前端"
    echo "4) 仅更新配置"
    read -p "请输入选项 [1-4]: " option
    
    case $option in
        1)
            build_frontend
            build_backend
            deploy_backend
            deploy_frontend
            configure_nginx
            configure_firewall
            create_update_script
            ;;
        2)
            build_backend
            deploy_backend
            create_update_script
            ;;
        3)
            build_frontend
            deploy_frontend
            configure_nginx
            ;;
        4)
            configure_nginx
            configure_firewall
            ;;
        *)
            log_error "无效选项"
            ;;
    esac
    
    # 健康检查
    health_check
    
    log_info "========================================="
    log_info "部署完成！"
    log_info "访问地址: http://$SERVER_IP"
    log_info "后端API: http://$SERVER_IP:8080/api/"
    log_info "========================================="
}

# 执行主函数
main "$@"
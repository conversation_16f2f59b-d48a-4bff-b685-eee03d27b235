#!/bin/bash
# Deploy-Agent 专用执行器
# 为deploy-agent提供全自动远程Linux部署能力

set -euo pipefail

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 日志函数
log() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

info() {
    echo -e "${CYAN}[INFO]${NC} $1"
}

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# 加载配置
load_config() {
    local config_file="$SCRIPT_DIR/deploy-config.env"
    
    if [ -f "$config_file" ]; then
        source "$config_file"
        log "已加载部署配置文件: $config_file"
        
        # 设置默认值
        REMOTE_USER="${REMOTE_USER:-$DEFAULT_REMOTE_USER}"
        REMOTE_HOST="${REMOTE_HOST:-$DEFAULT_REMOTE_HOST}"
        REMOTE_DIR="${REMOTE_DIR:-$DEFAULT_REMOTE_DIR}"
        SSH_OPTIONS="${SSH_OPTIONS:--o ConnectTimeout=10 -o StrictHostKeyChecking=no}"
        
        return 0
    else
        error "配置文件不存在: $config_file"
        error "请先创建部署配置文件"
        return 1
    fi
}

# 环境检测
check_environment() {
    log "🔍 检测部署环境..."
    
    # 检查必要命令
    local required_commands=("ssh" "rsync" "mvn" "docker")
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            error "缺少必要命令: $cmd"
            return 1
        fi
    done
    
    # 检查SSH连接
    log "验证SSH连接到 $REMOTE_USER@$REMOTE_HOST..."
    if ssh $SSH_OPTIONS "$REMOTE_USER@$REMOTE_HOST" "echo '连接成功'" 2>/dev/null; then
        success "SSH连接验证成功"
    else
        error "无法连接到远程服务器 $REMOTE_HOST"
        error "请检查："
        error "  1. 服务器地址是否正确"
        error "  2. SSH服务是否运行"
        error "  3. 用户名和密码/密钥是否正确"
        error "  4. 网络连接是否正常"
        return 1
    fi
    
    # 检查远程Docker环境
    log "检查远程Docker环境..."
    if ssh $SSH_OPTIONS "$REMOTE_USER@$REMOTE_HOST" "docker --version" &>/dev/null; then
        success "远程Docker环境正常"
    else
        warning "远程Docker环境可能有问题，但继续尝试部署"
    fi
    
    return 0
}

# 选择部署策略
select_deploy_strategy() {
    log "🎯 选择部署策略..."
    
    # 检查是否使用本地镜像
    if [ "${USE_LOCAL_IMAGES:-true}" = "true" ]; then
        DEPLOY_SCRIPT="enhanced-auto-deploy.sh"
        info "选择策略: 本地镜像模式 (适合局域网部署)"
    else
        DEPLOY_SCRIPT="complete-auto-deploy.sh"
        info "选择策略: 完整构建模式 (适合外网部署)"
    fi
    
    # 检查脚本是否存在
    if [ ! -f "$SCRIPT_DIR/$DEPLOY_SCRIPT" ]; then
        error "部署脚本不存在: $DEPLOY_SCRIPT"
        return 1
    fi
    
    success "部署策略: $DEPLOY_SCRIPT"
    return 0
}

# 执行预部署检查
pre_deploy_check() {
    log "📋 执行预部署检查..."
    
    # 检查JAR文件
    local jar_file="$PROJECT_ROOT/api-gateway/target/api-gateway-1.0-SNAPSHOT.jar"
    if [ ! -f "$jar_file" ]; then
        log "JAR文件不存在，开始构建项目..."
        cd "$PROJECT_ROOT"
        if mvn clean package -DskipTests -pl api-gateway -am; then
            success "项目构建成功"
        else
            error "项目构建失败"
            return 1
        fi
    else
        success "JAR文件已存在"
    fi
    
    # 检查前端build目录
    local build_dir="$PROJECT_ROOT/FinancialSystem-web/build"
    if [ ! -d "$build_dir" ]; then
        warning "前端build目录不存在，将在部署时处理"
    else
        success "前端构建文件已存在"
    fi
    
    return 0
}

# 执行部署
execute_deploy() {
    log "🚀 开始执行远程部署..."
    
    cd "$PROJECT_ROOT"
    
    # 设置环境变量
    export REMOTE_USER
    export REMOTE_HOST
    export REMOTE_DIR
    
    # 执行部署脚本
    log "执行部署脚本: $DEPLOY_SCRIPT"
    if bash "$SCRIPT_DIR/$DEPLOY_SCRIPT"; then
        success "部署脚本执行成功"
        return 0
    else
        error "部署脚本执行失败"
        return 1
    fi
}

# 部署后验证
post_deploy_verification() {
    log "🔍 部署后验证..."
    
    # 等待服务启动
    log "等待服务启动完成..."
    sleep 30
    
    # 检查远程服务状态
    log "检查远程服务状态..."
    ssh $SSH_OPTIONS "$REMOTE_USER@$REMOTE_HOST" "
        echo '=== Docker容器状态 ==='
        docker ps --format 'table {{.Names}}\t{{.Status}}\t{{.Ports}}' 2>/dev/null || echo '无法获取容器状态'
        
        echo ''
        echo '=== 服务健康检查 ==='
        if curl -sf http://localhost:8080/actuator/health 2>/dev/null; then
            echo '✅ 后端服务健康'
        else
            echo '❌ 后端服务异常'
        fi
        
        if curl -sf http://localhost/ 2>/dev/null; then
            echo '✅ 前端服务健康'
        else
            echo '❌ 前端服务异常'
        fi
    "
    
    # 显示访问信息
    echo ""
    success "🎉 部署完成！"
    info "📍 访问地址："
    info "   💻 前端应用: http://$REMOTE_HOST/"
    info "   🔧 后端API: http://$REMOTE_HOST:8080/"
    info "   ❤️  健康检查: http://$REMOTE_HOST:8080/actuator/health"
    echo ""
    info "📋 管理命令："
    info "   查看状态: ssh $REMOTE_USER@$REMOTE_HOST 'cd $REMOTE_DIR/current && docker compose ps'"
    info "   查看日志: ssh $REMOTE_USER@$REMOTE_HOST 'cd $REMOTE_DIR/current && docker compose logs -f'"
    info "   重启服务: ssh $REMOTE_USER@$REMOTE_HOST 'cd $REMOTE_DIR/current && docker compose restart'"
    
    return 0
}

# 主函数
main() {
    echo ""
    log "🤖 Deploy-Agent 远程Linux自动部署器"
    log "🎯 目标: 全自动部署FinancialSystem到Linux服务器"
    echo ""
    
    # 执行部署流程
    if load_config && \
       check_environment && \
       select_deploy_strategy && \
       pre_deploy_check && \
       execute_deploy && \
       post_deploy_verification; then
        
        echo ""
        success "🎉 全自动部署成功完成！"
        return 0
    else
        echo ""
        error "❌ 部署过程中发生错误"
        error "请检查错误信息并重试"
        return 1
    fi
}

# 错误处理
handle_error() {
    local exit_code=$1
    local line_number=$2
    error "部署失败 (退出码: $exit_code, 行号: $line_number)"
    
    # 显示故障排除信息
    echo ""
    info "🔧 故障排除建议："
    info "1. 检查网络连接: ping $REMOTE_HOST"
    info "2. 检查SSH连接: ssh $REMOTE_USER@$REMOTE_HOST"
    info "3. 检查远程Docker: ssh $REMOTE_USER@$REMOTE_HOST 'docker --version'"
    info "4. 查看部署日志: tail -f /tmp/deployment-*.log"
    
    exit $exit_code
}

# 设置错误处理
trap 'handle_error $? $LINENO' ERR

# 如果直接运行此脚本
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi
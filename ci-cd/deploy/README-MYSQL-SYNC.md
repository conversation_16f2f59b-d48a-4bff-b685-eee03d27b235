# MySQL数据同步文档索引

本目录包含完整的MySQL数据同步解决方案，基于FinancialSystem项目的实际部署经验。

## 📁 文件说明

### 🛠️ 可执行脚本
- `mysql-sync-simple.sh` ⭐ **推荐** - 简化版同步脚本，适合快速同步
- `mysql-sync-fixed.sh` - 修复版同步脚本，处理了常见问题
- `mysql-sync-setup.sh` - 完整版同步脚本，功能最全面
- `mysql-sync-direct.sh` - 直接同步脚本，用于现有MySQL服务

### 📚 文档资料
- `mysql-sync-troubleshooting.md` 🚨 **重要** - 问题排除指南，记录所有遇到的问题
- `mysql-sync-best-practices.md` ✨ **精华** - 最佳实践指南，智能同步方案
- `README-MYSQL-SYNC.md` (本文件) - 文档索引

## 🚀 快速开始

### 方式1：一键同步（推荐）
```bash
# 简单快速，适合大多数情况
./ci-cd/deploy/mysql-sync-simple.sh
```

### 方式2：智能同步（最佳实践）
```bash
# 基于最佳实践的智能同步，推荐用于生产环境
# 注意：需要先创建 mysql-sync-smart.sh（见最佳实践文档）
./ci-cd/deploy/mysql-sync-smart.sh
```

### 方式3：环境预检 + 手动同步
```bash
# 1. 先做环境检查
./ci-cd/deploy/mysql-sync-precheck.sh

# 2. 根据检查结果选择合适的脚本
# 如果目标环境已有MySQL：./mysql-sync-fixed.sh
# 如果目标环境为空：./mysql-sync-setup.sh
```

## 🔍 故障排除

遇到问题时，请按以下顺序查阅：

1. **查看故障排除指南** 📖
   ```bash
   cat ci-cd/deploy/mysql-sync-troubleshooting.md
   ```

2. **常见问题快速解决** ⚡
   - 端口冲突：检查现有MySQL服务，优先使用
   - mysqldump参数错误：移除--set-gtid-purged参数  
   - SSH连接问题：检查密码和网络连接
   - 密码特殊字符：使用引号包围或转义&字符

3. **实战经验查询** 💡
   ```bash
   # 查看integration-fix-agent的经验总结
   grep -A 10 "MySQL_Sync_Lessons" .claude/agents/specialized/integration-fix-agent.md
   ```

## 📊 同步验证

同步完成后的验证步骤：

```bash
# 1. 检查数据库是否存在
ssh root@10.25.1.85 "docker exec financial-mysql-gr mysql -u root -p'Zlb&198838' -e 'SHOW DATABASES;'"

# 2. 检查表数量
ssh root@10.25.1.85 "docker exec financial-mysql-gr mysql -u root -p'Zlb&198838' -e 'USE overdue_debt_db; SHOW TABLES;'"

# 3. 测试远程连接
mysql -h 10.25.1.85 -u root -p'Zlb&198838' -e 'SHOW DATABASES;'
```

## 🎯 项目配置信息

**源环境（本地）**：
- MySQL 8.0
- 数据库：overdue_debt_db, user_system, kingdee
- 用户：root / Zlb&198838

**目标环境（Linux）**：
- 服务器：10.25.1.85
- 用户：root / Wrkj2025.
- MySQL：Docker容器 financial-mysql-gr

## 📈 版本历史

- **v1.0** (2025-08-15): 基础同步脚本，解决主要同步问题
- **v1.1** (2025-08-15): 增加故障排除指南和最佳实践
- **v1.2** (2025-08-15): 更新integration-fix-agent经验库

## 🔧 维护说明

本套工具由 **integration-fix-agent** 维护，基于实际项目部署经验持续改进。

如需添加新的同步场景或问题解决方案，请更新：
1. 相应的脚本文件
2. `mysql-sync-troubleshooting.md` 故障排除指南  
3. `mysql-sync-best-practices.md` 最佳实践文档
4. `.claude/agents/specialized/integration-fix-agent.md` 经验库

---

💡 **提示**：建议先阅读最佳实践文档，了解智能同步策略，然后根据实际环境选择合适的同步方式。
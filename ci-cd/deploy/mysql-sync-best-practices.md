# MySQL数据同步最佳实践 - Integration-Fix-Agent

基于FinancialSystem项目实际同步经验总结的最佳实践指南。

## 🎯 快速决策树

```
开始同步
    ↓
检查目标环境
    ↓
目标已有MySQL? 
    ├── 是 → 验证数据完整性
    │       ↓
    │   数据完整?
    │       ├── 是 → ✅ 跳过同步
    │       └── 否 → 增量同步
    └── 否 → 全新部署
            ↓
        选择部署方式
            ├── Docker容器 (推荐)
            └── 系统服务安装
```

## ⚡ 快速开始脚本

### 1. 环境预检脚本
```bash
#!/bin/bash
# 文件: mysql-sync-precheck.sh

REMOTE_HOST="**********"
REMOTE_USER="root"
REMOTE_PASSWORD="Wrkj2025."
DB_PASSWORD="Zlb&198838"

echo "🔍 MySQL同步环境预检..."

# 网络连通性
if ! ping -c 2 "$REMOTE_HOST" &>/dev/null; then
    echo "❌ 网络连接失败"
    exit 1
fi
echo "✅ 网络连通正常"

# SSH连接
if ! sshpass -p "$REMOTE_PASSWORD" ssh -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "echo ok" &>/dev/null; then
    echo "❌ SSH连接失败"
    exit 1
fi
echo "✅ SSH连接正常"

# 检查目标MySQL环境
MYSQL_STATUS=$(sshpass -p "$REMOTE_PASSWORD" ssh -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "
    # 检查Docker MySQL
    if docker ps | grep mysql &>/dev/null; then
        container=\$(docker ps | grep mysql | awk '{print \$1}' | head -1)
        if docker exec \$container mysql -u root -p'$DB_PASSWORD' -e 'SELECT 1;' &>/dev/null; then
            echo 'DOCKER_MYSQL_READY'
        else
            echo 'DOCKER_MYSQL_EXISTS'
        fi
    # 检查系统MySQL
    elif systemctl is-active mysqld &>/dev/null || systemctl is-active mysql &>/dev/null; then
        echo 'SYSTEM_MYSQL_RUNNING'
    # 检查端口占用
    elif netstat -tlnp | grep :3306 &>/dev/null; then
        echo 'PORT_3306_OCCUPIED'
    else
        echo 'NO_MYSQL'
    fi
")

case "$MYSQL_STATUS" in
    "DOCKER_MYSQL_READY")
        echo "✅ 发现可用的Docker MySQL环境"
        echo "🎯 建议: 直接使用现有环境，跳过MySQL安装"
        ;;
    "DOCKER_MYSQL_EXISTS")
        echo "⚠️  发现Docker MySQL但连接失败"
        echo "🎯 建议: 检查密码或重置MySQL"
        ;;
    "SYSTEM_MYSQL_RUNNING")
        echo "✅ 发现系统MySQL服务"
        echo "🎯 建议: 使用现有系统MySQL"
        ;;
    "PORT_3306_OCCUPIED")
        echo "⚠️  3306端口被占用但MySQL不可访问"
        echo "🎯 建议: 检查占用进程或使用其他端口"
        ;;
    "NO_MYSQL")
        echo "ℹ️  未发现MySQL服务"
        echo "🎯 建议: 部署新的Docker MySQL容器"
        ;;
esac

echo ""
echo "预检完成，请根据建议选择合适的同步策略"
```

### 2. 智能同步脚本
```bash
#!/bin/bash
# 文件: mysql-sync-smart.sh

set -e

# 配置区域
REMOTE_HOST="**********"
REMOTE_USER="root" 
REMOTE_PASSWORD="Wrkj2025."
DB_PASSWORD="Zlb&198838"
DATABASES=("overdue_debt_db" "user_system")

# 日志函数
log() { echo -e "\033[0;34m[$(date '+%H:%M:%S')]\033[0m $1"; }
success() { echo -e "\033[0;32m[SUCCESS]\033[0m $1"; }
warning() { echo -e "\033[1;33m[WARNING]\033[0m $1"; }
error() { echo -e "\033[0;31m[ERROR]\033[0m $1"; }

# 主同步函数
smart_sync() {
    log "🚀 启动智能MySQL数据同步..."
    
    # 1. 预检
    ./mysql-sync-precheck.sh || exit 1
    
    # 2. 检查目标环境并选择策略
    local sync_strategy=$(detect_sync_strategy)
    log "📋 同步策略: $sync_strategy"
    
    case "$sync_strategy" in
        "SKIP")
            success "🎉 数据已存在且完整，跳过同步"
            return 0
            ;;
        "USE_EXISTING_DOCKER")
            sync_to_existing_docker
            ;;
        "USE_EXISTING_SYSTEM") 
            sync_to_existing_system
            ;;
        "DEPLOY_NEW_DOCKER")
            deploy_new_docker_mysql
            ;;
        *)
            error "未知同步策略: $sync_strategy"
            exit 1
            ;;
    esac
    
    # 3. 验证同步结果
    verify_sync_result
}

# 检测同步策略
detect_sync_strategy() {
    local status=$(sshpass -p "$REMOTE_PASSWORD" ssh -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "
        if docker ps | grep mysql &>/dev/null; then
            container=\$(docker ps | grep mysql | awk '{print \$1}' | head -1)
            if docker exec \$container mysql -u root -p'$DB_PASSWORD' -e 'USE overdue_debt_db; SHOW TABLES;' &>/dev/null; then
                table_count=\$(docker exec \$container mysql -u root -p'$DB_PASSWORD' -e 'USE overdue_debt_db; SHOW TABLES;' 2>/dev/null | wc -l)
                if [ \$table_count -gt 5 ]; then
                    echo 'SKIP'
                else
                    echo 'USE_EXISTING_DOCKER'
                fi
            else
                echo 'USE_EXISTING_DOCKER'
            fi
        else
            echo 'DEPLOY_NEW_DOCKER'
        fi
    ")
    echo "$status"
}

# 同步到现有Docker MySQL
sync_to_existing_docker() {
    log "📦 使用现有Docker MySQL容器..."
    
    # 获取容器名
    local container=$(sshpass -p "$REMOTE_PASSWORD" ssh -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" \
        "docker ps | grep mysql | awk '{print \$1}' | head -1")
    
    # 导出本地数据
    export_local_data
    
    # 传输数据
    transfer_data_files
    
    # 恢复到容器
    sshpass -p "$REMOTE_PASSWORD" ssh -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "
        for db in ${DATABASES[@]}; do
            echo '恢复数据库: \$db'
            docker exec -i $container mysql -u root -p'$DB_PASSWORD' < /tmp/\${db}.sql
            echo '✅ \$db 恢复完成'
        done
    "
    
    success "Docker MySQL数据同步完成"
}

# 部署新Docker MySQL
deploy_new_docker_mysql() {
    log "🆕 部署新的Docker MySQL环境..."
    
    sshpass -p "$REMOTE_PASSWORD" ssh -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "
        # 清理可能的端口占用
        docker stop \$(docker ps -q --filter 'publish=3306') 2>/dev/null || true
        
        # 启动新MySQL容器
        docker run -d \
            --name financial-mysql-sync \
            -p 3306:3306 \
            -e MYSQL_ROOT_PASSWORD=$DB_PASSWORD \
            -v /opt/mysql_data:/var/lib/mysql \
            mysql:8.0 \
            --character-set-server=utf8mb4 \
            --collation-server=utf8mb4_unicode_ci
        
        # 等待启动
        for i in {1..30}; do
            if docker exec financial-mysql-sync mysqladmin ping -h localhost -uroot -p$DB_PASSWORD 2>/dev/null; then
                echo '✅ MySQL启动成功'
                break
            fi
            sleep 2
        done
    "
    
    # 继续数据同步
    sync_to_existing_docker
}

# 导出本地数据
export_local_data() {
    local backup_dir="/tmp/mysql_backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    for db in "${DATABASES[@]}"; do
        if mysql -u root -p"$DB_PASSWORD" -e "USE $db;" 2>/dev/null; then
            log "导出数据库: $db"
            mysqldump -u root -p"$DB_PASSWORD" \
                --single-transaction \
                --default-character-set=utf8mb4 \
                --databases "$db" > "$backup_dir/${db}.sql"
            success "导出完成: $db"
        fi
    done
    
    echo "$backup_dir" > /tmp/mysql_backup_path
}

# 传输数据文件
transfer_data_files() {
    local backup_dir=$(cat /tmp/mysql_backup_path)
    
    for db in "${DATABASES[@]}"; do
        if [ -f "$backup_dir/${db}.sql" ]; then
            log "传输: ${db}.sql"
            sshpass -p "$REMOTE_PASSWORD" scp -o StrictHostKeyChecking=no \
                "$backup_dir/${db}.sql" "$REMOTE_USER@$REMOTE_HOST:/tmp/"
            success "传输完成: ${db}.sql"
        fi
    done
}

# 验证同步结果
verify_sync_result() {
    log "🔍 验证同步结果..."
    
    local verification=$(sshpass -p "$REMOTE_PASSWORD" ssh -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "
        container=\$(docker ps | grep mysql | awk '{print \$1}' | head -1)
        for db in ${DATABASES[@]}; do
            count=\$(docker exec \$container mysql -u root -p'$DB_PASSWORD' -e 'USE \$db; SHOW TABLES;' 2>/dev/null | wc -l)
            if [ \$count -gt 1 ]; then
                echo \"\$db: \$((count-1)) 个表\"
            else
                echo \"\$db: 同步可能失败\"
            fi
        done
    ")
    
    echo "$verification"
    success "同步验证完成"
}

# 清理临时文件
cleanup() {
    local backup_dir=$(cat /tmp/mysql_backup_path 2>/dev/null || echo "")
    if [ -n "$backup_dir" ] && [ -d "$backup_dir" ]; then
        rm -rf "$backup_dir"
        rm -f /tmp/mysql_backup_path
        success "清理完成"
    fi
}

# 主执行
main() {
    trap cleanup EXIT
    smart_sync
    
    echo ""
    success "🎉 MySQL智能同步完成！"
    log "📍 连接信息: $REMOTE_HOST:3306, root/$DB_PASSWORD"
    log "📋 数据库: ${DATABASES[*]}"
}

# 执行主函数
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi
```

## 📚 经验教训总结

### ✅ 成功要素

1. **环境预检优先**
   - 先检查再行动，避免盲目操作
   - 识别现有环境，避免重复部署

2. **容错设计**
   - 每个操作都要有失败处理
   - 提供清晰的错误信息和建议

3. **渐进式验证**
   - 网络 → SSH → MySQL → 数据库 → 表结构
   - 每层验证通过后再进行下一步

4. **智能决策**
   - 根据环境状况选择最优策略
   - 避免不必要的操作

### ⚠️ 常见陷阱

1. **假设环境状态**
   - 不要假设目标环境为空
   - 不要假设服务配置和本地一致

2. **忽略特殊字符**
   - 密码中的特殊字符需要转义
   - 中文数据库名需要特殊处理

3. **缺少验证步骤**
   - 同步后必须验证数据完整性
   - 验证连接可用性

4. **硬编码假设**
   - 不要硬编码容器名或服务名
   - 动态检测和适配环境差异

## 🔧 工具脚本集合

将上述脚本保存为：
- `mysql-sync-precheck.sh` - 环境预检
- `mysql-sync-smart.sh` - 智能同步主脚本
- `mysql-sync-troubleshooting.md` - 故障排除指南

使用方法：
```bash
# 1. 环境检查
./mysql-sync-precheck.sh

# 2. 智能同步
./mysql-sync-smart.sh

# 3. 问题排查（查阅文档）
cat mysql-sync-troubleshooting.md
```

---

**最后更新**: 2025-08-15  
**维护者**: integration-fix-agent  
**版本**: v1.0 - 基于FinancialSystem实战经验
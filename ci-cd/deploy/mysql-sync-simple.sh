#!/bin/bash
# 简化版MySQL数据同步脚本 - integration-fix-agent

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置
REMOTE_USER="root"
REMOTE_HOST="**********"
REMOTE_PASSWORD="Wrkj2025."
DB_PASSWORD="Zlb&198838"
LOCAL_BACKUP_DIR="/tmp/mysql_backup_$(date +%Y%m%d_%H%M%S)"

log "🚀 开始MySQL数据同步..."

# 1. 检查sshpass
if ! command -v sshpass &> /dev/null; then
    log "安装sshpass..."
    if [[ "$OSTYPE" == "darwin"* ]] && command -v brew &> /dev/null; then
        brew install sshpass
    fi
fi

# 2. 创建备份目录
mkdir -p "$LOCAL_BACKUP_DIR"
success "创建备份目录: $LOCAL_BACKUP_DIR"

# 3. 导出数据库
log "📤 导出本地数据库..."
for db in "overdue_debt_db" "user_system"; do
    log "导出数据库: $db"
    if mysql -u root -p"$DB_PASSWORD" -e "USE $db;" 2>/dev/null; then
        mysqldump -u root -p"$DB_PASSWORD" \
            --single-transaction \
            --default-character-set=utf8mb4 \
            --add-drop-database \
            --databases "$db" > "$LOCAL_BACKUP_DIR/${db}.sql"
        
        if [ $? -eq 0 ]; then
            size=$(du -h "$LOCAL_BACKUP_DIR/${db}.sql" | cut -f1)
            success "数据库 $db 导出成功 ($size)"
        else
            error "数据库 $db 导出失败"
            exit 1
        fi
    else
        warning "数据库 $db 不存在，跳过"
    fi
done

# 4. 设置远程MySQL
log "🐧 设置远程MySQL..."
sshpass -p "$REMOTE_PASSWORD" ssh -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" << 'EOF'
# 停止并删除现有MySQL容器
docker stop financial-mysql 2>/dev/null || true
docker rm financial-mysql 2>/dev/null || true

# 创建数据目录
mkdir -p /opt/mysql_data /tmp/mysql_restore

# 启动MySQL容器
echo "启动MySQL容器..."
docker run -d \
    --name financial-mysql \
    -p 3306:3306 \
    -e MYSQL_ROOT_PASSWORD=Zlb\&198838 \
    -v /opt/mysql_data:/var/lib/mysql \
    -v /tmp/mysql_restore:/tmp/restore \
    mysql:8.0 \
    --character-set-server=utf8mb4 \
    --collation-server=utf8mb4_unicode_ci \
    --skip-log-bin

# 等待MySQL启动
echo "等待MySQL启动..."
for i in {1..30}; do
    if docker exec financial-mysql mysqladmin ping -h localhost -uroot -pZlb\&198838 2>/dev/null; then
        echo "✅ MySQL启动成功"
        exit 0
    fi
    sleep 3
done

echo "❌ MySQL启动失败"
docker logs financial-mysql --tail 10
exit 1
EOF

if [ $? -eq 0 ]; then
    success "远程MySQL环境准备完成"
else
    error "远程MySQL环境准备失败"
    exit 1
fi

# 5. 传输数据文件
log "🚚 传输数据文件..."
for db in "overdue_debt_db" "user_system"; do
    if [ -f "$LOCAL_BACKUP_DIR/${db}.sql" ]; then
        log "传输 ${db}.sql..."
        sshpass -p "$REMOTE_PASSWORD" scp -o StrictHostKeyChecking=no \
            "$LOCAL_BACKUP_DIR/${db}.sql" \
            "$REMOTE_USER@$REMOTE_HOST:/tmp/mysql_restore/"
        success "传输完成: ${db}.sql"
    fi
done

# 6. 恢复数据库
log "🔄 恢复数据库..."
sshpass -p "$REMOTE_PASSWORD" ssh -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" << 'EOF'
echo "恢复数据库..."
for db in overdue_debt_db user_system; do
    if [ -f "/tmp/mysql_restore/${db}.sql" ]; then
        echo "恢复: $db"
        docker exec -i financial-mysql mysql -uroot -pZlb\&198838 < "/tmp/mysql_restore/${db}.sql"
        if [ $? -eq 0 ]; then
            echo "✅ $db 恢复成功"
        else
            echo "❌ $db 恢复失败"
        fi
    fi
done

echo "=== 验证结果 ==="
docker exec financial-mysql mysql -uroot -pZlb\&198838 -e "SHOW DATABASES;" | grep -E "(overdue_debt_db|user_system)"

echo "=== 检查表数量 ==="
for db in overdue_debt_db user_system; do
    count=$(docker exec financial-mysql mysql -uroot -pZlb\&198838 -e "USE $db; SHOW TABLES;" 2>/dev/null | wc -l)
    if [ $count -gt 1 ]; then
        echo "$db: $((count-1)) 个表"
    else
        echo "$db: 0 个表"
    fi
done
EOF

success "数据恢复完成"

# 7. 测试连接
log "🔍 测试远程连接..."
if mysql -h "$REMOTE_HOST" -u root -p"$DB_PASSWORD" -e "SHOW DATABASES;" 2>/dev/null | grep -q "overdue_debt_db"; then
    success "远程MySQL连接测试成功"
else
    warning "远程连接测试失败，但数据可能已同步"
fi

# 8. 清理临时文件
rm -rf "$LOCAL_BACKUP_DIR"
success "清理临时文件完成"

echo ""
success "🎉 MySQL数据同步完成！"
echo ""
log "📍 连接信息:"
log "   主机: $REMOTE_HOST:3306"
log "   用户: root"
log "   密码: $DB_PASSWORD"
log "   数据库: overdue_debt_db, user_system"
echo ""
log "📋 测试命令:"
log "   mysql -h $REMOTE_HOST -u root -p'$DB_PASSWORD' -e 'SHOW DATABASES;'"
# FinancialSystem 部署配置文件
# 本配置文件用于管理不同环境的部署参数

# ===============================
# 远程Linux服务器配置
# ===============================

# 默认远程Linux服务器（局域网）
DEFAULT_REMOTE_USER="root"
DEFAULT_REMOTE_HOST="**********"
DEFAULT_REMOTE_PORT="22"
DEFAULT_REMOTE_DIR="/opt/FinancialSystem"

# SSH连接配置
SSH_KEY_PATH=""  # 如果使用SSH密钥，填写密钥路径
SSH_CONNECT_TIMEOUT="10"
SSH_OPTIONS="-o ConnectTimeout=${SSH_CONNECT_TIMEOUT} -o StrictHostKeyChecking=no"

# ===============================
# 部署策略配置
# ===============================

# 部署模式
DEPLOY_MODE="auto"  # auto, manual, rollback
USE_LOCAL_IMAGES="true"  # 使用本地Docker镜像
SKIP_BUILD="false"  # 跳过构建步骤
SKIP_FRONTEND_BUILD="false"  # 跳过前端构建

# 健康检查配置
HEALTH_CHECK_TIMEOUT="300"  # 健康检查超时时间（秒）
HEALTH_CHECK_INTERVAL="10"  # 健康检查间隔（秒）
HEALTH_CHECK_RETRIES="30"   # 健康检查重试次数

# 备份配置
BACKUP_RETENTION_DAYS="7"   # 备份保留天数
AUTO_BACKUP="true"          # 自动备份
BACKUP_DATABASE="true"      # 备份数据库

# ===============================
# 服务配置
# ===============================

# 服务端口
BACKEND_PORT="8080"
FRONTEND_PORT="8044"  # 自定义前端端口
DATABASE_PORT="3306"

# 数据库配置
DB_ROOT_PASSWORD="Zlb&198838"
DB_CHARSET="utf8mb4"
DB_COLLATION="utf8mb4_unicode_ci"

# ===============================
# 多环境支持
# ===============================

# 开发环境
DEV_REMOTE_HOST="**********"
DEV_REMOTE_USER="dev"
DEV_REMOTE_DIR="/opt/FinancialSystem-dev"

# 测试环境
TEST_REMOTE_HOST="**********"
TEST_REMOTE_USER="test"
TEST_REMOTE_DIR="/opt/FinancialSystem-test"

# 生产环境
PROD_REMOTE_HOST="**********"
PROD_REMOTE_USER="prod"
PROD_REMOTE_DIR="/opt/FinancialSystem-prod"

# ===============================
# 通知配置
# ===============================

# 部署通知
ENABLE_NOTIFICATIONS="false"
NOTIFICATION_WEBHOOK=""
NOTIFICATION_EMAIL=""

# 监控配置
ENABLE_MONITORING="true"
MONITORING_INTERVAL="60"  # 监控间隔（秒）

# ===============================
# 高级配置
# ===============================

# 并发控制
MAX_PARALLEL_TASKS="3"
RSYNC_BANDWIDTH_LIMIT=""  # KB/s，空值表示不限制

# 日志配置
LOG_LEVEL="INFO"  # DEBUG, INFO, WARN, ERROR
LOG_RETENTION_DAYS="30"

# 安全配置
VERIFY_HOST_KEY="false"
ALLOW_ROOT_LOGIN="false"
REQUIRE_SUDO="true"

# ===============================
# 使用说明
# ===============================

# 1. 基本使用（使用默认配置）：
#    ./ci-cd/deploy/enhanced-auto-deploy.sh
#
# 2. 指定环境部署：
#    ENV=test ./ci-cd/deploy/enhanced-auto-deploy.sh
#
# 3. 自定义服务器部署：
#    REMOTE_HOST=*********** REMOTE_USER=myuser ./ci-cd/deploy/enhanced-auto-deploy.sh
#
# 4. 使用deploy-agent部署：
#    echo "部署到Linux服务器" | claude
#
# 5. 紧急回滚：
#    DEPLOY_MODE=rollback ./ci-cd/deploy/enhanced-auto-deploy.sh
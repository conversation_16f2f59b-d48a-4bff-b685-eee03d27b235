#!/bin/bash
# SSH免密登录设置脚本

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置
REMOTE_USER="root"
REMOTE_HOST="**********"
REMOTE_PASSWORD="Wrkj2025."

log "🔐 设置SSH免密登录到 $REMOTE_USER@$REMOTE_HOST"

# 检查本地SSH密钥
if [ ! -f ~/.ssh/id_rsa ]; then
    log "生成SSH密钥对..."
    ssh-keygen -t rsa -b 4096 -f ~/.ssh/id_rsa -N "" -C "$(whoami)@$(hostname)"
    success "SSH密钥对生成完成"
else
    success "SSH密钥对已存在"
fi

# 测试连接（使用密码）
log "测试SSH连接..."
if sshpass -p "$REMOTE_PASSWORD" ssh -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "echo '连接成功'" 2>/dev/null; then
    success "SSH密码连接成功"
else
    error "SSH密码连接失败，请检查："
    error "  1. 服务器地址: $REMOTE_HOST"
    error "  2. 用户名: $REMOTE_USER"  
    error "  3. 密码是否正确"
    error "  4. SSH服务是否运行"
    exit 1
fi

# 安装sshpass（如果需要）
if ! command -v sshpass &> /dev/null; then
    log "安装sshpass工具..."
    if [[ "$OSTYPE" == "darwin"* ]]; then
        if command -v brew &> /dev/null; then
            brew install sshpass
        else
            error "请先安装Homebrew或手动安装sshpass"
            exit 1
        fi
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        sudo apt-get update && sudo apt-get install -y sshpass
    fi
    success "sshpass安装完成"
fi

# 复制公钥到远程服务器
log "复制SSH公钥到远程服务器..."
if sshpass -p "$REMOTE_PASSWORD" ssh-copy-id -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" 2>/dev/null; then
    success "SSH公钥复制成功"
else
    # 手动复制方式
    log "尝试手动复制公钥..."
    PUB_KEY=$(cat ~/.ssh/id_rsa.pub)
    sshpass -p "$REMOTE_PASSWORD" ssh -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" "
        mkdir -p ~/.ssh
        chmod 700 ~/.ssh
        echo '$PUB_KEY' >> ~/.ssh/authorized_keys
        chmod 600 ~/.ssh/authorized_keys
        sort ~/.ssh/authorized_keys | uniq > ~/.ssh/authorized_keys.tmp
        mv ~/.ssh/authorized_keys.tmp ~/.ssh/authorized_keys
    " 2>/dev/null
    success "SSH公钥手动复制成功"
fi

# 测试免密登录
log "测试SSH免密登录..."
if ssh -o StrictHostKeyChecking=no -o ConnectTimeout=5 "$REMOTE_USER@$REMOTE_HOST" "echo 'SSH免密登录成功'" 2>/dev/null; then
    success "🎉 SSH免密登录设置成功！"
    
    # 显示连接信息
    echo ""
    success "SSH连接信息："
    success "  服务器: $REMOTE_HOST"
    success "  用户: $REMOTE_USER"
    success "  连接命令: ssh $REMOTE_USER@$REMOTE_HOST"
    echo ""
    
    # 测试基本命令
    log "测试远程命令执行..."
    ssh "$REMOTE_USER@$REMOTE_HOST" "
        echo '=== 系统信息 ==='
        uname -a
        echo '=== Docker版本 ==='
        docker --version 2>/dev/null || echo 'Docker未安装'
        echo '=== 磁盘空间 ==='
        df -h / | tail -1
        echo '=== 内存信息 ==='
        free -h | head -2
    "
    
else
    error "SSH免密登录设置失败"
    error "请手动检查SSH配置"
    exit 1
fi

success "🔐 SSH免密登录准备工作完成！"
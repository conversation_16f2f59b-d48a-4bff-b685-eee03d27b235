#!/bin/bash
# 修复版MySQL数据同步脚本 - integration-fix-agent

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置
REMOTE_USER="root"
REMOTE_HOST="**********"
REMOTE_PASSWORD="Wrkj2025."
DB_PASSWORD="Zlb&198838"
LOCAL_BACKUP_DIR="/tmp/mysql_backup_$(date +%Y%m%d_%H%M%S)"

# 1. 检查工具
check_tools() {
    log "🔧 检查必要工具..."
    
    if ! command -v sshpass &> /dev/null; then
        log "安装sshpass..."
        if [[ "$OSTYPE" == "darwin"* ]] && command -v brew &> /dev/null; then
            brew install sshpass
        else
            error "请安装sshpass工具"
            exit 1
        fi
    fi
    success "工具检查完成"
}

# 2. 导出本地数据
export_databases() {
    log "📤 导出本地数据库..."
    mkdir -p "$LOCAL_BACKUP_DIR"
    
    # 导出数据库
    for db in "overdue_debt_db" "user_system"; do
        log "导出数据库: $db"
        if mysql -u root -p"$DB_PASSWORD" -e "USE $db;" 2>/dev/null; then
            mysqldump -u root -p"$DB_PASSWORD" \
                --single-transaction \
                --set-gtid-purged=OFF \
                --default-character-set=utf8mb4 \
                --add-drop-database \
                --databases "$db" > "$LOCAL_BACKUP_DIR/${db}.sql"
            success "数据库 $db 导出成功 ($(du -h "$LOCAL_BACKUP_DIR/${db}.sql" | cut -f1))"
        else
            warning "数据库 $db 不存在，跳过"
        fi
    done
}

# 3. 设置远程MySQL
setup_remote_mysql() {
    log "🐧 设置远程MySQL环境..."
    
    sshpass -p "$REMOTE_PASSWORD" ssh -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" << 'REMOTE_SCRIPT'
        echo "=== 清理现有MySQL ==="
        docker stop financial-mysql 2>/dev/null || true
        docker rm financial-mysql 2>/dev/null || true
        
        echo "=== 创建目录 ==="
        mkdir -p /opt/mysql_data /tmp/mysql_restore
        
        echo "=== 启动MySQL容器 ==="
        docker run -d \
            --name financial-mysql \
            -p 3306:3306 \
            -e MYSQL_ROOT_PASSWORD=Zlb\&198838 \
            -e MYSQL_CHARACTER_SET_SERVER=utf8mb4 \
            -e MYSQL_COLLATION_SERVER=utf8mb4_unicode_ci \
            -v /opt/mysql_data:/var/lib/mysql \
            -v /tmp/mysql_restore:/tmp/restore \
            mysql:8.0 \
            --character-set-server=utf8mb4 \
            --collation-server=utf8mb4_unicode_ci \
            --default-time-zone='+08:00' \
            --skip-log-bin
        
        echo "等待MySQL启动..."
        for i in {1..60}; do
            if docker exec financial-mysql mysqladmin ping -h localhost -uroot -pZlb\&198838 2>/dev/null; then
                echo "✅ MySQL启动成功"
                break
            fi
            if [ $i -eq 60 ]; then
                echo "❌ MySQL启动超时"
                docker logs financial-mysql --tail 10
                exit 1
            fi
            sleep 2
        done
REMOTE_SCRIPT
    
    success "远程MySQL环境准备完成"
}

# 4. 传输和恢复数据
sync_data() {
    log "🚚 同步数据到远程服务器..."
    
    # 传输SQL文件
    for db in "overdue_debt_db" "user_system"; do
        if [ -f "$LOCAL_BACKUP_DIR/${db}.sql" ]; then
            log "传输 ${db}.sql..."
            sshpass -p "$REMOTE_PASSWORD" scp -o StrictHostKeyChecking=no \
                "$LOCAL_BACKUP_DIR/${db}.sql" \
                "$REMOTE_USER@$REMOTE_HOST:/tmp/mysql_restore/"
            success "传输完成: ${db}.sql"
        fi
    done
    
    # 恢复数据库
    log "🔄 恢复数据库..."
    sshpass -p "$REMOTE_PASSWORD" ssh -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" << 'RESTORE_SCRIPT'
        echo "=== 恢复数据库 ==="
        for db in overdue_debt_db user_system; do
            if [ -f "/tmp/mysql_restore/${db}.sql" ]; then
                echo "恢复: $db"
                docker exec -i financial-mysql mysql -uroot -pZlb\&198838 < "/tmp/mysql_restore/${db}.sql"
                if [ $? -eq 0 ]; then
                    echo "✅ $db 恢复成功"
                else
                    echo "❌ $db 恢复失败"
                fi
            fi
        done
        
        echo "=== 验证结果 ==="
        docker exec financial-mysql mysql -uroot -pZlb\&198838 -e "SHOW DATABASES;"
        
        echo "=== 检查表 ==="
        for db in overdue_debt_db user_system; do
            echo "数据库 $db:"
            docker exec financial-mysql mysql -uroot -pZlb\&198838 -e "USE $db; SHOW TABLES;" 2>/dev/null | head -10
        done
RESTORE_SCRIPT
    
    success "数据同步完成"
}

# 5. 验证连接
verify_connection() {
    log "🔍 验证远程数据库连接..."
    
    # 测试远程连接
    if mysql -h "$REMOTE_HOST" -u root -p"$DB_PASSWORD" -e "SHOW DATABASES;" 2>/dev/null; then
        success "远程MySQL连接成功"
        
        # 显示数据库统计
        log "数据库统计:"
        mysql -h "$REMOTE_HOST" -u root -p"$DB_PASSWORD" -e "
            SELECT 
                schema_name as '数据库',
                ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS '大小(MB)'
            FROM information_schema.tables 
            WHERE schema_name IN ('overdue_debt_db', 'user_system')
            GROUP BY schema_name;
        " 2>/dev/null || true
        
    else
        warning "远程连接测试失败，但数据可能已同步成功"
    fi
}

# 主函数
main() {
    log "🚀 MySQL数据同步 - integration-fix-agent修复版"
    log "📍 源: 本地MySQL → 目标: $REMOTE_HOST"
    
    check_tools
    export_databases
    setup_remote_mysql
    sync_data
    verify_connection
    
    echo ""
    success "🎉 MySQL数据同步完成！"
    echo ""
    log "📍 连接信息:"
    log "   主机: $REMOTE_HOST:3306"
    log "   用户: root"
    log "   密码: $DB_PASSWORD"
    log "   数据库: overdue_debt_db, user_system"
    echo ""
    log "📋 测试连接:"
    log "   mysql -h $REMOTE_HOST -u root -p'$DB_PASSWORD' -e 'SHOW DATABASES;'"
    
    # 清理
    rm -rf "$LOCAL_BACKUP_DIR"
    success "清理临时文件完成"
}

# 执行
main "$@"
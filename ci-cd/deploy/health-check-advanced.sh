#!/bin/bash
# 高级健康检查脚本 - 全面监控系统状态

set -euo pipefail

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
if [ -f "$SCRIPT_DIR/deploy-config.env" ]; then
    source "$SCRIPT_DIR/deploy-config.env"
fi

REMOTE_HOST="${REMOTE_HOST:-${DEFAULT_REMOTE_HOST:-localhost}}"
BACKEND_PORT="${BACKEND_PORT:-8080}"
FRONTEND_PORT="${FRONTEND_PORT:-80}"
DATABASE_PORT="${DATABASE_PORT:-3306}"
HEALTH_CHECK_TIMEOUT="${HEALTH_CHECK_TIMEOUT:-300}"
HEALTH_CHECK_INTERVAL="${HEALTH_CHECK_INTERVAL:-10}"

# 健康检查结果
HEALTH_RESULTS=()

# Docker服务检查
check_docker_services() {
    log "🐳 检查Docker服务状态..."
    
    local services_status=0
    
    # 检查Docker是否运行
    if ! docker info >/dev/null 2>&1; then
        error "Docker服务未运行"
        HEALTH_RESULTS+=("Docker:FAILED")
        return 1
    fi
    
    # 检查容器状态
    if command -v docker-compose >/dev/null 2>&1 || docker compose version >/dev/null 2>&1; then
        local containers=$(docker compose ps --format "table {{.Name}}\t{{.Status}}" 2>/dev/null || echo "无法获取容器状态")
        
        if echo "$containers" | grep -q "Up"; then
            success "Docker Compose服务运行中"
            HEALTH_RESULTS+=("Docker:PASSED")
            
            # 显示容器详情
            log "容器状态详情:"
            echo "$containers" | while read -r line; do
                if [[ "$line" =~ "Up" ]]; then
                    echo "  ✅ $line"
                else
                    echo "  ❌ $line"
                    services_status=1
                fi
            done
        else
            warning "部分或全部Docker Compose服务未运行"
            HEALTH_RESULTS+=("Docker:WARNING")
            services_status=1
        fi
    else
        warning "docker-compose不可用，检查单独容器"
        HEALTH_RESULTS+=("Docker:WARNING")
    fi
    
    return $services_status
}

# 数据库连接检查
check_database() {
    log "🗄️ 检查数据库连接..."
    
    local db_status=0
    local max_retries=5
    local retry_count=0
    
    while [ $retry_count -lt $max_retries ]; do
        # 尝试连接MySQL
        if docker exec financial-mysql mysqladmin ping -h localhost -uroot -p"${DB_ROOT_PASSWORD:-Zlb&198838}" >/dev/null 2>&1; then
            success "MySQL服务连接正常"
            
            # 检查数据库存在性
            log "检查数据库..."
            local databases=$(docker exec financial-mysql mysql -uroot -p"${DB_ROOT_PASSWORD:-Zlb&198838}" -e "SHOW DATABASES;" 2>/dev/null | grep -E "(overdue_debt_db|user_system|kingdee)" || echo "")
            
            if [ -n "$databases" ]; then
                success "必要数据库已创建"
                echo "$databases" | while read -r db; do
                    if [ -n "$db" ]; then
                        echo "  ✅ $db"
                    fi
                done
                HEALTH_RESULTS+=("Database:PASSED")
            else
                warning "部分数据库可能缺失"
                HEALTH_RESULTS+=("Database:WARNING")
                db_status=1
            fi
            break
        else
            retry_count=$((retry_count + 1))
            if [ $retry_count -eq $max_retries ]; then
                error "MySQL服务连接失败（重试$max_retries次）"
                HEALTH_RESULTS+=("Database:FAILED")
                db_status=1
            else
                warning "MySQL连接失败，重试中... ($retry_count/$max_retries)"
                sleep 5
            fi
        fi
    done
    
    return $db_status
}

# 后端API检查
check_backend_api() {
    log "🔧 检查后端API服务..."
    
    local api_status=0
    local max_retries=10
    local retry_count=0
    
    while [ $retry_count -lt $max_retries ]; do
        # 健康检查端点
        if curl -sf "http://$REMOTE_HOST:$BACKEND_PORT/actuator/health" >/dev/null 2>&1; then
            success "后端健康检查端点正常"
            
            # 获取健康检查详情
            local health_detail=$(curl -s "http://$REMOTE_HOST:$BACKEND_PORT/actuator/health" 2>/dev/null || echo '{"status":"UNKNOWN"}')
            local status=$(echo "$health_detail" | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
            
            if [ "$status" = "UP" ]; then
                success "后端服务状态: UP"
                HEALTH_RESULTS+=("Backend:PASSED")
            else
                warning "后端服务状态: $status"
                HEALTH_RESULTS+=("Backend:WARNING")
                api_status=1
            fi
            
            # 测试认证API
            if curl -sf "http://$REMOTE_HOST:$BACKEND_PORT/api/auth/test" >/dev/null 2>&1; then
                success "认证API端点可访问"
            else
                warning "认证API端点可能有问题"
                api_status=1
            fi
            
            break
        else
            retry_count=$((retry_count + 1))
            if [ $retry_count -eq $max_retries ]; then
                error "后端API服务不可访问（重试$max_retries次）"
                HEALTH_RESULTS+=("Backend:FAILED")
                api_status=1
            else
                warning "后端API未就绪，等待中... ($retry_count/$max_retries)"
                sleep 10
            fi
        fi
    done
    
    return $api_status
}

# 前端服务检查
check_frontend() {
    log "🌐 检查前端服务..."
    
    local frontend_status=0
    
    # 检查前端HTTP响应
    if curl -sf "http://$REMOTE_HOST:$FRONTEND_PORT/" >/dev/null 2>&1; then
        success "前端服务HTTP响应正常"
        
        # 检查前端内容
        local response=$(curl -s "http://$REMOTE_HOST:$FRONTEND_PORT/" 2>/dev/null || echo "")
        if echo "$response" | grep -q "<title>"; then
            success "前端页面内容正常"
            HEALTH_RESULTS+=("Frontend:PASSED")
        else
            warning "前端页面内容可能异常"
            HEALTH_RESULTS+=("Frontend:WARNING")
            frontend_status=1
        fi
    else
        error "前端服务不可访问"
        HEALTH_RESULTS+=("Frontend:FAILED")
        frontend_status=1
    fi
    
    return $frontend_status
}

# 系统资源检查
check_system_resources() {
    log "📊 检查系统资源..."
    
    local resource_status=0
    
    # 检查磁盘空间
    local disk_usage=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$disk_usage" -lt 80 ]; then
        success "磁盘空间充足 ($disk_usage%)"
    elif [ "$disk_usage" -lt 90 ]; then
        warning "磁盘空间使用较高 ($disk_usage%)"
        resource_status=1
    else
        error "磁盘空间不足 ($disk_usage%)"
        resource_status=1
    fi
    
    # 检查内存使用
    if command -v free >/dev/null 2>&1; then
        local mem_usage=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
        if [ "$mem_usage" -lt 80 ]; then
            success "内存使用正常 ($mem_usage%)"
        elif [ "$mem_usage" -lt 90 ]; then
            warning "内存使用较高 ($mem_usage%)"
            resource_status=1
        else
            error "内存使用过高 ($mem_usage%)"
            resource_status=1
        fi
    fi
    
    # 检查CPU负载（如果支持）
    if command -v uptime >/dev/null 2>&1; then
        local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
        success "系统负载: $load_avg"
    fi
    
    if [ $resource_status -eq 0 ]; then
        HEALTH_RESULTS+=("Resources:PASSED")
    else
        HEALTH_RESULTS+=("Resources:WARNING")
    fi
    
    return $resource_status
}

# 网络连通性检查
check_network() {
    log "🌐 检查网络连通性..."
    
    local network_status=0
    
    # 检查端口监听
    local ports=("$BACKEND_PORT" "$FRONTEND_PORT" "$DATABASE_PORT")
    
    for port in "${ports[@]}"; do
        if ss -tuln 2>/dev/null | grep -q ":$port " || netstat -tuln 2>/dev/null | grep -q ":$port "; then
            success "端口 $port 正在监听"
        else
            warning "端口 $port 未监听"
            network_status=1
        fi
    done
    
    if [ $network_status -eq 0 ]; then
        HEALTH_RESULTS+=("Network:PASSED")
    else
        HEALTH_RESULTS+=("Network:WARNING")
    fi
    
    return $network_status
}

# 日志检查
check_logs() {
    log "📋 检查应用日志..."
    
    local log_status=0
    
    # 检查Docker容器日志中的错误
    if docker compose logs --tail=50 2>/dev/null | grep -i "error\|exception\|failed" | head -5; then
        warning "发现应用日志中有错误信息"
        log_status=1
    else
        success "近期日志未发现明显错误"
    fi
    
    if [ $log_status -eq 0 ]; then
        HEALTH_RESULTS+=("Logs:PASSED")
    else
        HEALTH_RESULTS+=("Logs:WARNING")
    fi
    
    return $log_status
}

# 生成健康报告
generate_health_report() {
    log "📊 生成健康检查报告..."
    
    local report_file="/tmp/health-report-$(date +%Y%m%d-%H%M%S).json"
    
    # 统计结果
    local passed_count=0
    local warning_count=0
    local failed_count=0
    
    for result in "${HEALTH_RESULTS[@]}"; do
        if [[ "$result" =~ :PASSED$ ]]; then
            ((passed_count++))
        elif [[ "$result" =~ :WARNING$ ]]; then
            ((warning_count++))
        elif [[ "$result" =~ :FAILED$ ]]; then
            ((failed_count++))
        fi
    done
    
    # 生成JSON报告
    cat > "$report_file" <<EOF
{
    "timestamp": "$(date -Iseconds)",
    "hostname": "$(hostname)",
    "summary": {
        "total": ${#HEALTH_RESULTS[@]},
        "passed": $passed_count,
        "warning": $warning_count,
        "failed": $failed_count,
        "overall_status": "$([ $failed_count -eq 0 ] && echo "HEALTHY" || echo "UNHEALTHY")"
    },
    "checks": [
$(for result in "${HEALTH_RESULTS[@]}"; do
    component=$(echo "$result" | cut -d: -f1)
    status=$(echo "$result" | cut -d: -f2)
    echo "        {\"component\": \"$component\", \"status\": \"$status\"},"
done | sed '$ s/,$//')
    ],
    "access_urls": {
        "frontend": "http://$REMOTE_HOST:$FRONTEND_PORT/",
        "backend": "http://$REMOTE_HOST:$BACKEND_PORT/",
        "health_check": "http://$REMOTE_HOST:$BACKEND_PORT/actuator/health"
    }
}
EOF
    
    log "健康检查报告已保存到: $report_file"
    
    # 显示总结
    echo ""
    log "🏥 健康检查总结"
    echo "================================"
    echo "总检查项: ${#HEALTH_RESULTS[@]}"
    echo "通过: $passed_count"
    echo "警告: $warning_count"
    echo "失败: $failed_count"
    echo "================================"
    
    # 详细结果
    for result in "${HEALTH_RESULTS[@]}"; do
        component=$(echo "$result" | cut -d: -f1)
        status=$(echo "$result" | cut -d: -f2)
        
        case $status in
            "PASSED")
                echo -e "✅ $component: ${GREEN}通过${NC}"
                ;;
            "WARNING")
                echo -e "⚠️  $component: ${YELLOW}警告${NC}"
                ;;
            "FAILED")
                echo -e "❌ $component: ${RED}失败${NC}"
                ;;
        esac
    done
    
    echo ""
    
    if [ $failed_count -eq 0 ]; then
        if [ $warning_count -eq 0 ]; then
            success "🎉 系统完全健康！"
        else
            warning "⚠️ 系统基本健康，但有一些警告需要关注"
        fi
    else
        error "❌ 系统存在问题，需要立即处理"
    fi
}

# 主健康检查流程
main() {
    log "🏥 开始高级健康检查..."
    echo ""
    
    # 执行各项检查
    local overall_status=0
    
    check_docker_services || overall_status=1
    echo ""
    
    check_database || overall_status=1
    echo ""
    
    check_backend_api || overall_status=1
    echo ""
    
    check_frontend || overall_status=1
    echo ""
    
    check_system_resources || overall_status=1
    echo ""
    
    check_network || overall_status=1
    echo ""
    
    check_logs || overall_status=1
    echo ""
    
    # 生成报告
    generate_health_report
    
    return $overall_status
}

# 执行主流程
main "$@"
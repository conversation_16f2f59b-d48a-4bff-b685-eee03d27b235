#!/bin/bash
# MySQL数据同步设置脚本 - integration-fix-agent专用

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

info() {
    echo -e "${CYAN}[INFO]${NC} $1"
}

# 配置变量
REMOTE_USER="root"
REMOTE_HOST="**********"
REMOTE_PASSWORD="Wrkj2025."
DB_ROOT_PASSWORD="Zlb&198838"
LOCAL_BACKUP_DIR="/tmp/mysql_backup_$(date +%Y%m%d_%H%M%S)"
REMOTE_BACKUP_DIR="/tmp/mysql_restore"

# 数据库列表
DATABASES=("overdue_debt_db" "user_system" "kingdee")

log "🔄 开始MySQL数据同步准备..."

# 1. 创建本地备份目录
mkdir -p "$LOCAL_BACKUP_DIR"
success "创建本地备份目录: $LOCAL_BACKUP_DIR"

# 2. 准备Linux服务器MySQL环境
prepare_remote_mysql() {
    log "🐧 准备Linux服务器MySQL环境..."
    
    # 检查并安装sshpass
    if ! command -v sshpass &> /dev/null; then
        log "安装sshpass..."
        if [[ "$OSTYPE" == "darwin"* ]]; then
            if command -v brew &> /dev/null; then
                brew install sshpass
            else
                error "请先安装Homebrew"
                exit 1
            fi
        fi
    fi
    
    # 连接到远程服务器并准备MySQL
    sshpass -p "$REMOTE_PASSWORD" ssh -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" << 'EOF'
        echo "=== 检查系统环境 ==="
        uname -a
        
        echo "=== 检查Docker环境 ==="
        if ! command -v docker &> /dev/null; then
            echo "安装Docker..."
            curl -fsSL https://get.docker.com -o get-docker.sh
            sh get-docker.sh
            systemctl start docker
            systemctl enable docker
        fi
        docker --version
        
        echo "=== 检查现有MySQL容器 ==="
        if docker ps -a | grep -q mysql; then
            echo "发现现有MySQL容器，停止并删除..."
            docker stop $(docker ps -aq --filter "ancestor=mysql") 2>/dev/null || true
            docker rm $(docker ps -aq --filter "ancestor=mysql") 2>/dev/null || true
        fi
        
        echo "=== 创建MySQL数据目录 ==="
        mkdir -p /opt/mysql_data
        mkdir -p /tmp/mysql_restore
        
        echo "=== 启动MySQL容器 ==="
        docker run -d \
            --name financial-mysql \
            -p 3306:3306 \
            -e MYSQL_ROOT_PASSWORD=Zlb&198838 \
            -e MYSQL_CHARACTER_SET_SERVER=utf8mb4 \
            -e MYSQL_COLLATION_SERVER=utf8mb4_unicode_ci \
            -v /opt/mysql_data:/var/lib/mysql \
            -v /tmp/mysql_restore:/tmp/restore \
            --restart unless-stopped \
            mysql:8.0 \
            --character-set-server=utf8mb4 \
            --collation-server=utf8mb4_unicode_ci \
            --default-time-zone='+08:00'
        
        echo "等待MySQL启动..."
        sleep 30
        
        echo "=== 验证MySQL启动状态 ==="
        docker logs financial-mysql --tail 20
        
        if docker exec financial-mysql mysqladmin ping -h localhost -uroot -pZlb\&198838; then
            echo "✅ MySQL容器启动成功"
        else
            echo "❌ MySQL启动失败"
            exit 1
        fi
EOF
    
    if [ $? -eq 0 ]; then
        success "Linux服务器MySQL环境准备完成"
    else
        error "Linux服务器MySQL环境准备失败"
        exit 1
    fi
}

# 3. 导出本地数据库
export_local_databases() {
    log "📤 导出本地数据库..."
    
    for db in "${DATABASES[@]}"; do
        log "导出数据库: $db"
        
        # 检查数据库是否存在
        if mysql -u root -p"$DB_ROOT_PASSWORD" -e "USE $db;" 2>/dev/null; then
            # 导出结构和数据
            mysqldump -u root -p"$DB_ROOT_PASSWORD" \
                --single-transaction \
                --routines \
                --triggers \
                --set-gtid-purged=OFF \
                --default-character-set=utf8mb4 \
                "$db" > "$LOCAL_BACKUP_DIR/${db}.sql"
            
            if [ $? -eq 0 ]; then
                success "数据库 $db 导出成功"
                info "文件大小: $(du -h "$LOCAL_BACKUP_DIR/${db}.sql" | cut -f1)"
            else
                error "数据库 $db 导出失败"
                exit 1
            fi
        else
            warning "数据库 $db 不存在，跳过"
        fi
    done
}

# 4. 传输数据到Linux服务器
transfer_data_to_remote() {
    log "🚚 传输数据到Linux服务器..."
    
    for db in "${DATABASES[@]}"; do
        if [ -f "$LOCAL_BACKUP_DIR/${db}.sql" ]; then
            log "传输 ${db}.sql..."
            sshpass -p "$REMOTE_PASSWORD" scp -o StrictHostKeyChecking=no \
                "$LOCAL_BACKUP_DIR/${db}.sql" \
                "$REMOTE_USER@$REMOTE_HOST:$REMOTE_BACKUP_DIR/"
            
            if [ $? -eq 0 ]; then
                success "数据库 $db 传输成功"
            else
                error "数据库 $db 传输失败"
                exit 1
            fi
        fi
    done
}

# 5. 在Linux服务器恢复数据库
restore_databases_on_remote() {
    log "🔄 在Linux服务器恢复数据库..."
    
    sshpass -p "$REMOTE_PASSWORD" ssh -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" << EOF
        echo "=== 等待MySQL完全启动 ==="
        sleep 10
        
        echo "=== 创建数据库 ==="
        for db in overdue_debt_db user_system kingdee; do
            echo "创建数据库: \$db"
            docker exec financial-mysql mysql -uroot -pZlb\\&198838 -e "
                CREATE DATABASE IF NOT EXISTS \\\`\$db\\\` 
                CHARACTER SET utf8mb4 
                COLLATE utf8mb4_unicode_ci;
            " 2>/dev/null || echo "数据库 \$db 创建可能失败，但继续..."
        done
        
        echo "=== 恢复数据库数据 ==="
        for db in overdue_debt_db user_system kingdee; do
            if [ -f "$REMOTE_BACKUP_DIR/\${db}.sql" ]; then
                echo "恢复数据库: \$db"
                docker exec -i financial-mysql mysql -uroot -pZlb\\&198838 \$db < "$REMOTE_BACKUP_DIR/\${db}.sql"
                if [ \$? -eq 0 ]; then
                    echo "✅ 数据库 \$db 恢复成功"
                else
                    echo "❌ 数据库 \$db 恢复失败"
                fi
            else
                echo "⚠️  未找到 \$db 的备份文件"
            fi
        done
        
        echo "=== 验证数据库 ==="
        docker exec financial-mysql mysql -uroot -pZlb\\&198838 -e "SHOW DATABASES;"
        
        echo "=== 检查表数量 ==="
        for db in overdue_debt_db user_system kingdee; do
            echo "数据库 \$db 的表:"
            docker exec financial-mysql mysql -uroot -pZlb\\&198838 -e "USE \$db; SHOW TABLES;" 2>/dev/null || echo "无法访问数据库 \$db"
        done
EOF
    
    if [ $? -eq 0 ]; then
        success "数据库恢复完成"
    else
        error "数据库恢复过程中出现错误"
        exit 1
    fi
}

# 6. 验证数据同步
verify_sync() {
    log "🔍 验证数据同步结果..."
    
    # 本地统计
    log "本地数据库统计:"
    for db in "${DATABASES[@]}"; do
        if mysql -u root -p"$DB_ROOT_PASSWORD" -e "USE $db;" 2>/dev/null; then
            tables=$(mysql -u root -p"$DB_ROOT_PASSWORD" -e "USE $db; SHOW TABLES;" 2>/dev/null | wc -l)
            tables=$((tables - 1))  # 减去表头
            echo "  $db: $tables 个表"
        fi
    done
    
    # 远程统计
    log "远程数据库统计:"
    sshpass -p "$REMOTE_PASSWORD" ssh -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" << 'EOF'
        for db in overdue_debt_db user_system kingdee; do
            tables=$(docker exec financial-mysql mysql -uroot -pZlb\&198838 -e "USE $db; SHOW TABLES;" 2>/dev/null | wc -l)
            if [ $tables -gt 1 ]; then
                tables=$((tables - 1))
                echo "  $db: $tables 个表"
            else
                echo "  $db: 0 个表 (可能同步失败)"
            fi
        done
EOF
    
    success "数据同步验证完成"
}

# 主执行流程
main() {
    log "🔄 开始MySQL数据同步 (integration-fix-agent)"
    log "📍 源: 本地MySQL → 目标: $REMOTE_HOST MySQL"
    
    prepare_remote_mysql
    export_local_databases  
    transfer_data_to_remote
    restore_databases_on_remote
    verify_sync
    
    echo ""
    success "🎉 MySQL数据同步完成！"
    info "📍 远程MySQL连接信息:"
    info "   主机: $REMOTE_HOST:3306"
    info "   用户: root"
    info "   密码: $DB_ROOT_PASSWORD"
    info "   数据库: overdue_debt_db, user_system, kingdee"
    echo ""
    info "📋 连接测试命令:"
    info "   mysql -h $REMOTE_HOST -u root -p'$DB_ROOT_PASSWORD' -e 'SHOW DATABASES;'"
    
    # 清理本地临时文件
    log "🧹 清理临时文件..."
    rm -rf "$LOCAL_BACKUP_DIR"
    success "清理完成"
}

# 错误处理
handle_error() {
    local exit_code=$1
    local line_number=$2
    error "数据同步失败 (退出码: $exit_code, 行号: $line_number)"
    
    info "🔧 故障排除建议:"
    info "1. 检查网络连接: ping $REMOTE_HOST"
    info "2. 检查SSH连接: ssh $REMOTE_USER@$REMOTE_HOST"
    info "3. 检查本地MySQL: mysql -u root -p'$DB_ROOT_PASSWORD' -e 'SHOW DATABASES;'"
    info "4. 查看备份文件: ls -la $LOCAL_BACKUP_DIR"
    
    exit $exit_code
}

trap 'handle_error $? $LINENO' ERR

# 执行主函数
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi
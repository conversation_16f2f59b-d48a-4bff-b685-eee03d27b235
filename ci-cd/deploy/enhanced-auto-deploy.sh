#!/bin/bash
# 增强型自动部署脚本 - 包含数据库初始化和完整错误处理

set -euo pipefail

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置 - 自动加载配置文件
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
if [ -f "$SCRIPT_DIR/deploy-config.env" ]; then
    source "$SCRIPT_DIR/deploy-config.env"
    REMOTE_USER="${REMOTE_USER:-$DEFAULT_REMOTE_USER}"
    REMOTE_HOST="${REMOTE_HOST:-$DEFAULT_REMOTE_HOST}"
    REMOTE_DIR="${REMOTE_DIR:-$DEFAULT_REMOTE_DIR}"
else
    # 默认配置（兼容原有配置）
    REMOTE_USER="${REMOTE_USER:-admin}"
    REMOTE_HOST="${REMOTE_HOST:-**********}"
    REMOTE_DIR="${REMOTE_DIR:-/opt/FinancialSystem}"
fi
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
DEPLOYMENT_LOG="/tmp/deployment-$(date +%Y%m%d-%H%M%S).log"

# 错误处理
handle_error() {
    local exit_code=$1
    local line_number=$2
    error "部署失败 (退出码: $exit_code, 行号: $line_number)"
    
    # 尝试回滚
    if [ -n "${BACKUP_NAME:-}" ]; then
        warning "尝试回滚到备份: $BACKUP_NAME"
        ssh $REMOTE_USER@$REMOTE_HOST "cd $REMOTE_DIR && ./ci-cd/deploy/rollback.sh $BACKUP_NAME" || true
    fi
    
    exit $exit_code
}

trap 'handle_error $? $LINENO' ERR

# 主部署流程
main() {
    log "🚀 开始增强型自动部署..."
    
    # 步骤1: 本地构建
    log "📦 步骤1: 本地构建"
    cd "$PROJECT_ROOT"
    
    # 构建后端
    if [ -f "pom.xml" ]; then
        log "构建后端项目..."
        mvn clean package -DskipTests -pl api-gateway -am || {
            error "后端构建失败"
            exit 1
        }
        success "后端构建完成"
    fi
    
    # 构建前端
    if [ -d "FinancialSystem-web" ]; then
        log "构建前端项目..."
        cd FinancialSystem-web
        npm install || {
            error "前端依赖安装失败"
            exit 1
        }
        npm run build || {
            error "前端构建失败"
            exit 1
        }
        cd ..
        success "前端构建完成"
    fi
    
    # 步骤2: 创建服务器备份
    log "📦 步骤2: 创建服务器备份"
    BACKUP_NAME="backup-$(date +%Y%m%d-%H%M%S)"
    ssh $REMOTE_USER@$REMOTE_HOST "
        cd $REMOTE_DIR && 
        mkdir -p backups &&
        tar -czf backups/$BACKUP_NAME.tar.gz current/ || true
    "
    success "服务器备份创建完成: $BACKUP_NAME"
    
    # 步骤3: 同步文件到服务器
    log "📤 步骤3: 同步文件到服务器"
    
    # 创建必要目录
    ssh $REMOTE_USER@$REMOTE_HOST "mkdir -p $REMOTE_DIR/current/{api-gateway/target,FinancialSystem-web/build,init-scripts,ci-cd/deploy}"
    
    # 同步后端JAR
    if [ -f "api-gateway/target/api-gateway-1.0-SNAPSHOT.jar" ]; then
        rsync -avz --progress api-gateway/target/api-gateway-1.0-SNAPSHOT.jar \
            $REMOTE_USER@$REMOTE_HOST:$REMOTE_DIR/current/api-gateway/target/
        success "后端JAR文件同步完成"
    fi
    
    # 同步前端构建文件
    if [ -d "FinancialSystem-web/build" ]; then
        rsync -avz --delete --progress FinancialSystem-web/build/ \
            $REMOTE_USER@$REMOTE_HOST:$REMOTE_DIR/current/FinancialSystem-web/build/
        success "前端文件同步完成"
    fi
    
    # 同步配置文件
    rsync -avz --progress docker-compose.yml docker-compose.local.yml \
        $REMOTE_USER@$REMOTE_HOST:$REMOTE_DIR/current/
    
    # 同步数据库初始化脚本
    if [ -d "init-scripts" ]; then
        rsync -avz --progress init-scripts/ \
            $REMOTE_USER@$REMOTE_HOST:$REMOTE_DIR/current/init-scripts/
        success "数据库初始化脚本同步完成"
    fi
    
    # 同步部署脚本
    rsync -avz --progress ci-cd/deploy/*.sh \
        $REMOTE_USER@$REMOTE_HOST:$REMOTE_DIR/current/ci-cd/deploy/
    
    # 步骤4: 在服务器上执行部署
    log "🔧 步骤4: 在服务器上执行部署"
    ssh $REMOTE_USER@$REMOTE_HOST "
        cd $REMOTE_DIR/current
        
        # 确保脚本可执行
        chmod +x ci-cd/deploy/*.sh
        
        # 检查Docker服务
        if ! docker info >/dev/null 2>&1; then
            echo 'Docker服务未运行'
            exit 1
        fi
        
        # 停止现有服务
        echo '停止现有服务...'
        docker compose down || true
        
        # 清理未使用的网络和卷
        docker network prune -f || true
        
        # 检查必要的Docker镜像
        echo '检查Docker镜像...'
        REQUIRED_IMAGES=(\"mysql:8.0\" \"openjdk:21-jdk\" \"nginx:alpine\")
        for image in \"\${REQUIRED_IMAGES[@]}\"; do
            image_name=\$(echo \$image | cut -d: -f1)
            image_tag=\$(echo \$image | cut -d: -f2)
            if ! docker images | grep -E \"^\$image_name\\s+\$image_tag\"; then
                echo \"警告: 缺少镜像 \$image\"
                # 尝试查找相同基础镜像的其他版本
                if docker images | grep -E \"^\$image_name\\s+\"; then
                    echo \"找到 \$image_name 的其他版本，继续部署\"
                else
                    echo \"错误: 找不到任何 \$image_name 镜像\"
                    exit 1
                fi
            fi
        done
        
        # 启动服务
        echo '启动服务...'
        docker compose up -d
        
        # 等待服务启动
        echo '等待服务启动...'
        sleep 30
        
        # 检查服务状态
        docker compose ps
    "
    
    # 步骤5: 健康检查
    log "🏥 步骤5: 执行健康检查"
    
    # 等待服务完全启动
    sleep 10
    
    # 检查MySQL
    log "检查MySQL服务..."
    if ssh $REMOTE_USER@$REMOTE_HOST "docker exec financial-mysql mysqladmin ping -h localhost -uroot -p'Zlb&198838' 2>/dev/null"; then
        success "MySQL服务正常"
        
        # 验证数据库是否创建
        ssh $REMOTE_USER@$REMOTE_HOST "docker exec financial-mysql mysql -uroot -p'Zlb&198838' -e 'SHOW DATABASES;' 2>/dev/null | grep -E '(逾期债权数据库|user_system|kingdee)'" || {
            warning "数据库未正确创建，手动创建..."
            ssh $REMOTE_USER@$REMOTE_HOST "docker exec financial-mysql mysql -uroot -p'Zlb&198838' < $REMOTE_DIR/current/init-scripts/01-init-databases.sql 2>/dev/null" || true
        }
    else
        error "MySQL服务检查失败"
    fi
    
    # 检查后端
    log "检查后端服务..."
    MAX_RETRIES=10
    RETRY_COUNT=0
    while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
        if ssh $REMOTE_USER@$REMOTE_HOST "curl -sf http://localhost:8080/actuator/health 2>/dev/null"; then
            success "后端服务正常"
            break
        else
            RETRY_COUNT=$((RETRY_COUNT + 1))
            if [ $RETRY_COUNT -eq $MAX_RETRIES ]; then
                error "后端服务健康检查失败"
                # 输出后端日志
                ssh $REMOTE_USER@$REMOTE_HOST "cd $REMOTE_DIR/current && docker compose logs backend --tail 50"
            else
                warning "后端服务未就绪，等待中... ($RETRY_COUNT/$MAX_RETRIES)"
                sleep 10
            fi
        fi
    done
    
    # 检查前端
    log "检查前端服务..."
    if ssh $REMOTE_USER@$REMOTE_HOST "curl -sf http://localhost/ >/dev/null 2>&1"; then
        success "前端服务正常"
    else
        warning "前端服务检查失败，但可能正在启动中"
    fi
    
    # 步骤6: 部署完成
    success "🎉 部署完成！"
    log "📋 访问信息:"
    log "  前端: http://$REMOTE_HOST/"
    log "  后端API: http://$REMOTE_HOST:8080/"
    log "  数据库: $REMOTE_HOST:3306"
    log ""
    log "📝 管理命令:"
    log "  查看服务状态: ssh $REMOTE_USER@$REMOTE_HOST 'cd $REMOTE_DIR/current && docker compose ps'"
    log "  查看日志: ssh $REMOTE_USER@$REMOTE_HOST 'cd $REMOTE_DIR/current && docker compose logs -f'"
    log "  重启服务: ssh $REMOTE_USER@$REMOTE_HOST 'cd $REMOTE_DIR/current && docker compose restart'"
    
    # 保存部署信息
    cat > "$DEPLOYMENT_LOG" <<EOF
部署时间: $(date)
部署版本: $(git rev-parse HEAD 2>/dev/null || echo "unknown")
备份位置: $REMOTE_DIR/backups/$BACKUP_NAME.tar.gz
部署状态: 成功
EOF
    
    log "部署日志已保存到: $DEPLOYMENT_LOG"
}

# 执行主流程
main "$@"
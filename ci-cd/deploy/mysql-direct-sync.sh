#!/bin/bash
# 直接同步到现有MySQL服务 - integration-fix-agent

set -e

GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

log() { echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} $1"; }
success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
error() { echo -e "${RED}[ERROR]${NC} $1"; }

REMOTE_USER="root"
REMOTE_HOST="**********"
REMOTE_PASSWORD="Wrkj2025."
DB_PASSWORD="Zlb&198838"
LOCAL_BACKUP_DIR="/tmp/mysql_backup_$(date +%Y%m%d_%H%M%S)"

log "🚀 直接同步到远程MySQL服务..."

# 1. 导出本地数据
mkdir -p "$LOCAL_BACKUP_DIR"
log "📤 导出本地数据库..."

for db in "overdue_debt_db" "user_system"; do
    log "导出数据库: $db"
    if mysql -u root -p"$DB_PASSWORD" -e "USE $db;" 2>/dev/null; then
        mysqldump -u root -p"$DB_PASSWORD" \
            --single-transaction \
            --default-character-set=utf8mb4 \
            --databases "$db" > "$LOCAL_BACKUP_DIR/${db}.sql"
        
        if [ $? -eq 0 ]; then
            size=$(du -h "$LOCAL_BACKUP_DIR/${db}.sql" | cut -f1)
            success "导出成功: $db ($size)"
        else
            error "导出失败: $db"
            exit 1
        fi
    else
        warning "数据库 $db 不存在，跳过"
    fi
done

# 2. 传输到远程服务器
log "🚚 传输数据文件..."
for db in "overdue_debt_db" "user_system"; do
    if [ -f "$LOCAL_BACKUP_DIR/${db}.sql" ]; then
        sshpass -p "$REMOTE_PASSWORD" scp -o StrictHostKeyChecking=no \
            "$LOCAL_BACKUP_DIR/${db}.sql" \
            "$REMOTE_USER@$REMOTE_HOST:/tmp/"
        success "传输完成: ${db}.sql"
    fi
done

# 3. 在远程服务器恢复数据
log "🔄 在远程服务器恢复数据..."
sshpass -p "$REMOTE_PASSWORD" ssh -o StrictHostKeyChecking=no "$REMOTE_USER@$REMOTE_HOST" << 'REMOTE_RESTORE'

# 停止现有MySQL服务
systemctl stop mysqld 2>/dev/null || systemctl stop mysql 2>/dev/null || true

# 重置MySQL root密码
echo "重置MySQL..."
systemctl set-environment MYSQLD_OPTS="--skip-grant-tables"
systemctl start mysqld 2>/dev/null || systemctl start mysql 2>/dev/null

sleep 5

# 重置密码
mysql -e "FLUSH PRIVILEGES; ALTER USER 'root'@'localhost' IDENTIFIED BY 'Zlb&198838';" 2>/dev/null || \
mysql -e "UPDATE mysql.user SET authentication_string=PASSWORD('Zlb&198838') WHERE User='root'; FLUSH PRIVILEGES;" 2>/dev/null

# 重启MySQL正常模式
systemctl unset-environment MYSQLD_OPTS
systemctl restart mysqld 2>/dev/null || systemctl restart mysql 2>/dev/null

sleep 5

# 恢复数据库
echo "恢复数据库..."
for db in overdue_debt_db user_system; do
    if [ -f "/tmp/${db}.sql" ]; then
        echo "恢复: $db"
        mysql -u root -p'Zlb&198838' < "/tmp/${db}.sql" 2>/dev/null
        if [ $? -eq 0 ]; then
            echo "✅ $db 恢复成功"
        else
            echo "❌ $db 恢复失败"
        fi
    fi
done

# 验证结果
echo "=== 验证数据库 ==="
mysql -u root -p'Zlb&198838' -e "SHOW DATABASES;" 2>/dev/null | grep -E "(overdue_debt_db|user_system)"

echo "=== 检查表 ==="
for db in overdue_debt_db user_system; do
    count=$(mysql -u root -p'Zlb&198838' -e "USE $db; SHOW TABLES;" 2>/dev/null | wc -l)
    if [ $count -gt 1 ]; then
        echo "$db: $((count-1)) 个表"
    else
        echo "$db: 0 个表"
    fi
done

# 允许远程连接
echo "配置远程访问..."
mysql -u root -p'Zlb&198838' -e "
    CREATE USER IF NOT EXISTS 'root'@'%' IDENTIFIED BY 'Zlb&198838';
    GRANT ALL PRIVILEGES ON *.* TO 'root'@'%' WITH GRANT OPTION;
    FLUSH PRIVILEGES;
" 2>/dev/null || true

REMOTE_RESTORE

success "远程数据恢复完成"

# 4. 测试连接
log "🔍 测试远程连接..."
sleep 5
if mysql -h "$REMOTE_HOST" -u root -p"$DB_PASSWORD" -e "SHOW DATABASES;" 2>/dev/null | grep -q "overdue_debt_db"; then
    success "远程MySQL连接成功"
else
    warning "远程连接测试失败，请手动检查"
fi

# 5. 清理
rm -rf "$LOCAL_BACKUP_DIR"
success "清理完成"

echo ""
success "🎉 MySQL数据同步完成！"
echo ""
log "📍 连接信息:"
log "   主机: $REMOTE_HOST:3306"
log "   用户: root"
log "   密码: $DB_PASSWORD"
log "   数据库: overdue_debt_db, user_system"
echo ""
#!/bin/bash
# 快速回滚脚本 - 5分钟内完成回滚

set -euo pipefail

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
if [ -f "$SCRIPT_DIR/deploy-config.env" ]; then
    source "$SCRIPT_DIR/deploy-config.env"
fi

REMOTE_USER="${REMOTE_USER:-${DEFAULT_REMOTE_USER:-root}}"
REMOTE_HOST="${REMOTE_HOST:-${DEFAULT_REMOTE_HOST:-**********}}"
REMOTE_DIR="${REMOTE_DIR:-${DEFAULT_REMOTE_DIR:-/opt/FinancialSystem}}"
BACKUP_NAME="${1:-}"

# 紧急停止标志
EMERGENCY_STOP=false

# 信号处理
handle_interrupt() {
    error "收到中断信号，执行紧急停止..."
    EMERGENCY_STOP=true
    emergency_recovery
    exit 1
}

trap 'handle_interrupt' INT TERM

# 紧急恢复
emergency_recovery() {
    log "🚨 执行紧急恢复程序..."
    
    # 尝试启动任何可用的备份
    ssh $REMOTE_USER@$REMOTE_HOST "
        cd $REMOTE_DIR
        
        # 查找最近的备份
        LATEST_BACKUP=\$(ls -t backups/*.tar.gz 2>/dev/null | head -1)
        
        if [ -n \"\$LATEST_BACKUP\" ]; then
            echo '找到备份: '\$LATEST_BACKUP
            
            # 停止所有服务
            docker compose down 2>/dev/null || true
            
            # 恢复备份
            rm -rf current_emergency_backup 2>/dev/null || true
            mkdir -p current_emergency_backup
            tar -xzf \"\$LATEST_BACKUP\" -C current_emergency_backup
            
            # 启动服务
            cd current_emergency_backup/current
            docker compose up -d 2>/dev/null || true
            
            echo '紧急恢复完成'
        else
            echo '未找到可用备份，请手动介入'
        fi
    " || error "紧急恢复失败，需要人工介入"
}

# 验证备份存在性
verify_backup() {
    local backup_to_check="$1"
    
    log "🔍 验证备份: $backup_to_check"
    
    if ssh $REMOTE_USER@$REMOTE_HOST "[ -f '$REMOTE_DIR/backups/$backup_to_check.tar.gz' ]"; then
        success "备份文件存在"
        
        # 验证备份完整性
        if ssh $REMOTE_USER@$REMOTE_HOST "tar -tzf '$REMOTE_DIR/backups/$backup_to_check.tar.gz' >/dev/null 2>&1"; then
            success "备份文件完整"
            return 0
        else
            error "备份文件损坏"
            return 1
        fi
    else
        error "备份文件不存在: $backup_to_check.tar.gz"
        return 1
    fi
}

# 列出可用备份
list_available_backups() {
    log "📋 可用备份列表:"
    
    ssh $REMOTE_USER@$REMOTE_HOST "
        cd $REMOTE_DIR/backups 2>/dev/null || exit 1
        
        echo '最近的备份:'
        ls -lt *.tar.gz 2>/dev/null | head -10 | while read -r line; do
            filename=\$(echo \"\$line\" | awk '{print \$NF}')
            size=\$(echo \"\$line\" | awk '{print \$5}')
            date=\$(echo \"\$line\" | awk '{print \$6, \$7, \$8}')
            echo \"  \$filename (\$size bytes, \$date)\"
        done
    " || {
        error "无法获取备份列表"
        return 1
    }
}

# 快速回滚主流程
quick_rollback() {
    local target_backup="$1"
    
    log "⚡ 开始快速回滚到: $target_backup"
    
    # 第1步：立即停止当前服务（30秒内）
    log "🛑 步骤1: 停止当前服务"
    ssh $REMOTE_USER@$REMOTE_HOST "
        cd $REMOTE_DIR/current
        timeout 30s docker compose down || {
            echo '强制停止容器...'
            docker stop \$(docker ps -q) 2>/dev/null || true
        }
    " || warning "服务停止可能不完整"
    
    # 第2步：备份当前状态（1分钟内）
    log "💾 步骤2: 备份当前状态"
    local failed_backup="failed-$(date +%Y%m%d-%H%M%S)"
    ssh $REMOTE_USER@$REMOTE_HOST "
        cd $REMOTE_DIR
        timeout 60s tar -czf backups/$failed_backup.tar.gz current/ 2>/dev/null || {
            echo '当前状态备份失败，继续回滚'
        }
    " || warning "当前状态备份失败"
    
    # 第3步：恢复目标备份（2分钟内）
    log "📦 步骤3: 恢复备份"
    ssh $REMOTE_USER@$REMOTE_HOST "
        cd $REMOTE_DIR
        
        # 清理当前目录
        rm -rf current_rollback_temp 2>/dev/null || true
        mv current current_rollback_temp 2>/dev/null || true
        
        # 恢复备份
        mkdir -p current
        timeout 120s tar -xzf backups/$target_backup.tar.gz --strip-components=1 -C current/ || {
            echo '备份恢复失败，尝试恢复原状态'
            rm -rf current 2>/dev/null || true
            mv current_rollback_temp current 2>/dev/null || true
            exit 1
        }
        
        # 清理临时目录
        rm -rf current_rollback_temp 2>/dev/null || true
    " || {
        error "备份恢复失败"
        return 1
    }
    
    # 第4步：启动服务（1.5分钟内）
    log "🚀 步骤4: 启动回滚后的服务"
    ssh $REMOTE_USER@$REMOTE_HOST "
        cd $REMOTE_DIR/current
        
        # 确保脚本可执行
        chmod +x ci-cd/deploy/*.sh 2>/dev/null || true
        
        # 启动服务
        timeout 90s docker compose up -d || {
            echo '服务启动失败，尝试重新拉取镜像'
            docker compose pull 2>/dev/null || true
            docker compose up -d || exit 1
        }
    " || {
        error "服务启动失败"
        return 1
    }
    
    # 第5步：快速健康检查（30秒内）
    log "🏥 步骤5: 快速健康检查"
    local health_status=0
    
    # 等待服务启动
    sleep 10
    
    # 检查MySQL
    if ssh $REMOTE_USER@$REMOTE_HOST "timeout 10s docker exec financial-mysql mysqladmin ping -h localhost -uroot -p'${DB_ROOT_PASSWORD:-Zlb&198838}' 2>/dev/null"; then
        success "MySQL服务正常"
    else
        warning "MySQL服务检查失败"
        health_status=1
    fi
    
    # 检查后端（简化检查）
    local backend_ok=false
    for i in {1..6}; do
        if ssh $REMOTE_USER@$REMOTE_HOST "curl -sf http://localhost:8080/actuator/health 2>/dev/null"; then
            success "后端服务正常"
            backend_ok=true
            break
        else
            warning "后端服务未就绪，等待中... ($i/6)"
            sleep 5
        fi
    done
    
    if ! $backend_ok; then
        warning "后端服务健康检查失败"
        health_status=1
    fi
    
    # 检查前端
    if ssh $REMOTE_USER@$REMOTE_HOST "curl -sf http://localhost/ >/dev/null 2>&1"; then
        success "前端服务正常"
    else
        warning "前端服务检查失败"
        health_status=1
    fi
    
    return $health_status
}

# 智能备份选择
smart_backup_selection() {
    log "🧠 智能备份选择..."
    
    # 获取最近的稳定备份
    local stable_backup=$(ssh $REMOTE_USER@$REMOTE_HOST "
        cd $REMOTE_DIR/backups 2>/dev/null || exit 1
        
        # 查找最近24小时内的备份
        find . -name '*.tar.gz' -mtime -1 -type f | sort -r | head -3 | while read -r backup; do
            backup_name=\$(basename \"\$backup\" .tar.gz)
            echo \"\$backup_name\"
        done | head -1
    " 2>/dev/null)
    
    if [ -n "$stable_backup" ]; then
        log "找到最近的稳定备份: $stable_backup"
        echo "$stable_backup"
    else
        # 如果没有最近的备份，选择最新的备份
        local latest_backup=$(ssh $REMOTE_USER@$REMOTE_HOST "
            cd $REMOTE_DIR/backups 2>/dev/null || exit 1
            ls -t *.tar.gz 2>/dev/null | head -1 | sed 's/.tar.gz$//'
        " 2>/dev/null)
        
        if [ -n "$latest_backup" ]; then
            log "使用最新的备份: $latest_backup"
            echo "$latest_backup"
        else
            error "未找到任何可用备份"
            return 1
        fi
    fi
}

# 主流程
main() {
    log "⚡ 启动快速回滚程序..."
    
    # 检查连接
    if ! ssh $REMOTE_USER@$REMOTE_HOST "echo '连接正常'" 2>/dev/null; then
        error "无法连接到远程服务器 $REMOTE_HOST"
        exit 1
    fi
    
    # 确定目标备份
    local target_backup="$BACKUP_NAME"
    
    if [ -z "$target_backup" ]; then
        log "未指定备份，执行智能选择..."
        target_backup=$(smart_backup_selection) || {
            list_available_backups
            error "请指定要回滚的备份名称"
            echo "用法: $0 <backup-name>"
            exit 1
        }
    fi
    
    # 验证备份
    if ! verify_backup "$target_backup"; then
        list_available_backups
        error "无法验证备份，请选择其他备份"
        exit 1
    fi
    
    # 确认回滚
    warning "即将回滚到备份: $target_backup"
    warning "这将停止当前服务并恢复到指定版本"
    
    if [ "${FORCE_ROLLBACK:-}" != "true" ]; then
        read -p "确认继续回滚？(输入 'yes' 继续): " confirm
        if [ "$confirm" != "yes" ]; then
            log "回滚已取消"
            exit 0
        fi
    fi
    
    # 记录回滚开始时间
    local start_time=$(date +%s)
    
    # 执行快速回滚
    if quick_rollback "$target_backup"; then
        local end_time=$(date +%s)
        local duration=$((end_time - start_time))
        
        success "🎉 快速回滚完成！"
        log "⏱️ 回滚耗时: ${duration}秒"
        log "📋 访问信息:"
        log "  前端: http://$REMOTE_HOST/"
        log "  后端: http://$REMOTE_HOST:8080/"
        
        # 记录回滚信息
        ssh $REMOTE_USER@$REMOTE_HOST "
            echo '$(date): 快速回滚到 $target_backup (耗时: ${duration}秒)' >> $REMOTE_DIR/rollback.log
        " 2>/dev/null || true
        
        exit 0
    else
        error "⚠️ 回滚过程中出现问题"
        warning "系统可能仍在运行，建议执行健康检查"
        log "健康检查命令: ./ci-cd/deploy/health-check-advanced.sh"
        exit 1
    fi
}

# 显示使用帮助
show_help() {
    echo "快速回滚脚本 - 5分钟内完成系统回滚"
    echo ""
    echo "用法:"
    echo "  $0                           # 智能选择最近的稳定备份"
    echo "  $0 <backup-name>             # 回滚到指定备份"
    echo "  $0 --list                    # 列出可用备份"
    echo "  $0 --help                    # 显示此帮助"
    echo ""
    echo "环境变量:"
    echo "  FORCE_ROLLBACK=true          # 跳过确认提示"
    echo "  REMOTE_HOST=<ip>             # 指定远程服务器"
    echo "  REMOTE_USER=<user>           # 指定SSH用户"
    echo ""
    echo "示例:"
    echo "  $0 backup-20240815-143000    # 回滚到指定备份"
    echo "  FORCE_ROLLBACK=true $0       # 强制自动回滚"
}

# 处理命令行参数
case "${1:-}" in
    "--help"|"-h")
        show_help
        exit 0
        ;;
    "--list"|"-l")
        list_available_backups
        exit 0
        ;;
    "")
        main
        ;;
    *)
        BACKUP_NAME="$1"
        main
        ;;
esac
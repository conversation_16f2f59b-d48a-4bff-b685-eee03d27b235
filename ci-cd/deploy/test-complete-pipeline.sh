#!/bin/bash
# 完整的CI/CD管道测试脚本

set -euo pipefail

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
TEST_LOG="/tmp/ci-cd-pipeline-test-$(date +%Y%m%d-%H%M%S).log"

# 测试阶段
test_code_quality() {
    log "🔍 代码质量检查..."
    
    cd "$PROJECT_ROOT"
    
    # 检查后端代码
    if [ -f "pom.xml" ]; then
        log "检查后端代码编译..."
        mvn clean compile -DskipTests || {
            error "后端代码编译失败"
            return 1
        }
        success "后端代码编译通过"
    fi
    
    # 检查前端代码
    if [ -d "FinancialSystem-web" ]; then
        log "检查前端代码..."
        cd FinancialSystem-web
        
        # 检查package.json
        if [ ! -f "package.json" ]; then
            error "package.json不存在"
            return 1
        fi
        
        # 安装依赖（如果需要）
        if [ ! -d "node_modules" ]; then
            log "安装前端依赖..."
            npm install || {
                error "前端依赖安装失败"
                return 1
            }
        fi
        
        # 代码语法检查
        if npm run lint --if-present; then
            success "前端代码语法检查通过"
        else
            warning "前端代码语法检查有警告"
        fi
        
        cd ..
    fi
    
    success "代码质量检查完成"
}

test_build_process() {
    log "🔨 构建过程测试..."
    
    cd "$PROJECT_ROOT"
    
    # 测试后端构建
    if [ -f "pom.xml" ]; then
        log "测试后端构建..."
        mvn clean package -DskipTests -pl api-gateway -am || {
            error "后端构建失败"
            return 1
        }
        
        # 检查JAR文件
        if [ -f "api-gateway/target/api-gateway-1.0-SNAPSHOT.jar" ]; then
            success "后端JAR文件构建成功"
        else
            error "后端JAR文件未找到"
            return 1
        fi
    fi
    
    # 测试前端构建
    if [ -d "FinancialSystem-web" ]; then
        log "测试前端构建..."
        cd FinancialSystem-web
        
        npm run build || {
            error "前端构建失败"
            return 1
        }
        
        # 检查构建文件
        if [ -d "build" ] && [ -f "build/index.html" ]; then
            success "前端构建文件生成成功"
        else
            error "前端构建文件未找到"
            return 1
        fi
        
        cd ..
    fi
    
    success "构建过程测试完成"
}

test_docker_images() {
    log "🐳 Docker镜像测试..."
    
    cd "$PROJECT_ROOT"
    
    # 检查Docker是否可用
    if ! docker info >/dev/null 2>&1; then
        error "Docker服务不可用"
        return 1
    fi
    
    # 测试后端Docker镜像构建
    if [ -f "Dockerfile" ]; then
        log "构建后端Docker镜像..."
        docker build -t financial-backend:test . || {
            error "后端Docker镜像构建失败"
            return 1
        }
        success "后端Docker镜像构建成功"
    fi
    
    # 测试前端Docker镜像构建
    if [ -f "FinancialSystem-web/Dockerfile" ]; then
        log "构建前端Docker镜像..."
        cd FinancialSystem-web
        docker build -t financial-frontend:test . || {
            error "前端Docker镜像构建失败"
            return 1
        }
        success "前端Docker镜像构建成功"
        cd ..
    fi
    
    # 清理测试镜像
    docker rmi financial-backend:test financial-frontend:test 2>/dev/null || true
    
    success "Docker镜像测试完成"
}

test_deployment_scripts() {
    log "📋 部署脚本测试..."
    
    # 检查部署脚本是否存在
    DEPLOY_SCRIPTS=(
        "ci-cd/deploy/enhanced-auto-deploy.sh"
        "ci-cd/deploy/deploy-config.env"
    )
    
    for script in "${DEPLOY_SCRIPTS[@]}"; do
        if [ -f "$PROJECT_ROOT/$script" ]; then
            success "脚本存在: $script"
            
            # 检查脚本权限
            if [ -x "$PROJECT_ROOT/$script" ] || [[ "$script" == *.env ]]; then
                success "脚本权限正确: $script"
            else
                warning "脚本权限需要修正: $script"
                chmod +x "$PROJECT_ROOT/$script"
            fi
        else
            error "脚本缺失: $script"
            return 1
        fi
    done
    
    # 语法检查
    log "检查脚本语法..."
    for script in ci-cd/deploy/*.sh; do
        if [ -f "$script" ]; then
            bash -n "$script" || {
                error "脚本语法错误: $script"
                return 1
            }
        fi
    done
    
    success "部署脚本测试完成"
}

test_configuration() {
    log "⚙️ 配置文件测试..."
    
    # 检查关键配置文件
    CONFIG_FILES=(
        "docker-compose.yml"
        "docker-compose.local.yml"
        "ci-cd/deploy/deploy-config.env"
    )
    
    for config in "${CONFIG_FILES[@]}"; do
        if [ -f "$PROJECT_ROOT/$config" ]; then
            success "配置文件存在: $config"
            
            # 检查YAML语法（如果是YAML文件）
            if [[ "$config" == *.yml ]] || [[ "$config" == *.yaml ]]; then
                if command -v yamllint >/dev/null 2>&1; then
                    yamllint "$PROJECT_ROOT/$config" || warning "YAML语法检查警告: $config"
                else
                    warning "yamllint未安装，跳过YAML语法检查"
                fi
            fi
        else
            error "配置文件缺失: $config"
            return 1
        fi
    done
    
    success "配置文件测试完成"
}

test_startup_sequence() {
    log "🚀 启动序列测试..."
    
    cd "$PROJECT_ROOT"
    
    # 使用test-startup.sh如果存在
    if [ -f "scripts/test-startup.sh" ]; then
        log "运行启动测试脚本..."
        timeout 300s ./scripts/test-startup.sh || {
            warning "启动测试超时或失败"
        }
    else
        log "快速启动验证..."
        
        # 简单的后端启动测试
        if [ -f "api-gateway/target/api-gateway-1.0-SNAPSHOT.jar" ]; then
            log "测试后端JAR启动..."
            timeout 30s java -jar api-gateway/target/api-gateway-1.0-SNAPSHOT.jar --server.port=0 --spring.profiles.active=test 2>/dev/null &
            PID=$!
            sleep 5
            
            if kill -0 $PID 2>/dev/null; then
                success "后端JAR可以正常启动"
                kill $PID 2>/dev/null
            else
                warning "后端JAR启动可能有问题"
            fi
        fi
    fi
    
    success "启动序列测试完成"
}

# 生成测试报告
generate_report() {
    log "📊 生成测试报告..."
    
    cat > "$TEST_LOG" <<EOF
CI/CD管道测试报告
==================
测试时间: $(date)
项目路径: $PROJECT_ROOT
Git分支: $(git branch --show-current 2>/dev/null || echo "unknown")
Git提交: $(git rev-parse HEAD 2>/dev/null || echo "unknown")

测试结果:
---------
EOF

    if [ ${#PASSED_TESTS[@]} -gt 0 ]; then
        echo "通过的测试:" >> "$TEST_LOG"
        for test in "${PASSED_TESTS[@]}"; do
            echo "  ✅ $test" >> "$TEST_LOG"
        done
    fi
    
    if [ ${#FAILED_TESTS[@]} -gt 0 ]; then
        echo "失败的测试:" >> "$TEST_LOG"
        for test in "${FAILED_TESTS[@]}"; do
            echo "  ❌ $test" >> "$TEST_LOG"
        done
    fi
    
    echo "" >> "$TEST_LOG"
    echo "总结:" >> "$TEST_LOG"
    echo "  通过: ${#PASSED_TESTS[@]}" >> "$TEST_LOG"
    echo "  失败: ${#FAILED_TESTS[@]}" >> "$TEST_LOG"
    echo "  总计: $((${#PASSED_TESTS[@]} + ${#FAILED_TESTS[@]}))" >> "$TEST_LOG"
    
    log "测试报告已保存到: $TEST_LOG"
}

# 主测试流程
main() {
    log "🧪 开始CI/CD管道完整测试..."
    
    # 初始化测试结果数组
    PASSED_TESTS=()
    FAILED_TESTS=()
    
    # 执行各项测试
    TESTS=(
        "test_code_quality:代码质量检查"
        "test_build_process:构建过程测试"
        "test_docker_images:Docker镜像测试"
        "test_deployment_scripts:部署脚本测试"
        "test_configuration:配置文件测试"
        "test_startup_sequence:启动序列测试"
    )
    
    for test_item in "${TESTS[@]}"; do
        test_function=$(echo "$test_item" | cut -d: -f1)
        test_name=$(echo "$test_item" | cut -d: -f2)
        
        log "执行测试: $test_name"
        
        if $test_function; then
            PASSED_TESTS+=("$test_name")
        else
            FAILED_TESTS+=("$test_name")
        fi
        
        echo ""
    done
    
    # 生成报告
    generate_report
    
    # 输出总结
    log "🏁 测试完成"
    log "通过: ${#PASSED_TESTS[@]} | 失败: ${#FAILED_TESTS[@]}"
    
    if [ ${#FAILED_TESTS[@]} -eq 0 ]; then
        success "🎉 所有测试通过！CI/CD管道准备就绪"
        return 0
    else
        error "❌ 有测试失败，请检查问题后重试"
        return 1
    fi
}

# 执行主流程
main "$@"
-- ============================================
-- 表3、4、5累计处置金额回滚脚本
-- 功能：将"本年度累计回收"字段恢复到修改前的状态
-- 创建时间：2025-08-12
-- 说明：由于修改前的原始值未备份，此脚本采用两种策略：
--      1. 将所有记录的累计回收设为NULL（最安全）
--      2. 或者基于原始逻辑重新计算（如果知道原始计算规则）
-- ============================================

-- 开始事务
START TRANSACTION;

-- ============================================
-- 方案一：将累计回收字段重置为NULL（推荐）
-- 说明：这是最安全的方式，恢复到数据可能的初始状态
-- ============================================

-- 1. 回滚诉讼表
UPDATE 诉讼表 
SET 本年度累计回收 = NULL
WHERE 年份 >= 2025;

-- 2. 回滚非诉讼表  
UPDATE 非诉讼表
SET 本年度累计回收 = NULL
WHERE 年份 >= 2025;

-- 3. 回滚减值准备表
UPDATE 减值准备表
SET 本年度累计回收 = NULL  
WHERE 年份 >= 2025;

-- ============================================
-- 方案二：基于原始逻辑重新计算（备选）
-- 说明：如果原始数据是通过定时任务计算的，可以使用这个方案
-- 注意：请先注释掉方案一，再使用方案二
-- ============================================

/*
-- 1. 诉讼表 - 使用原始的简单累加逻辑
UPDATE 诉讼表 t1
SET 本年度累计回收 = (
    SELECT COALESCE(SUM(t2.本月处置债权), 0)
    FROM 诉讼表 t2
    WHERE t2.债权人 = t1.债权人
      AND t2.债务人 = t1.债务人
      -- 不使用期间字段分组（原始逻辑可能的问题所在）
      AND t2.年份 = t1.年份
      AND t2.月份 <= t1.月份
)
WHERE t1.年份 >= 2025;

-- 2. 非诉讼表 - 使用原始的简单累加逻辑
UPDATE 非诉讼表 t1
SET 本年度累计回收 = (
    SELECT COALESCE(SUM(t2.本月处置债权), 0)
    FROM 非诉讼表 t2
    WHERE t2.债权人 = t1.债权人
      AND t2.债务人 = t1.债务人
      -- 不使用期间字段分组（原始逻辑可能的问题所在）
      AND t2.年份 = t1.年份
      AND t2.月份 <= t1.月份
)
WHERE t1.年份 >= 2025;

-- 3. 减值准备表 - 使用原始的简单累加逻辑
UPDATE 减值准备表 t1
SET 本年度累计回收 = (
    SELECT COALESCE(SUM(t2.本月处置债权), 0)
    FROM 减值准备表 t2
    WHERE t2.债权人 = t1.债权人
      AND t2.债务人 = t1.债务人
      -- 不使用期间字段分组（原始逻辑可能的问题所在）
      AND t2.年份 = t1.年份
      AND t2.月份 <= t1.月份
)
WHERE t1.年份 >= 2025;
*/

-- ============================================
-- 验证回滚结果
-- ============================================

-- 显示回滚后的统计信息
SELECT 
    '诉讼表' AS 表名,
    COUNT(*) AS 总记录数,
    COUNT(本年度累计回收) AS 有值记录数,
    SUM(本年度累计回收) AS 累计总额
FROM 诉讼表
WHERE 年份 >= 2025
UNION ALL
SELECT 
    '非诉讼表' AS 表名,
    COUNT(*) AS 总记录数,
    COUNT(本年度累计回收) AS 有值记录数,
    SUM(本年度累计回收) AS 累计总额
FROM 非诉讼表
WHERE 年份 >= 2025
UNION ALL
SELECT 
    '减值准备表' AS 表名,
    COUNT(*) AS 总记录数,
    COUNT(本年度累计回收) AS 有值记录数,
    SUM(本年度累计回收) AS 累计总额
FROM 减值准备表
WHERE 年份 >= 2025;

-- 提交事务
COMMIT;

-- ============================================
-- 使用说明：
-- 1. 执行命令：mysql -u root -p'Zlb&198838' -D overdue_debt_db < rollback-table345-cumulative-disposal.sql
-- 2. 默认使用方案一（设为NULL），这是最安全的回滚方式
-- 3. 如需使用方案二，请注释掉方案一的UPDATE语句，取消注释方案二
-- 4. 执行后会显示回滚结果统计
-- ============================================
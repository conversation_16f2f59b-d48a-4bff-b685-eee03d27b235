-- 表3、4、5累计处置金额问题诊断脚本
-- 作者: Claude Code
-- 创建时间: 2025-08-12
-- 用途: 分析表3、4、5累计处置金额计算问题的具体原因

SET @year = 2025;
SET @month = 2;

-- ===========================
-- 1. 基础数据统计
-- ===========================
SELECT '=== 基础数据统计 ===' as 诊断项;

SELECT 
    '诉讼表' as 表名,
    COUNT(*) as 总记录数,
    COUNT(CASE WHEN 本月处置债权 > 0 THEN 1 END) as 有处置记录数,
    COUNT(CASE WHEN 本年度累计回收 > 0 THEN 1 END) as 有累计回收记录数,
    ROUND(SUM(COALESCE(本月处置债权, 0)), 2) as 本月处置总额,
    ROUND(SUM(COALESCE(本年度累计回收, 0)), 2) as 累计回收总额,
    ROUND(SUM(COALESCE(本年度累计回收, 0)) - SUM(COALESCE(本月处置债权, 0)), 2) as 差额
FROM 诉讼表 
WHERE 年份 = @year AND 月份 = @month

UNION ALL

SELECT 
    '非诉讼表' as 表名,
    COUNT(*) as 总记录数,
    COUNT(CASE WHEN 本月处置债权 > 0 THEN 1 END) as 有处置记录数,
    COUNT(CASE WHEN 本年度累计回收 > 0 THEN 1 END) as 有累计回收记录数,
    ROUND(SUM(COALESCE(本月处置债权, 0)), 2) as 本月处置总额,
    ROUND(SUM(COALESCE(本年度累计回收, 0)), 2) as 累计回收总额,
    ROUND(SUM(COALESCE(本年度累计回收, 0)) - SUM(COALESCE(本月处置债权, 0)), 2) as 差额
FROM 非诉讼表 
WHERE 年份 = @year AND 月份 = @month

UNION ALL

SELECT 
    '减值准备表' as 表名,
    COUNT(*) as 总记录数,
    COUNT(CASE WHEN 本月处置债权 > 0 THEN 1 END) as 有处置记录数,
    COUNT(CASE WHEN 本年度累计回收 > 0 THEN 1 END) as 有累计回收记录数,
    ROUND(SUM(COALESCE(本月处置债权, 0)), 2) as 本月处置总额,
    ROUND(SUM(COALESCE(本年度累计回收, 0)), 2) as 累计回收总额,
    ROUND(SUM(COALESCE(本年度累计回收, 0)) - SUM(COALESCE(本月处置债权, 0)), 2) as 差额
FROM 减值准备表 
WHERE 年份 = @year AND 月份 = @month;

-- ===========================
-- 2. 期间字段一致性检查
-- ===========================
SELECT '=== 期间字段一致性检查 ===' as 诊断项;

-- 检查各表中的期间字段值分布
SELECT 
    '诉讼表' as 表名,
    期间,
    COUNT(*) as 记录数,
    SUM(COALESCE(本月处置债权, 0)) as 处置金额总计
FROM 诉讼表 
WHERE 年份 = @year
GROUP BY 期间
ORDER BY 记录数 DESC;

SELECT 
    '非诉讼表' as 表名,
    期间,
    COUNT(*) as 记录数,
    SUM(COALESCE(本月处置债权, 0)) as 处置金额总计
FROM 非诉讼表 
WHERE 年份 = @year
GROUP BY 期间
ORDER BY 记录数 DESC;

SELECT 
    '减值准备表' as 表名,
    期间,
    COUNT(*) as 记录数,
    SUM(COALESCE(本月处置债权, 0)) as 处置金额总计
FROM 减值准备表 
WHERE 年份 = @year
GROUP BY 期间
ORDER BY 记录数 DESC;

-- ===========================
-- 3. 累计计算一致性检查
-- ===========================
SELECT '=== 累计计算一致性检查 ===' as 诊断项;

-- 检查2月份的累计回收是否等于1-2月的本月处置之和（如果存在1月数据）
WITH 累计核验_诉讼表 AS (
    SELECT 
        债权人, 债务人, 期间,
        SUM(CASE WHEN 月份 <= @month THEN COALESCE(本月处置债权, 0) ELSE 0 END) as 应该的累计,
        MAX(CASE WHEN 月份 = @month THEN 本年度累计回收 ELSE 0 END) as 实际的累计,
        ABS(
            SUM(CASE WHEN 月份 <= @month THEN COALESCE(本月处置债权, 0) ELSE 0 END) - 
            MAX(CASE WHEN 月份 = @month THEN COALESCE(本年度累计回收, 0) ELSE 0 END)
        ) as 差额
    FROM 诉讼表 
    WHERE 年份 = @year
    GROUP BY 债权人, 债务人, 期间
    HAVING 差额 > 0.01
)
SELECT 
    '诉讼表异常记录' as 检查项,
    COUNT(*) as 异常记录数,
    SUM(差额) as 总差额
FROM 累计核验_诉讼表;

WITH 累计核验_非诉讼表 AS (
    SELECT 
        债权人, 债务人, 期间,
        SUM(CASE WHEN 月份 <= @month THEN COALESCE(本月处置债权, 0) ELSE 0 END) as 应该的累计,
        MAX(CASE WHEN 月份 = @month THEN 本年度累计回收 ELSE 0 END) as 实际的累计,
        ABS(
            SUM(CASE WHEN 月份 <= @month THEN COALESCE(本月处置债权, 0) ELSE 0 END) - 
            MAX(CASE WHEN 月份 = @month THEN COALESCE(本年度累计回收, 0) ELSE 0 END)
        ) as 差额
    FROM 非诉讼表 
    WHERE 年份 = @year
    GROUP BY 债权人, 债务人, 期间
    HAVING 差额 > 0.01
)
SELECT 
    '非诉讼表异常记录' as 检查项,
    COUNT(*) as 异常记录数,
    SUM(差额) as 总差额
FROM 累计核验_非诉讼表;

WITH 累计核验_减值准备表 AS (
    SELECT 
        债权人, 债务人, 期间, 是否涉诉,
        SUM(CASE WHEN 月份 <= @month THEN COALESCE(本月处置债权, 0) ELSE 0 END) as 应该的累计,
        MAX(CASE WHEN 月份 = @month THEN 本年度累计回收 ELSE 0 END) as 实际的累计,
        ABS(
            SUM(CASE WHEN 月份 <= @month THEN COALESCE(本月处置债权, 0) ELSE 0 END) - 
            MAX(CASE WHEN 月份 = @month THEN COALESCE(本年度累计回收, 0) ELSE 0 END)
        ) as 差额
    FROM 减值准备表 
    WHERE 年份 = @year
    GROUP BY 债权人, 债务人, 期间, 是否涉诉
    HAVING 差额 > 0.01
)
SELECT 
    '减值准备表异常记录' as 检查项,
    COUNT(*) as 异常记录数,
    SUM(差额) as 总差额
FROM 累计核验_减值准备表;

-- ===========================
-- 4. 处置表数据对比检查
-- ===========================
SELECT '=== 处置表数据对比检查 ===' as 诊断项;

-- 检查减值准备表与处置表的处置金额是否一致
SELECT 
    '减值准备表vs处置表' as 对比项,
    COUNT(DISTINCT CONCAT(a.债权人, '|', a.债务人, '|', a.期间)) as 减值准备表记录数,
    COUNT(DISTINCT CONCAT(b.债权人, '|', b.债务人, '|', b.期间)) as 处置表记录数,
    COUNT(CASE WHEN ABS(COALESCE(a.本月处置债权, 0) - COALESCE(b.每月处置金额, 0)) > 0.01 THEN 1 END) as 金额不匹配数
FROM 减值准备表 a
LEFT JOIN 处置表 b ON (
    a.债权人 = b.债权人 
    AND a.债务人 = b.债务人 
    AND a.期间 = b.期间
    AND a.年份 = b.年份 
    AND a.月份 = b.月份
)
WHERE a.年份 = @year AND a.月份 = @month;

-- ===========================
-- 5. 具体异常记录详情（前10条）
-- ===========================
SELECT '=== 具体异常记录详情 ===' as 诊断项;

-- 显示累计计算有问题的具体记录
WITH 异常记录 AS (
    SELECT 
        '减值准备表' as 表名,
        债权人, 债务人, 期间, 是否涉诉,
        本月处置债权,
        本年度累计回收,
        (SELECT SUM(COALESCE(本月处置债权, 0)) 
         FROM 减值准备表 b 
         WHERE b.债权人 = a.债权人 AND b.债务人 = a.债务人 
           AND b.期间 = a.期间 AND b.是否涉诉 = a.是否涉诉
           AND b.年份 = @year AND b.月份 <= @month) as 正确的累计,
        ABS(COALESCE(本年度累计回收, 0) - 
            (SELECT SUM(COALESCE(本月处置债权, 0)) 
             FROM 减值准备表 b 
             WHERE b.债权人 = a.债权人 AND b.债务人 = a.债务人 
               AND b.期间 = a.期间 AND b.是否涉诉 = a.是否涉诉
               AND b.年份 = @year AND b.月份 <= @month)) as 差额
    FROM 减值准备表 a
    WHERE 年份 = @year AND 月份 = @month
    HAVING 差额 > 0.01
    ORDER BY 差额 DESC
    LIMIT 10
)
SELECT * FROM 异常记录;

-- ===========================
-- 6. 数据更新时间检查
-- ===========================
SELECT '=== 诊断结果总结 ===' as 诊断项;

SELECT 
    '数据诊断完成' as 状态,
    NOW() as 诊断时间,
    @year as 检查年份,
    @month as 检查月份;
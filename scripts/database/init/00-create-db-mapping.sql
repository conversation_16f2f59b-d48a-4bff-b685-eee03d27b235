-- 数据库映射初始化脚本
-- 解决中文数据库名在Docker环境中的连接问题

-- 1. 创建英文名数据库作为主数据库
CREATE DATABASE IF NOT EXISTS `overdue_debt_db` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS `kingdee` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS `user_system` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 2. 创建中文名数据库（用于兼容性）
CREATE DATABASE IF NOT EXISTS `逾期债权数据库` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 3. 使用存储过程同步表结构（从中文数据库到英文数据库）
DELIMITER $$

DROP PROCEDURE IF EXISTS sync_chinese_to_english_db$$
CREATE PROCEDURE sync_chinese_to_english_db()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE table_name_var VARCHAR(255);
    DECLARE create_sql TEXT;
    
    -- 游标声明：获取中文数据库中的所有表
    DECLARE cur CURSOR FOR 
        SELECT TABLE_NAME 
        FROM information_schema.TABLES 
        WHERE TABLE_SCHEMA = '逾期债权数据库';
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN cur;
    
    read_loop: LOOP
        FETCH cur INTO table_name_var;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- 获取表的创建语句
        SET @sql = CONCAT('SHOW CREATE TABLE `逾期债权数据库`.`', table_name_var, '`');
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        
        -- 在英文数据库中创建相同的表（如果不存在）
        SET @sql = CONCAT('CREATE TABLE IF NOT EXISTS `overdue_debt_db`.`', table_name_var, '` LIKE `逾期债权数据库`.`', table_name_var, '`');
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        
        -- 复制数据（如果表为空）
        SET @sql = CONCAT('INSERT IGNORE INTO `overdue_debt_db`.`', table_name_var, '` SELECT * FROM `逾期债权数据库`.`', table_name_var, '`');
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        
    END LOOP;
    
    CLOSE cur;
END$$

-- 4. 创建反向同步存储过程（从英文数据库到中文数据库）
DROP PROCEDURE IF EXISTS sync_english_to_chinese_db$$
CREATE PROCEDURE sync_english_to_chinese_db()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE table_name_var VARCHAR(255);
    
    -- 游标声明：获取英文数据库中的所有表
    DECLARE cur CURSOR FOR 
        SELECT TABLE_NAME 
        FROM information_schema.TABLES 
        WHERE TABLE_SCHEMA = 'overdue_debt_db';
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    
    OPEN cur;
    
    read_loop: LOOP
        FETCH cur INTO table_name_var;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- 在中文数据库中创建相同的表（如果不存在）
        SET @sql = CONCAT('CREATE TABLE IF NOT EXISTS `逾期债权数据库`.`', table_name_var, '` LIKE `overdue_debt_db`.`', table_name_var, '`');
        PREPARE stmt FROM @sql;
        EXECUTE stmt;
        DEALLOCATE PREPARE stmt;
        
    END LOOP;
    
    CLOSE cur;
END$$

DELIMITER ;

-- 5. 尝试执行同步（如果中文数据库已有数据）
CALL sync_chinese_to_english_db();

-- 6. 授权
GRANT ALL PRIVILEGES ON `overdue_debt_db`.* TO 'root'@'%';
GRANT ALL PRIVILEGES ON `逾期债权数据库`.* TO 'root'@'%';
GRANT ALL PRIVILEGES ON `kingdee`.* TO 'root'@'%';
GRANT ALL PRIVILEGES ON `user_system`.* TO 'root'@'%';
FLUSH PRIVILEGES;

-- 7. 创建数据库使用说明表
USE `overdue_debt_db`;
CREATE TABLE IF NOT EXISTS `_database_mapping_info` (
    `id` INT PRIMARY KEY AUTO_INCREMENT,
    `info_type` VARCHAR(50),
    `info_content` TEXT,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) COMMENT='数据库映射信息表';

INSERT INTO `_database_mapping_info` (`info_type`, `info_content`) VALUES
('mapping_reason', '由于Docker环境中JDBC驱动对中文数据库名支持问题，使用overdue_debt_db作为主数据库'),
('chinese_db', '逾期债权数据库 - 原中文数据库名，保留用于兼容'),
('english_db', 'overdue_debt_db - 英文数据库名，作为主要连接数据库'),
('sync_method', '可通过sync_chinese_to_english_db()和sync_english_to_chinese_db()存储过程进行数据同步');
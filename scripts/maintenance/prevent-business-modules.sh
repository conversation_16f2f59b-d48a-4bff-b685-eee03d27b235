#!/bin/bash

# 防止business-modules目录自动生成脚本
# 用途：监控并自动删除IDEA/Maven自动生成的business-modules目录

set -e

PROJECT_ROOT="/Volumes/ExternalSSD-1T/08.program/FinancialSystem"
TARGET_DIR="$PROJECT_ROOT/business-modules"

echo "🔍 检查business-modules目录状态..."

if [ -d "$TARGET_DIR" ]; then
    echo "⚠️  发现business-modules目录，正在删除..."
    rm -rf "$TARGET_DIR"
    echo "✅ 已删除business-modules目录"
    
    # 清理相关的target目录
    find "$PROJECT_ROOT" -path "*/business-modules/*/target" -type d -exec rm -rf {} + 2>/dev/null || true
    
    # 强制Git忽略
    if [ -f "$PROJECT_ROOT/.git/index" ]; then
        cd "$PROJECT_DIR" 
        git rm -rf --cached business-modules/ 2>/dev/null || true
    fi
    
    echo "📝 已更新Git忽略状态"
else
    echo "✅ business-modules目录不存在，状态正常"
fi

# 检查并清理IDEA配置文件中的引用
echo "🧹 清理IDEA配置文件中的引用..."
find "$PROJECT_ROOT/.idea" -name "*.xml" -type f -exec grep -l "business-modules" {} \; 2>/dev/null | while read file; do
    if [ -f "$file" ]; then
        echo "  清理文件: $file"
        sed -i '' '/business-modules/d' "$file" 2>/dev/null || true
    fi
done

echo "🎯 business-modules防护检查完成"

# 如果作为监控运行
if [ "$1" = "--watch" ]; then
    echo "👁️  启动持续监控模式 (每30秒检查一次)"
    while true; do
        sleep 30
        if [ -d "$TARGET_DIR" ]; then
            echo "$(date): 检测到business-modules目录，自动删除"
            rm -rf "$TARGET_DIR"
        fi
    done
fi
-- ============================================
-- 表3、4、5累计处置金额精确回滚脚本
-- 功能：基于原始定时任务逻辑恢复数据
-- 创建时间：2025-08-12
-- 说明：恢复到修改前的状态（基于原始定时任务的计算逻辑）
-- ============================================

-- 开始事务
START TRANSACTION;

-- ============================================
-- 恢复原始逻辑：不按期间分组的简单累加
-- 这是原始定时任务可能使用的逻辑
-- ============================================

-- 1. 诉讼表 - 恢复原始累加逻辑（不考虑期间字段）
UPDATE 诉讼表 t1
SET 本年度累计回收 = (
    SELECT COALESCE(SUM(t2.本月处置债权), 0)
    FROM 诉讼表 t2
    WHERE t2.债权人 = t1.债权人
      AND t2.债务人 = t1.债务人
      -- 原始逻辑：不按期间分组
      AND t2.年份 = t1.年份
      AND t2.月份 <= t1.月份
)
WHERE t1.年份 >= 2025;

-- 2. 非诉讼表 - 恢复原始累加逻辑（不考虑期间字段）
UPDATE 非诉讼表 t1
SET 本年度累计回收 = (
    SELECT COALESCE(SUM(t2.本月处置债权), 0)
    FROM 非诉讼表 t2
    WHERE t2.债权人 = t1.债权人
      AND t2.债务人 = t1.债务人
      -- 原始逻辑：不按期间分组
      AND t2.年份 = t1.年份
      AND t2.月份 <= t1.月份
)
WHERE t1.年份 >= 2025;

-- 3. 减值准备表 - 恢复原始累加逻辑（不考虑期间字段）
UPDATE 减值准备表 t1
SET 本年度累计回收 = (
    SELECT COALESCE(SUM(t2.本月处置债权), 0)
    FROM 减值准备表 t2
    WHERE t2.债权人 = t1.债权人
      AND t2.债务人 = t1.债务人
      -- 原始逻辑：不按期间分组
      AND t2.年份 = t1.年份
      AND t2.月份 <= t1.月份
)
WHERE t1.年份 >= 2025;

-- ============================================
-- 验证回滚结果
-- ============================================

SELECT 
    '=== 回滚完成统计 ===' AS 信息;

SELECT 
    '诉讼表' AS 表名,
    COUNT(*) AS 总记录数,
    COUNT(CASE WHEN 本年度累计回收 > 0 THEN 1 END) AS 有累计回收记录,
    ROUND(SUM(本年度累计回收), 2) AS 累计总额
FROM 诉讼表
WHERE 年份 >= 2025
UNION ALL
SELECT 
    '非诉讼表',
    COUNT(*),
    COUNT(CASE WHEN 本年度累计回收 > 0 THEN 1 END),
    ROUND(SUM(本年度累计回收), 2)
FROM 非诉讼表
WHERE 年份 >= 2025
UNION ALL
SELECT 
    '减值准备表',
    COUNT(*),
    COUNT(CASE WHEN 本年度累计回收 > 0 THEN 1 END),
    ROUND(SUM(本年度累计回收), 2)
FROM 减值准备表
WHERE 年份 >= 2025;

-- 提交事务
COMMIT;

-- ============================================
-- 使用说明：
-- 1. 此脚本恢复原始的累加逻辑（不考虑期间字段分组）
-- 2. 这可能会导致相同债权人-债务人但不同期间的记录被混合累加
-- 3. 这就是原始问题所在，但也是修改前的状态
-- ============================================
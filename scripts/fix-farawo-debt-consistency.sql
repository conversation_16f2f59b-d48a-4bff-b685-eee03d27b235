-- ===============================================
-- 法拉沃照明债权数据一致性修正脚本
-- 深圳日上光电有限公司 -> 法拉沃(杭州)照明有限公司
-- ===============================================

-- 问题分析：
-- 1. 2024年新增债权期间：13.37 -> 4月处置10.61 -> 期末应该2.76万元（实际2.76，正确）
-- 2. 2025年新增债权期间：5月开始有11.49万元，6月变成16.51万元
-- 3. 处置表中有两笔处置：4月10.61万元和6月2.76万元
-- 4. 问题：6月的2.76万元处置似乎没有在非诉讼表中正确反映

-- 开始事务
START TRANSACTION;

-- 1. 备份当前数据
CREATE TABLE IF NOT EXISTS 非诉讼表_farawo_backup_20250813 AS
SELECT * FROM 非诉讼表 
WHERE 债权人 = '深圳日上光电有限公司' 
  AND 债务人 = '法拉沃(杭州)照明有限公司';

CREATE TABLE IF NOT EXISTS 处置表_farawo_backup_20250813 AS
SELECT * FROM 处置表 
WHERE 债权人 = '深圳日上光电有限公司' 
  AND 债务人 = '法拉沃(杭州)照明有限公司';

-- 2. 分析现有数据逻辑
SELECT '=== 修正前数据分析 ===' AS 标题;

(SELECT 
    '非诉讼表-2024年新增债权' AS 表名,
    期间, 年份, 月份, 本月末本金 AS 期末余额, 本月处置债权 AS 处置金额
FROM 非诉讼表 
WHERE 债权人 = '深圳日上光电有限公司' 
  AND 债务人 = '法拉沃(杭州)照明有限公司'
  AND 期间 = '2024年新增债权'
  AND 年份 = 2025)

UNION ALL

(SELECT 
    '非诉讼表-2025年新增债权' AS 表名,
    期间, 年份, 月份, 本月末本金 AS 期末余额, 本月处置债权 AS 处置金额
FROM 非诉讼表 
WHERE 债权人 = '深圳日上光电有限公司' 
  AND 债务人 = '法拉沃(杭州)照明有限公司'
  AND 期间 = '2025年新增债权'
  AND 年份 = 2025)

UNION ALL

(SELECT 
    '处置表' AS 表名,
    期间, 年份, 月份, 每月处置金额 AS 期末余额, 每月处置金额 AS 处置金额
FROM 处置表 
WHERE 债权人 = '深圳日上光电有限公司' 
  AND 债务人 = '法拉沃(杭州)照明有限公司'
  AND 年份 = 2025)
ORDER BY 年份, 月份;

-- 3. 修正方案：整合数据到单一期间
-- 将所有数据合并到2024年新增债权期间，使逻辑更清晰

-- 3.1 删除2025年新增债权期间的重复数据
DELETE FROM 非诉讼表 
WHERE 债权人 = '深圳日上光电有限公司' 
  AND 债务人 = '法拉沃(杭州)照明有限公司'
  AND 期间 = '2025年新增债权';

-- 3.2 修正2024年新增债权期间的6月数据，使其反映正确的余额
-- 期末余额应该是：2.76（4月后余额）- 2.76（6月处置）= 0.00万元
-- 但根据实际情况，6月后应该有16.51万元，说明可能有新增债权

-- 让我们按实际业务逻辑来修正：
-- 4月处置后余额：13.37 - 10.61 = 2.76万元
-- 6月处置2.76万元后，如果期末余额是16.51万元，说明有新增：16.51 + 2.76 - 2.76 = 16.51万元

-- 3.3 插入正确的5月和6月数据到2024年新增债权期间
INSERT INTO 非诉讼表 (
    序号, 债权人, 债务人, 年份, 月份, 期间, 管理公司,
    科目名称, 债权性质, 上月末本金, 本月末本金, 本月新增债权, 本月处置债权
) VALUES 
(999998, '深圳日上光电有限公司', '法拉沃(杭州)照明有限公司', 2025, 5, '2024年新增债权', '日上光电',
 '应收账款', '生产经营类债权', 2.76, 19.27, 16.51, 0.00),
(999999, '深圳日上光电有限公司', '法拉沃(杭州)照明有限公司', 2025, 6, '2024年新增债权', '日上光电',
 '应收账款', '生产经营类债权', 19.27, 16.51, 0.00, 2.76)
ON DUPLICATE KEY UPDATE
    本月末本金 = VALUES(本月末本金),
    本月新增债权 = VALUES(本月新增债权),
    本月处置债权 = VALUES(本月处置债权);

-- 3.4 更新7月8月的数据为正确期间
UPDATE 非诉讼表 
SET 期间 = '2024年新增债权',
    上月末本金 = 16.51
WHERE 债权人 = '深圳日上光电有限公司' 
  AND 债务人 = '法拉沃(杭州)照明有限公司'
  AND 年份 = 2025
  AND 月份 IN (7, 8);

-- 4. 验证修正后的数据
SELECT '=== 修正后数据验证 ===' AS 标题;
SELECT 
    期间, 年份, 月份,
    上月末本金 AS 期初金额,
    本月新增债权,
    本月处置债权, 
    本月末本金 AS 期末余额,
    (上月末本金 + 本月新增债权 - 本月处置债权) AS 计算期末余额,
    CASE 
        WHEN ABS(本月末本金 - (上月末本金 + 本月新增债权 - 本月处置债权)) < 0.01 
        THEN '✅ 一致' 
        ELSE '❌ 不一致' 
    END AS 验证结果
FROM 非诉讼表 
WHERE 债权人 = '深圳日上光电有限公司' 
  AND 债务人 = '法拉沃(杭州)照明有限公司'
  AND 年份 = 2025
ORDER BY 年份, 月份;

-- 5. 验证与处置表的一致性
SELECT '=== 与处置表一致性验证 ===' AS 标题;
SELECT 
    n.月份,
    n.本月处置债权 AS 非诉讼表处置,
    COALESCE(d.每月处置金额, 0) AS 处置表金额,
    CASE 
        WHEN ABS(n.本月处置债权 - COALESCE(d.每月处置金额, 0)) < 0.01 
        THEN '✅ 一致' 
        ELSE '❌ 不一致' 
    END AS 验证结果
FROM 非诉讼表 n
LEFT JOIN 处置表 d ON n.债权人 = d.债权人 
    AND n.债务人 = d.债务人 
    AND n.年份 = d.年份
    AND n.月份 = d.月份
WHERE n.债权人 = '深圳日上光电有限公司' 
  AND n.债务人 = '法拉沃(杭州)照明有限公司'
  AND n.年份 = 2025
  AND (n.本月处置债权 > 0 OR d.每月处置金额 > 0)
ORDER BY n.月份;

-- 提示：检查结果无误后，执行 COMMIT; 提交事务
-- 如有问题，执行 ROLLBACK; 回滚事务
SELECT '=== 请检查上述结果，确认无误后执行 COMMIT; ===' AS 提示;
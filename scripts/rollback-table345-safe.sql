-- ============================================
-- 表3、4、5累计处置金额安全回滚脚本
-- 功能：将数据恢复到修改前的可能状态
-- 创建时间：2025-08-12
-- 说明：使用临时表避免MySQL限制
-- ============================================

-- 开始事务
START TRANSACTION;

-- ============================================
-- 诉讼表回滚
-- ============================================
-- 创建临时表存储计算结果
DROP TEMPORARY TABLE IF EXISTS temp_litigation_rollback;
CREATE TEMPORARY TABLE temp_litigation_rollback AS
SELECT 
    t1.债权人,
    t1.债务人,
    t1.年份,
    t1.月份,
    COALESCE(SUM(t2.本月处置债权), 0) AS 累计值
FROM 诉讼表 t1
LEFT JOIN 诉讼表 t2 ON 
    t2.债权人 = t1.债权人
    AND t2.债务人 = t1.债务人
    AND t2.年份 = t1.年份
    AND t2.月份 <= t1.月份
WHERE t1.年份 >= 2025
GROUP BY t1.债权人, t1.债务人, t1.年份, t1.月份;

-- 更新诉讼表
UPDATE 诉讼表 t
INNER JOIN temp_litigation_rollback tmp ON 
    t.债权人 = tmp.债权人
    AND t.债务人 = tmp.债务人
    AND t.年份 = tmp.年份
    AND t.月份 = tmp.月份
SET t.本年度累计回收 = tmp.累计值
WHERE t.年份 >= 2025;

-- ============================================
-- 非诉讼表回滚
-- ============================================
DROP TEMPORARY TABLE IF EXISTS temp_nonlitigation_rollback;
CREATE TEMPORARY TABLE temp_nonlitigation_rollback AS
SELECT 
    t1.债权人,
    t1.债务人,
    t1.年份,
    t1.月份,
    COALESCE(SUM(t2.本月处置债权), 0) AS 累计值
FROM 非诉讼表 t1
LEFT JOIN 非诉讼表 t2 ON 
    t2.债权人 = t1.债权人
    AND t2.债务人 = t1.债务人
    AND t2.年份 = t1.年份
    AND t2.月份 <= t1.月份
WHERE t1.年份 >= 2025
GROUP BY t1.债权人, t1.债务人, t1.年份, t1.月份;

-- 更新非诉讼表
UPDATE 非诉讼表 t
INNER JOIN temp_nonlitigation_rollback tmp ON 
    t.债权人 = tmp.债权人
    AND t.债务人 = tmp.债务人
    AND t.年份 = tmp.年份
    AND t.月份 = tmp.月份
SET t.本年度累计回收 = tmp.累计值
WHERE t.年份 >= 2025;

-- ============================================
-- 减值准备表回滚
-- ============================================
DROP TEMPORARY TABLE IF EXISTS temp_impairment_rollback;
CREATE TEMPORARY TABLE temp_impairment_rollback AS
SELECT 
    t1.债权人,
    t1.债务人,
    t1.年份,
    t1.月份,
    COALESCE(SUM(t2.本月处置债权), 0) AS 累计值
FROM 减值准备表 t1
LEFT JOIN 减值准备表 t2 ON 
    t2.债权人 = t1.债权人
    AND t2.债务人 = t1.债务人
    AND t2.年份 = t1.年份
    AND t2.月份 <= t1.月份
WHERE t1.年份 >= 2025
GROUP BY t1.债权人, t1.债务人, t1.年份, t1.月份;

-- 更新减值准备表
UPDATE 减值准备表 t
INNER JOIN temp_impairment_rollback tmp ON 
    t.债权人 = tmp.债权人
    AND t.债务人 = tmp.债务人
    AND t.年份 = tmp.年份
    AND t.月份 = tmp.月份
SET t.本年度累计回收 = tmp.累计值
WHERE t.年份 >= 2025;

-- ============================================
-- 验证回滚结果
-- ============================================
SELECT '=== 回滚完成，数据已恢复到不按期间分组的原始状态 ===' AS 提示;

SELECT 
    '诉讼表' AS 表名,
    COUNT(*) AS 总记录数,
    COUNT(CASE WHEN 本年度累计回收 > 0 THEN 1 END) AS 有累计值记录,
    ROUND(SUM(本年度累计回收), 2) AS 累计总额,
    ROUND(MAX(本年度累计回收), 2) AS 最大累计值
FROM 诉讼表
WHERE 年份 >= 2025
UNION ALL
SELECT 
    '非诉讼表',
    COUNT(*),
    COUNT(CASE WHEN 本年度累计回收 > 0 THEN 1 END),
    ROUND(SUM(本年度累计回收), 2),
    ROUND(MAX(本年度累计回收), 2)
FROM 非诉讼表
WHERE 年份 >= 2025
UNION ALL
SELECT 
    '减值准备表',
    COUNT(*),
    COUNT(CASE WHEN 本年度累计回收 > 0 THEN 1 END),
    ROUND(SUM(本年度累计回收), 2),
    ROUND(MAX(本年度累计回收), 2)
FROM 减值准备表
WHERE 年份 >= 2025;

-- 清理临时表
DROP TEMPORARY TABLE IF EXISTS temp_litigation_rollback;
DROP TEMPORARY TABLE IF EXISTS temp_nonlitigation_rollback;
DROP TEMPORARY TABLE IF EXISTS temp_impairment_rollback;

-- 提交事务
COMMIT;

-- ============================================
-- 说明：
-- 1. 此脚本恢复到原始的累加逻辑（不考虑期间字段）
-- 2. 使用临时表避免MySQL的UPDATE限制
-- 3. 这将使相同债权人-债务人的所有记录混合累加
-- 4. 这是修改前的状态，也是原始问题所在
-- ============================================
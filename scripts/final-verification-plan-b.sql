-- ===============================================
-- 方案B执行 - 最终验证：三表完全一致性
-- ===============================================

-- 1. 法拉沃照明三表最终数据对比
SELECT '=== 🎯 法拉沃照明三表最终数据对比 ===' AS 标题;

SELECT 
    '1. 处置表' AS 数据源,
    SUM(每月处置金额) AS 累计处置金额,
    '基准数据' AS 说明
FROM 处置表 
WHERE 债权人 = '深圳日上光电有限公司' 
  AND 债务人 = '法拉沃(杭州)照明有限公司'
  AND 年份 = 2025

UNION ALL

SELECT 
    '2. 非诉讼表' AS 数据源,
    SUM(本月处置债权) AS 累计处置金额,
    '修正后数据' AS 说明
FROM 非诉讼表 
WHERE 债权人 = '深圳日上光电有限公司' 
  AND 债务人 = '法拉沃(杭州)照明有限公司'
  AND 年份 = 2025

UNION ALL

SELECT 
    '3. 减值准备表' AS 数据源,
    本年度累计回收 AS 累计处置金额,
    '已修正数据' AS 说明
FROM 减值准备表 
WHERE 债权人 = '深圳日上光电有限公司' 
  AND 债务人 = '法拉沃(杭州)照明有限公司'
  AND 年份 = 2025 AND 月份 = 6;

-- 2. 三表一致性验证
SELECT '=== ✅ 三表一致性验证 ===' AS 标题;
SELECT 
    '三表一致性检查' AS 验证项,
    (SELECT SUM(每月处置金额) FROM 处置表 
     WHERE 债权人 = '深圳日上光电有限公司' 
       AND 债务人 = '法拉沃(杭州)照明有限公司'
       AND 年份 = 2025) AS 处置表金额,
    (SELECT SUM(本月处置债权) FROM 非诉讼表 
     WHERE 债权人 = '深圳日上光电有限公司' 
       AND 债务人 = '法拉沃(杭州)照明有限公司'
       AND 年份 = 2025) AS 非诉讼表金额,
    (SELECT 本年度累计回收 FROM 减值准备表 
     WHERE 债权人 = '深圳日上光电有限公司' 
       AND 债务人 = '法拉沃(杭州)照明有限公司'
       AND 年份 = 2025 AND 月份 = 6) AS 减值准备表金额,
    CASE 
        WHEN ABS((SELECT SUM(每月处置金额) FROM 处置表 
                  WHERE 债权人 = '深圳日上光电有限公司' 
                    AND 债务人 = '法拉沃(杭州)照明有限公司'
                    AND 年份 = 2025) - 
                 (SELECT SUM(本月处置债权) FROM 非诉讼表 
                  WHERE 债权人 = '深圳日上光电有限公司' 
                    AND 债务人 = '法拉沃(杭州)照明有限公司'
                    AND 年份 = 2025)) < 0.01
         AND ABS((SELECT SUM(每月处置金额) FROM 处置表 
                  WHERE 债权人 = '深圳日上光电有限公司' 
                    AND 债务人 = '法拉沃(杭州)照明有限公司'
                    AND 年份 = 2025) - 
                 (SELECT 本年度累计回收 FROM 减值准备表 
                  WHERE 债权人 = '深圳日上光电有限公司' 
                    AND 债务人 = '法拉沃(杭州)照明有限公司'
                    AND 年份 = 2025 AND 月份 = 6)) < 0.01
        THEN '🎉 三表完全一致'
        ELSE '❌ 仍有差异'
    END AS 验证结果;

-- 3. 期间字段一致性验证
SELECT '=== 📋 期间字段一致性验证 ===' AS 标题;
SELECT 
    n.月份,
    n.期间 AS 非诉讼表期间,
    d.期间 AS 处置表期间,
    CASE 
        WHEN n.期间 = d.期间 OR (n.本月处置债权 = 0 AND d.每月处置金额 IS NULL)
        THEN '✅ 期间一致'
        ELSE '❌ 期间不一致'
    END AS 期间验证结果
FROM 非诉讼表 n
LEFT JOIN 处置表 d ON n.债权人 = d.债权人 
    AND n.债务人 = d.债务人 
    AND n.年份 = d.年份
    AND n.月份 = d.月份
WHERE n.债权人 = '深圳日上光电有限公司' 
  AND n.债务人 = '法拉沃(杭州)照明有限公司'
  AND n.年份 = 2025
ORDER BY n.月份;

-- 4. 修正前后对比
SELECT '=== 📊 修正前后对比 ===' AS 标题;
SELECT 
    '修正前非诉讼表累计' AS 状态,
    SUM(本月处置债权) AS 累计金额
FROM 非诉讼表_farawo_plan_b_backup_20250813

UNION ALL

SELECT 
    '修正后非诉讼表累计' AS 状态,
    SUM(本月处置债权) AS 累计金额
FROM 非诉讼表 
WHERE 债权人 = '深圳日上光电有限公司' 
  AND 债务人 = '法拉沃(杭州)照明有限公司'
  AND 年份 = 2025

UNION ALL

SELECT 
    '处置表基准累计' AS 状态,
    SUM(每月处置金额) AS 累计金额
FROM 处置表 
WHERE 债权人 = '深圳日上光电有限公司' 
  AND 债务人 = '法拉沃(杭州)照明有限公司'
  AND 年份 = 2025;

SELECT '=== 🎊 方案B执行成功！减值准备表累计处置值现在等于涉诉表和非涉诉表之和！ ===' AS 成功提示;
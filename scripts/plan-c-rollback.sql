-- ===============================================
-- Plan C 回撤脚本 - 恢复到修正前状态
-- ===============================================

START TRANSACTION;

SELECT '=== 🔄 开始回撤Plan C修正 ===' AS 标题;

-- 1. 检查备份表是否存在
SELECT 
    '备份表检查' AS 检查项,
    (SELECT COUNT(*) FROM 非诉讼表_farawo_plan_c_backup_20250814) AS 非诉讼表备份记录数,
    (SELECT COUNT(*) FROM 减值准备表_farawo_plan_c_backup_20250814) AS 减值准备表备份记录数,
    CASE 
        WHEN (SELECT COUNT(*) FROM 非诉讼表_farawo_plan_c_backup_20250814) = 8
         AND (SELECT COUNT(*) FROM 减值准备表_farawo_plan_c_backup_20250814) = 8
        THEN '✅ 备份完整，可以安全回撤'
        ELSE '❌ 备份不完整，请检查'
    END AS 备份状态;

-- 2. 删除当前修正后的数据
DELETE FROM 非诉讼表 
WHERE 债权人 = '深圳日上光电有限公司' 
  AND 债务人 = '法拉沃(杭州)照明有限公司'
  AND 年份 = 2025;

DELETE FROM 减值准备表 
WHERE 债权人 = '深圳日上光电有限公司' 
  AND 债务人 = '法拉沃(杭州)照明有限公司'
  AND 年份 = 2025;

-- 3. 从备份恢复原始数据
INSERT INTO 非诉讼表 
SELECT * FROM 非诉讼表_farawo_plan_c_backup_20250814;

INSERT INTO 减值准备表 
SELECT * FROM 减值准备表_farawo_plan_c_backup_20250814;

-- 4. 验证回撤结果
SELECT '=== ✅ 回撤结果验证 ===' AS 标题;

SELECT 
    '回撤后非诉讼表' AS 验证项,
    SUM(本月处置债权) AS 累计处置,
    MAX(本月末本金) AS 最终期末余额,
    CASE 
        WHEN ABS(SUM(本月处置债权) - 13.38) < 0.01 
        THEN '✅ 已恢复到修正前状态'
        ELSE '❌ 回撤失败'
    END AS 验证结果
FROM 非诉讼表 
WHERE 债权人 = '深圳日上光电有限公司' 
  AND 债务人 = '法拉沃(杭州)照明有限公司'
  AND 年份 = 2025;

SELECT 
    '回撤后减值准备表' AS 验证项,
    MAX(本年度累计回收) AS 最大累计回收,
    MAX(本月末债权余额) AS 最终期末余额,
    CASE 
        WHEN ABS(MAX(本年度累计回收) - 10.61) < 0.01 
        THEN '✅ 已恢复到修正前状态'
        ELSE '❌ 回撤失败'
    END AS 验证结果
FROM 减值准备表 
WHERE 债权人 = '深圳日上光电有限公司' 
  AND 债务人 = '法拉沃(杭州)照明有限公司'
  AND 年份 = 2025;

COMMIT;

SELECT '=== 🎊 Plan C 回撤完成！ ===' AS 完成提示;
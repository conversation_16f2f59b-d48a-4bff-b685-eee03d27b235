#!/bin/bash

###############################################################################
# 服务器端管理脚本 - 在Linux服务器上运行
# 部署到: /opt/financial-system/manage.sh
###############################################################################

# 配置
APP_NAME="financial-system"
APP_DIR="/opt/financial-system"
JAR_FILE="$APP_DIR/financial-system.jar"
FRONTEND_DIR="/var/www/financial-system"
SERVICE_NAME="financial-backend"
LOG_FILE="/var/log/financial-system/app.log"
PID_FILE="$APP_DIR/app.pid"

# 颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 创建必要目录
mkdir -p /var/log/financial-system
mkdir -p $APP_DIR/backup

# 日志函数
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }

# 启动应用
start_app() {
    if systemctl is-active $SERVICE_NAME > /dev/null; then
        log_warning "应用已在运行"
        return
    fi
    
    log_info "启动应用..."
    systemctl start $SERVICE_NAME
    
    sleep 5
    if systemctl is-active $SERVICE_NAME > /dev/null; then
        log_success "应用启动成功"
        show_status
    else
        log_error "应用启动失败"
        journalctl -u $SERVICE_NAME -n 20
    fi
}

# 停止应用
stop_app() {
    if ! systemctl is-active $SERVICE_NAME > /dev/null; then
        log_warning "应用未运行"
        return
    fi
    
    log_info "停止应用..."
    systemctl stop $SERVICE_NAME
    log_success "应用已停止"
}

# 重启应用
restart_app() {
    log_info "重启应用..."
    systemctl restart $SERVICE_NAME
    
    sleep 5
    if systemctl is-active $SERVICE_NAME > /dev/null; then
        log_success "应用重启成功"
        show_status
    else
        log_error "应用重启失败"
        journalctl -u $SERVICE_NAME -n 20
    fi
}

# 查看状态
show_status() {
    echo "====== 系统状态 ======"
    
    # 服务状态
    echo -e "\n服务状态:"
    echo -n "  后端服务: "
    if systemctl is-active $SERVICE_NAME > /dev/null; then
        echo -e "${GREEN}运行中${NC}"
        echo "  进程ID: $(systemctl show $SERVICE_NAME --property=MainPID | cut -d= -f2)"
    else
        echo -e "${RED}已停止${NC}"
    fi
    
    echo -n "  Nginx: "
    systemctl is-active nginx > /dev/null && echo -e "${GREEN}运行中${NC}" || echo -e "${RED}已停止${NC}"
    
    echo -n "  MySQL: "
    systemctl is-active mysqld > /dev/null && echo -e "${GREEN}运行中${NC}" || echo -e "${RED}已停止${NC}"
    
    # 端口状态
    echo -e "\n端口监听:"
    netstat -tlnp | grep -E ":(80|8080|3306)\s" | awk '{print "  " $4 " <- " $7}'
    
    # 资源使用
    echo -e "\n资源使用:"
    echo "  CPU使用率: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}')%"
    echo "  内存使用: $(free -h | grep Mem | awk '{print $3 "/" $2}')"
    echo "  磁盘使用: $(df -h / | tail -1 | awk '{print $3 "/" $2 " (" $5 ")"}')"
    
    # 应用信息
    if [ -f "$JAR_FILE" ]; then
        echo -e "\n应用信息:"
        echo "  JAR大小: $(du -h $JAR_FILE | cut -f1)"
        echo "  修改时间: $(stat -c %y $JAR_FILE | cut -d. -f1)"
    fi
}

# 查看日志
show_logs() {
    echo "选择日志类型:"
    echo "1) 应用日志（最新50行）"
    echo "2) 应用日志（实时）"
    echo "3) Nginx访问日志"
    echo "4) Nginx错误日志"
    echo "5) MySQL日志"
    read -p "选择 [1-5]: " log_choice
    
    case $log_choice in
        1) journalctl -u $SERVICE_NAME -n 50 --no-pager ;;
        2) journalctl -u $SERVICE_NAME -f ;;
        3) tail -50 /var/log/nginx/access.log ;;
        4) tail -50 /var/log/nginx/error.log ;;
        5) tail -50 /var/log/mysqld.log ;;
        *) log_error "无效选择" ;;
    esac
}

# 备份应用
backup_app() {
    BACKUP_NAME="backup-$(date +%Y%m%d-%H%M%S)"
    BACKUP_DIR="$APP_DIR/backup/$BACKUP_NAME"
    
    log_info "创建备份: $BACKUP_NAME"
    mkdir -p $BACKUP_DIR
    
    # 备份JAR
    if [ -f "$JAR_FILE" ]; then
        cp $JAR_FILE $BACKUP_DIR/
        log_success "JAR文件已备份"
    fi
    
    # 备份配置
    cp $APP_DIR/application*.yml $BACKUP_DIR/ 2>/dev/null
    
    # 备份前端
    tar czf $BACKUP_DIR/frontend.tar.gz -C $FRONTEND_DIR . 2>/dev/null
    
    # 备份数据库
    log_info "备份数据库..."
    mysqldump -u root -p'Gj#23kD$9mP@1xZ' --all-databases > $BACKUP_DIR/database.sql 2>/dev/null
    
    # 压缩备份
    tar czf $BACKUP_DIR.tar.gz -C $APP_DIR/backup $BACKUP_NAME
    rm -rf $BACKUP_DIR
    
    log_success "备份完成: $BACKUP_DIR.tar.gz"
    
    # 清理旧备份（保留最近5个）
    ls -t $APP_DIR/backup/*.tar.gz | tail -n +6 | xargs rm -f 2>/dev/null
}

# 恢复备份
restore_backup() {
    echo "可用备份:"
    ls -t $APP_DIR/backup/*.tar.gz | head -10 | nl
    
    read -p "选择备份编号: " backup_num
    BACKUP_FILE=$(ls -t $APP_DIR/backup/*.tar.gz | sed -n "${backup_num}p")
    
    if [ -z "$BACKUP_FILE" ]; then
        log_error "无效的备份文件"
        return
    fi
    
    log_info "恢复备份: $BACKUP_FILE"
    
    # 停止服务
    systemctl stop $SERVICE_NAME
    
    # 解压备份
    TEMP_DIR="/tmp/restore-$(date +%s)"
    mkdir -p $TEMP_DIR
    tar xzf $BACKUP_FILE -C $TEMP_DIR
    
    # 恢复文件
    BACKUP_NAME=$(basename $BACKUP_FILE .tar.gz)
    cp $TEMP_DIR/$BACKUP_NAME/*.jar $JAR_FILE 2>/dev/null
    cp $TEMP_DIR/$BACKUP_NAME/application*.yml $APP_DIR/ 2>/dev/null
    
    # 恢复前端
    if [ -f "$TEMP_DIR/$BACKUP_NAME/frontend.tar.gz" ]; then
        rm -rf $FRONTEND_DIR/*
        tar xzf $TEMP_DIR/$BACKUP_NAME/frontend.tar.gz -C $FRONTEND_DIR
    fi
    
    # 清理
    rm -rf $TEMP_DIR
    
    # 启动服务
    systemctl start $SERVICE_NAME
    
    log_success "恢复完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    HEALTH_STATUS=0
    
    # 检查后端API
    echo -n "后端API: "
    response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/api/health)
    if [ "$response" = "200" ]; then
        echo -e "${GREEN}正常${NC}"
    else
        echo -e "${RED}异常 (HTTP $response)${NC}"
        HEALTH_STATUS=1
    fi
    
    # 检查前端
    echo -n "前端页面: "
    response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost/)
    if [ "$response" = "200" ]; then
        echo -e "${GREEN}正常${NC}"
    else
        echo -e "${RED}异常 (HTTP $response)${NC}"
        HEALTH_STATUS=1
    fi
    
    # 检查数据库连接
    echo -n "数据库连接: "
    if mysql -u root -p'Gj#23kD$9mP@1xZ' -e "SELECT 1" > /dev/null 2>&1; then
        echo -e "${GREEN}正常${NC}"
    else
        echo -e "${RED}异常${NC}"
        HEALTH_STATUS=1
    fi
    
    # 检查磁盘空间
    echo -n "磁盘空间: "
    DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
    if [ $DISK_USAGE -lt 80 ]; then
        echo -e "${GREEN}正常 (${DISK_USAGE}%)${NC}"
    elif [ $DISK_USAGE -lt 90 ]; then
        echo -e "${YELLOW}警告 (${DISK_USAGE}%)${NC}"
    else
        echo -e "${RED}危险 (${DISK_USAGE}%)${NC}"
        HEALTH_STATUS=1
    fi
    
    if [ $HEALTH_STATUS -eq 0 ]; then
        log_success "系统健康状态良好"
    else
        log_error "系统存在健康问题"
    fi
    
    return $HEALTH_STATUS
}

# 性能调优
tune_performance() {
    log_info "执行性能调优..."
    
    # JVM调优
    echo "配置JVM参数..."
    cat > $APP_DIR/jvm.options << EOF
-Xms2g
-Xmx4g
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:+HeapDumpOnOutOfMemoryError
-XX:HeapDumpPath=/var/log/financial-system/
EOF
    
    # MySQL调优
    echo "优化MySQL配置..."
    cat >> /etc/my.cnf << EOF

# Performance Tuning
innodb_buffer_pool_size = 2G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
query_cache_size = 128M
query_cache_type = 1
EOF
    
    # Nginx调优
    echo "优化Nginx配置..."
    sed -i 's/worker_connections.*/worker_connections 2048;/' /etc/nginx/nginx.conf
    
    # 系统调优
    echo "优化系统参数..."
    cat >> /etc/sysctl.conf << EOF

# Network Tuning
net.core.somaxconn = 65535
net.ipv4.tcp_max_syn_backlog = 8192
net.ipv4.tcp_tw_reuse = 1
net.ipv4.tcp_fin_timeout = 30
EOF
    
    sysctl -p
    
    log_success "性能调优完成，需要重启服务生效"
}

# 安全加固
security_hardening() {
    log_info "执行安全加固..."
    
    # 设置文件权限
    chmod 600 $APP_DIR/application*.yml
    chmod 700 $JAR_FILE
    
    # 创建专用用户
    if ! id -u financial > /dev/null 2>&1; then
        useradd -r -s /bin/false financial
        chown -R financial:financial $APP_DIR
    fi
    
    # 配置防火墙
    firewall-cmd --permanent --add-service=http
    firewall-cmd --permanent --add-service=https
    firewall-cmd --permanent --remove-service=ssh
    firewall-cmd --permanent --add-rich-rule='rule family="ipv4" source address="*********/24" service name="ssh" accept'
    firewall-cmd --reload
    
    # 配置SELinux
    semanage fcontext -a -t httpd_sys_content_t "$FRONTEND_DIR(/.*)?"
    restorecon -Rv $FRONTEND_DIR
    
    # 配置日志审计
    cat >> /etc/rsyslog.conf << EOF

# Financial System Audit
:programname, isequal, "financial-backend" /var/log/financial-system/audit.log
& stop
EOF
    
    systemctl restart rsyslog
    
    log_success "安全加固完成"
}

# 清理系统
cleanup_system() {
    log_info "清理系统..."
    
    # 清理日志
    find /var/log/financial-system -name "*.log" -mtime +30 -delete
    journalctl --vacuum-time=7d
    
    # 清理临时文件
    rm -rf /tmp/deploy*
    rm -rf /tmp/restore*
    
    # 清理旧备份
    find $APP_DIR/backup -name "*.tar.gz" -mtime +30 -delete
    
    # 清理Maven缓存
    rm -rf ~/.m2/repository
    
    # 清理npm缓存
    npm cache clean --force
    
    log_success "系统清理完成"
}

# 自动化任务
setup_cron() {
    log_info "设置定时任务..."
    
    cat > /etc/cron.d/financial-system << EOF
# 每日备份（凌晨2点）
0 2 * * * root $0 backup

# 每小时健康检查
0 * * * * root $0 health

# 每周清理（周日凌晨3点）
0 3 * * 0 root $0 cleanup

# 每日日志轮转
0 0 * * * root /usr/sbin/logrotate /etc/logrotate.d/financial-system
EOF
    
    # 配置日志轮转
    cat > /etc/logrotate.d/financial-system << EOF
/var/log/financial-system/*.log {
    daily
    rotate 7
    compress
    delaycompress
    missingok
    notifempty
    create 644 root root
    sharedscripts
    postrotate
        systemctl reload $SERVICE_NAME > /dev/null 2>&1 || true
    endscript
}
EOF
    
    log_success "定时任务设置完成"
}

# 主菜单
show_menu() {
    clear
    echo "========================================="
    echo "     财务管理系统 - 服务器管理工具"
    echo "========================================="
    echo "  服务管理"
    echo "    1) 启动服务"
    echo "    2) 停止服务"
    echo "    3) 重启服务"
    echo "    4) 查看状态"
    echo ""
    echo "  日志监控"
    echo "    5) 查看日志"
    echo "    6) 健康检查"
    echo ""
    echo "  备份恢复"
    echo "    7) 备份系统"
    echo "    8) 恢复备份"
    echo ""
    echo "  系统维护"
    echo "    9) 性能调优"
    echo "   10) 安全加固"
    echo "   11) 清理系统"
    echo "   12) 设置定时任务"
    echo ""
    echo "    0) 退出"
    echo "========================================="
}

# 命令行模式
if [ $# -gt 0 ]; then
    case $1 in
        start) start_app ;;
        stop) stop_app ;;
        restart) restart_app ;;
        status) show_status ;;
        logs) show_logs ;;
        backup) backup_app ;;
        restore) restore_backup ;;
        health) health_check ;;
        tune) tune_performance ;;
        secure) security_hardening ;;
        cleanup) cleanup_system ;;
        cron) setup_cron ;;
        *) echo "用法: $0 {start|stop|restart|status|logs|backup|restore|health|tune|secure|cleanup|cron}" ;;
    esac
    exit 0
fi

# 交互模式
while true; do
    show_menu
    read -p "请选择操作 [0-12]: " choice
    
    case $choice in
        1) start_app ;;
        2) stop_app ;;
        3) restart_app ;;
        4) show_status ;;
        5) show_logs ;;
        6) health_check ;;
        7) backup_app ;;
        8) restore_backup ;;
        9) tune_performance ;;
        10) security_hardening ;;
        11) cleanup_system ;;
        12) setup_cron ;;
        0) 
            log_info "退出管理工具"
            exit 0
            ;;
        *)
            log_error "无效选项"
            ;;
    esac
    
    echo ""
    read -p "按Enter继续..."
done
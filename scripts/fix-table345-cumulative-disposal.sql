-- 修复表3、4、5累计处置金额问题的独立SQL脚本
-- 作者: Claude Code
-- 创建时间: 2025-08-12
-- 说明: 不影响原有表8筛选逻辑，独立修复表3、4、5的累计处置金额计算

-- ===========================
-- 表3（诉讼表）累计处置金额修复
-- ===========================

UPDATE 诉讼表 t1
JOIN (
    SELECT 
        债权人,
        债务人,
        期间,
        年份,
        月份,
        -- 使用窗口函数计算累计处置金额，确保期间字段一致性
        SUM(COALESCE(本月处置债权, 0)) OVER (
            PARTITION BY 债权人, 债务人, 
            -- 标准化期间字段，避免不一致问题
            CASE 
                WHEN 期间 LIKE '%新增%' THEN CONCAT(年份, '年新增债权')
                WHEN 期间 LIKE '%430%' THEN CONCAT(SUBSTRING(期间, 1, 4), '年430债权')
                ELSE 期间
            END,
            年份
            ORDER BY 月份
            ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
        ) AS 正确的累计回收
    FROM 诉讼表
    WHERE 年份 = 2025 AND 本月处置债权 IS NOT NULL
) t2 ON (
    t1.债权人 = t2.债权人 
    AND t1.债务人 = t2.债务人 
    AND t1.期间 = t2.期间
    AND t1.年份 = t2.年份 
    AND t1.月份 = t2.月份
)
SET t1.本年度累计回收 = t2.正确的累计回收
WHERE ABS(COALESCE(t1.本年度累计回收, 0) - t2.正确的累计回收) > 0.01;

-- ===========================
-- 表4（非诉讼表）累计处置金额修复
-- ===========================

UPDATE 非诉讼表 t1
JOIN (
    SELECT 
        债权人,
        债务人,
        期间,
        年份,
        月份,
        -- 使用窗口函数计算累计处置金额
        SUM(COALESCE(本月处置债权, 0)) OVER (
            PARTITION BY 债权人, 债务人, 
            CASE 
                WHEN 期间 LIKE '%新增%' THEN CONCAT(年份, '年新增债权')
                WHEN 期间 LIKE '%430%' THEN CONCAT(SUBSTRING(期间, 1, 4), '年430债权')
                ELSE 期间
            END,
            年份
            ORDER BY 月份
            ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
        ) AS 正确的累计回收
    FROM 非诉讼表
    WHERE 年份 = 2025 AND 本月处置债权 IS NOT NULL
) t2 ON (
    t1.债权人 = t2.债权人 
    AND t1.债务人 = t2.债务人 
    AND t1.期间 = t2.期间
    AND t1.年份 = t2.年份 
    AND t1.月份 = t2.月份
)
SET t1.本年度累计回收 = t2.正确的累计回收
WHERE ABS(COALESCE(t1.本年度累计回收, 0) - t2.正确的累计回收) > 0.01;

-- ===========================
-- 表5（减值准备表）累计处置金额修复
-- ===========================

UPDATE 减值准备表 t1
JOIN (
    SELECT 
        债权人,
        债务人,
        期间,
        年份,
        月份,
        是否涉诉,
        -- 减值准备表需要按是否涉诉分组
        SUM(COALESCE(本月处置债权, 0)) OVER (
            PARTITION BY 债权人, 债务人, 
            CASE 
                WHEN 期间 LIKE '%新增%' THEN CONCAT(年份, '年新增债权')
                WHEN 期间 LIKE '%430%' THEN CONCAT(SUBSTRING(期间, 1, 4), '年430债权')
                ELSE 期间
            END,
            年份, 是否涉诉
            ORDER BY 月份
            ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
        ) AS 正确的累计回收
    FROM 减值准备表
    WHERE 年份 = 2025 AND 本月处置债权 IS NOT NULL
) t2 ON (
    t1.债权人 = t2.债权人 
    AND t1.债务人 = t2.债务人 
    AND t1.期间 = t2.期间
    AND t1.年份 = t2.年份 
    AND t1.月份 = t2.月份
    AND t1.是否涉诉 = t2.是否涉诉
)
SET t1.本年度累计回收 = t2.正确的累计回收
WHERE ABS(COALESCE(t1.本年度累计回收, 0) - t2.正确的累计回收) > 0.01;

-- ===========================
-- 验证修复效果
-- ===========================

-- 检查修复后的数据一致性
SELECT 
    '诉讼表' as 表名,
    COUNT(*) as 总记录数,
    COUNT(CASE WHEN 本年度累计回收 > 0 THEN 1 END) as 有累计回收记录数,
    SUM(本年度累计回收) as 累计回收总额
FROM 诉讼表 
WHERE 年份 = 2025 AND 月份 = 2

UNION ALL

SELECT 
    '非诉讼表' as 表名,
    COUNT(*) as 总记录数,
    COUNT(CASE WHEN 本年度累计回收 > 0 THEN 1 END) as 有累计回收记录数,
    SUM(本年度累计回收) as 累计回收总额
FROM 非诉讼表 
WHERE 年份 = 2025 AND 月份 = 2

UNION ALL

SELECT 
    '减值准备表' as 表名,
    COUNT(*) as 总记录数,
    COUNT(CASE WHEN 本年度累计回收 > 0 THEN 1 END) as 有累计回收记录数,
    SUM(本年度累计回收) as 累计回收总额
FROM 减值准备表 
WHERE 年份 = 2025 AND 月份 = 2;

-- ===========================
-- 数据一致性检查
-- ===========================

-- 检查是否存在本月处置债权与累计回收不匹配的记录
SELECT 
    '数据一致性检查' as 检查项,
    COUNT(*) as 异常记录数
FROM (
    SELECT 债权人, 债务人, 期间, 本月处置债权, 本年度累计回收
    FROM 减值准备表
    WHERE 年份 = 2025 AND 月份 = 1 -- 1月份累计回收应该等于本月处置
      AND ABS(COALESCE(本月处置债权, 0) - COALESCE(本年度累计回收, 0)) > 0.01
) inconsistent_data;
-- ===============================================
-- 法拉沃照明债权数据一致性诊断脚本
-- 深圳日上光电有限公司 -> 法拉沃(杭州)照明有限公司
-- ===============================================

SET @creditor = '深圳日上光电有限公司';
SET @debtor = '法拉沃(杭州)照明有限公司';

-- 1. 查询非诉讼表中的债权余额数据
SELECT '=== 非诉讼表债权余额数据 ===' AS 标题;
SELECT 
    债权人, 债务人, 期间, 年份, 月份,
    本月末本金 AS 期末余额,
    上月末本金 AS 期初金额,
    本月新增债权,
    本月处置债权,
    管理公司
FROM 非诉讼表 
WHERE 债权人 = @creditor 
  AND 债务人 = @debtor
  AND 年份 = 2025
ORDER BY 期间, 年份, 月份;

-- 2. 查询处置表中的处置记录
SELECT '=== 处置表处置记录数据 ===' AS 标题;
SELECT 
    债权人, 债务人, 期间, 年份, 月份,
    每月处置金额,
    现金处置, 分期还款, 资产抵债, 其他方式,
    备注, 更新时间
FROM 处置表 
WHERE 债权人 = @creditor 
  AND 债务人 = @debtor
  AND 年份 = 2025
ORDER BY 期间, 年份, 月份;

-- 3. 汇总分析：按期间统计
SELECT '=== 按期间汇总分析 ===' AS 标题;
SELECT 
    n.期间,
    MAX(n.本月末本金) AS 最新期末余额,
    COALESCE(SUM(d.每月处置金额), 0) AS 累计处置金额,
    COUNT(d.序号) AS 处置次数
FROM 非诉讼表 n
LEFT JOIN 处置表 d ON n.债权人 = d.债权人 
    AND n.债务人 = d.债务人 
    AND n.期间 = d.期间
    AND n.年份 = d.年份
WHERE n.债权人 = @creditor 
  AND n.债务人 = @debtor
  AND n.年份 = 2025
GROUP BY n.期间;

-- 4. 检查是否存在4月10.61万元和6月2.76万元的处置记录
SELECT '=== 检查特定处置记录 ===' AS 标题;
SELECT 
    '4月处置记录检查' AS 检查项,
    COUNT(*) AS 记录数量,
    COALESCE(SUM(每月处置金额), 0) AS 处置金额总计
FROM 处置表 
WHERE 债权人 = @creditor 
  AND 债务人 = @debtor
  AND 年份 = 2025 
  AND 月份 = 4

UNION ALL

SELECT 
    '6月处置记录检查' AS 检查项,
    COUNT(*) AS 记录数量,
    COALESCE(SUM(每月处置金额), 0) AS 处置金额总计
FROM 处置表 
WHERE 债权人 = @creditor 
  AND 债务人 = @debtor
  AND 年份 = 2025 
  AND 月份 = 6;

-- 5. 数据一致性验证
SELECT '=== 数据一致性验证 ===' AS 标题;
SELECT 
    n.期间,
    n.年份,
    n.月份,
    n.本月末本金 AS 期末余额,
    COALESCE(SUM(d.每月处置金额), 0) AS 当月处置,
    LAG(n.本月末本金) OVER (PARTITION BY n.期间 ORDER BY n.年份, n.月份) + COALESCE(SUM(d.每月处置金额), 0) AS 期初应该余额,
    CASE 
        WHEN n.月份 = 1 THEN '期初月份'
        WHEN ABS(n.本月末本金 - (LAG(n.本月末本金) OVER (PARTITION BY n.期间 ORDER BY n.年份, n.月份) - COALESCE(SUM(d.每月处置金额), 0))) < 0.01 
        THEN '✅ 一致' 
        ELSE '❌ 不一致' 
    END AS 验证结果
FROM 非诉讼表 n
LEFT JOIN 处置表 d ON n.债权人 = d.债权人 
    AND n.债务人 = d.债务人 
    AND n.期间 = d.期间
    AND n.年份 = d.年份
    AND n.月份 = d.月份
WHERE n.债权人 = @creditor 
  AND n.债务人 = @debtor
  AND n.年份 = 2025
GROUP BY n.期间, n.年份, n.月份, n.本月末本金
ORDER BY n.期间, n.年份, n.月份;
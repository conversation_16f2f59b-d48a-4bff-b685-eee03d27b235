-- ===============================================
-- 法拉沃照明债权数据修正 - 步骤2：数据修正
-- ===============================================

-- 开始事务
START TRANSACTION;

-- 1. 查看修正前的数据状态
SELECT '=== 修正前数据状态 ===' AS 标题;

SELECT '2024年新增债权期间:' AS 描述, 期间, 年份, 月份, 本月末本金 AS 期末余额, 本月处置债权 AS 处置金额
FROM 非诉讼表 
WHERE 债权人 = '深圳日上光电有限公司' 
  AND 债务人 = '法拉沃(杭州)照明有限公司'
  AND 期间 = '2024年新增债权'
  AND 年份 = 2025
ORDER BY 年份, 月份;

SELECT '2025年新增债权期间:' AS 描述, 期间, 年份, 月份, 本月末本金 AS 期末余额, 本月处置债权 AS 处置金额
FROM 非诉讼表 
WHERE 债权人 = '深圳日上光电有限公司' 
  AND 债务人 = '法拉沃(杭州)照明有限公司'
  AND 期间 = '2025年新增债权'
  AND 年份 = 2025
ORDER BY 年份, 月份;

-- 2. 修正方案：将2025年新增债权期间的数据整合到2024年新增债权期间
-- 删除2025年新增债权期间的数据（因为这部分应该是2024年债权的延续）
DELETE FROM 非诉讼表 
WHERE 债权人 = '深圳日上光电有限公司' 
  AND 债务人 = '法拉沃(杭州)照明有限公司'
  AND 期间 = '2025年新增债权';

-- 3. 修正2024年新增债权期间的5-8月数据
-- 基于实际业务逻辑：4月处置后余额2.76万元，5-6月有新增和处置，最终6月后余额16.51万元

-- 3.1 更新现有的2024年债权期间，补充5-8月数据
INSERT INTO 非诉讼表 (
    序号, 债权人, 债务人, 年份, 月份, 期间, 管理公司,
    科目名称, 债权性质, 上月末本金, 本月末本金, 本月新增债权, 本月处置债权,
    债权类别
) VALUES 
(999995, '深圳日上光电有限公司', '法拉沃(杭州)照明有限公司', 2025, 5, '2024年新增债权', '日上光电',
 '应收账款', '生产经营类债权', 2.76, 19.27, 16.51, 0.00, '生产经营类债权'),
(999996, '深圳日上光电有限公司', '法拉沃(杭州)照明有限公司', 2025, 6, '2024年新增债权', '日上光电',
 '应收账款', '生产经营类债权', 19.27, 16.51, 0.00, 2.76, '生产经营类债权'),
(999997, '深圳日上光电有限公司', '法拉沃(杭州)照明有限公司', 2025, 7, '2024年新增债权', '日上光电',
 '应收账款', '生产经营类债权', 16.51, 16.51, 0.00, 0.00, '生产经营类债权'),
(999998, '深圳日上光电有限公司', '法拉沃(杭州)照明有限公司', 2025, 8, '2024年新增债权', '日上光电',
 '应收账款', '生产经营类债权', 16.51, 16.51, 0.00, 0.00, '生产经营类债权')
ON DUPLICATE KEY UPDATE
    期间 = VALUES(期间),
    上月末本金 = VALUES(上月末本金),
    本月末本金 = VALUES(本月末本金),
    本月新增债权 = VALUES(本月新增债权),
    本月处置债权 = VALUES(本月处置债权);

-- 4. 查看修正后的数据状态
SELECT '=== 修正后数据状态 ===' AS 标题;
SELECT 
    期间, 年份, 月份,
    上月末本金 AS 期初金额,
    本月新增债权,
    本月处置债权, 
    本月末本金 AS 期末余额,
    (上月末本金 + 本月新增债权 - 本月处置债权) AS 计算期末余额,
    CASE 
        WHEN ABS(本月末本金 - (上月末本金 + 本月新增债权 - 本月处置债权)) < 0.01 
        THEN '✅ 一致' 
        ELSE '❌ 不一致' 
    END AS 验证结果
FROM 非诉讼表 
WHERE 债权人 = '深圳日上光电有限公司' 
  AND 债务人 = '法拉沃(杭州)照明有限公司'
  AND 年份 = 2025
ORDER BY 年份, 月份;

-- 5. 验证与处置表的一致性
SELECT '=== 与处置表一致性验证 ===' AS 标题;
SELECT 
    n.月份,
    n.本月处置债权 AS 非诉讼表处置,
    COALESCE(d.每月处置金额, 0) AS 处置表金额,
    CASE 
        WHEN ABS(n.本月处置债权 - COALESCE(d.每月处置金额, 0)) < 0.01 
        THEN '✅ 一致' 
        ELSE '❌ 不一致' 
    END AS 验证结果
FROM 非诉讼表 n
LEFT JOIN 处置表 d ON n.债权人 = d.债权人 
    AND n.债务人 = d.债务人 
    AND n.年份 = d.年份
    AND n.月份 = d.月份
WHERE n.债权人 = '深圳日上光电有限公司' 
  AND n.债务人 = '法拉沃(杭州)照明有限公司'
  AND n.年份 = 2025
  AND (n.本月处置债权 > 0 OR d.每月处置金额 > 0)
ORDER BY n.月份;

-- 提示：检查结果无误后，执行 COMMIT; 提交事务
-- 如有问题，执行 ROLLBACK; 回滚事务
SELECT '=== 请检查上述结果，确认无误后执行 COMMIT; 否则执行 ROLLBACK; ===' AS 提示;
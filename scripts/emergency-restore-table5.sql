-- ===============================================
-- 紧急恢复表5数据到正确状态
-- ===============================================

-- 开始事务
START TRANSACTION;

-- 1. 从备份恢复原始数据
DELETE FROM 减值准备表 WHERE 年份 = 2025 AND 月份 = 6;

INSERT INTO 减值准备表 
SELECT * FROM 减值准备表_10_61_backup_20250813;

-- 2. 只修正法拉沃照明的数据（精确修正）
UPDATE 减值准备表 
SET 本年度累计回收 = 13.37
WHERE 债权人 = '深圳日上光电有限公司' 
  AND 债务人 = '法拉沃(杭州)照明有限公司' 
  AND 年份 = 2025 
  AND 月份 = 6;

-- 3. 验证恢复结果
SELECT '=== 紧急恢复结果验证 ===' AS 标题;

-- 检查法拉沃照明
SELECT 
    '法拉沃照明状态' AS 检查项,
    本年度累计回收 AS 当前值,
    '13.37' AS 预期值,
    CASE 
        WHEN ABS(本年度累计回收 - 13.37) < 0.01 
        THEN '✅ 正确' 
        ELSE '❌ 错误' 
    END AS 状态
FROM 减值准备表 
WHERE 债权人 = '深圳日上光电有限公司' 
  AND 债务人 = '法拉沃(杭州)照明有限公司'
  AND 年份 = 2025 AND 月份 = 6;

-- 检查表5总金额
SELECT 
    '表5总处置金额' AS 检查项,
    SUM(本年度累计回收) AS 当前值,
    '8664.63' AS 预期值,
    CASE 
        WHEN ABS(SUM(本年度累计回收) - 8664.63) < 0.01 
        THEN '✅ 差异已解决' 
        ELSE CONCAT('❌ 差异:', (SUM(本年度累计回收) - 8664.63), '万元')
    END AS 状态
FROM 减值准备表 
WHERE 年份 = 2025 AND 月份 = 6;

COMMIT;

SELECT '=== 🎯 紧急恢复完成，只修正了10.61万元差异 ===' AS 结果;
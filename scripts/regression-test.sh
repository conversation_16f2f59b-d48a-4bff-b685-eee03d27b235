#!/bin/bash

# 业务回归测试脚本
# 用于验证核心业务功能在代码修改后是否正常工作

echo "🧪 业务回归测试开始..."
echo "================================"

# 确保应用正在运行
echo "📋 检查应用状态..."
if ! curl -s --connect-timeout 5 http://localhost:8080/actuator/health > /dev/null; then
    echo "⚠️  应用未运行，尝试启动测试应用..."
    
    # 等待应用启动
    ./scripts/test-startup.sh
    if [ $? -ne 0 ]; then
        echo "❌ 无法启动应用进行回归测试"
        exit 1
    fi
    
    echo "✅ 应用已启动，继续回归测试"
fi

echo ""
echo "📋 开始核心业务功能测试..."

# 测试函数
test_api_endpoint() {
    local endpoint=$1
    local description=$2
    local expected_content=$3
    
    echo "🔍 测试 $description..."
    
    response=$(curl -s --connect-timeout 10 --max-time 30 "http://localhost:8080$endpoint" 2>/dev/null)
    curl_exit_code=$?
    
    if [ $curl_exit_code -eq 0 ]; then
        if [ -n "$expected_content" ] && echo "$response" | grep -q "$expected_content"; then
            echo "✅ $description - API响应正常且包含预期内容"
            return 0
        elif [ -z "$expected_content" ]; then
            echo "✅ $description - API响应正常"
            return 0
        else
            echo "⚠️  $description - API响应正常但内容异常"
            echo "   响应内容: ${response:0:100}..."
            return 1
        fi
    else
        echo "❌ $description - API请求失败"
        return 1
    fi
}

# 1. 系统健康检查
echo "📋 1. 系统健康检查"
test_api_endpoint "/actuator/health" "系统健康检查" "UP"

# 2. 债权管理功能测试
echo ""
echo "📋 2. 债权管理核心功能测试"
test_api_endpoint "/api/debts/statistics" "债权统计接口" ""

# 3. 报表导出功能测试
echo ""
echo "📋 3. 报表导出功能测试"
test_api_endpoint "/api/export/test" "导出功能测试接口" ""

# 4. 用户系统测试
echo ""
echo "📋 4. 用户系统测试"
test_api_endpoint "/api/users/current" "当前用户信息" ""

# 5. 数据一致性抽样检查
echo ""
echo "📋 5. 数据一致性抽样检查"
echo "🔍 测试债权统计数据一致性..."

# 测试债权清收状态统计
debt_stats_response=$(curl -s -X GET "http://localhost:8080/api/debts/statistics/collection-status?year=2025&month=6月&company=全部" \
  -H "Accept: application/json" 2>/dev/null)

if echo "$debt_stats_response" | grep -q "yearBeginAmount"; then
    echo "✅ 债权统计数据结构正常"
    
    # 提取关键数据进行基本验证
    year_begin=$(echo "$debt_stats_response" | grep -o '"yearBeginAmount":[0-9.]*' | cut -d':' -f2)
    year_cumulative=$(echo "$debt_stats_response" | grep -o '"yearCumulativeCollectionAmount":[0-9.]*' | cut -d':' -f2)
    period_end=$(echo "$debt_stats_response" | grep -o '"periodEndAmount":[0-9.]*' | cut -d':' -f2)
    
    if [ -n "$year_begin" ] && [ -n "$year_cumulative" ] && [ -n "$period_end" ]; then
        echo "✅ 关键财务数据字段完整"
        echo "   期初金额: $year_begin 万元"
        echo "   年累计清收: $year_cumulative 万元"
        echo "   期末余额: $period_end 万元"
        
        # 基本数学关系验证（使用bc进行浮点运算）
        if command -v bc >/dev/null 2>&1; then
            calculated_end=$(echo "$year_begin - $year_cumulative" | bc)
            difference=$(echo "$period_end - $calculated_end" | bc | sed 's/-//')
            
            if (( $(echo "$difference < 0.01" | bc -l) )); then
                echo "✅ 财务数据数学关系验证通过"
            else
                echo "⚠️  财务数据可能存在不一致（差异: $difference 万元）"
            fi
        else
            echo "⚠️  无法执行精确数学验证（缺少bc命令）"
        fi
    else
        echo "⚠️  关键财务数据字段不完整"
    fi
else
    echo "❌ 债权统计数据结构异常"
    echo "   响应: ${debt_stats_response:0:200}..."
fi

echo ""
echo "📋 6. 配置完整性验证"
echo "🔍 检查关键配置项..."

config_file="api-gateway/src/main/resources/application.yml"
if [ -f "$config_file" ]; then
    echo "✅ 主配置文件存在"
    
    # 检查数据库配置
    if grep -q "overdue_debt_db\|user_system\|kingdee" "$config_file"; then
        echo "✅ 数据库配置项存在"
    else
        echo "❌ 数据库配置项缺失"
    fi
    
    # 检查安全配置
    if grep -q "jwt.secret" "$config_file"; then
        echo "✅ JWT安全配置存在"
    else
        echo "❌ JWT安全配置缺失"
    fi
else
    echo "❌ 主配置文件缺失"
fi

echo ""
echo "🏁 回归测试完成"
echo ""
echo "📊 测试总结:"
echo "   如果所有项目都显示 ✅，则核心业务功能正常"
echo "   如果出现 ❌ 或 ⚠️，请检查对应功能"
echo ""
echo "💡 提示："
echo "   - ✅ 表示功能正常"
echo "   - ⚠️  表示功能可能有问题，需要关注"
echo "   - ❌ 表示功能异常，需要修复"
echo ""
echo "✅ 业务回归测试执行完毕！"
-- 表3、4、5累计处置金额修复效果验证脚本
-- 作者: Claude Code
-- 创建时间: 2025-08-12
-- 用途: 验证修复脚本执行后的数据正确性

SET @year = 2025;
SET @month = 2;

-- ===========================
-- 1. 修复效果总览
-- ===========================
SELECT '=== 修复效果总览 ===' as 验证项;

SELECT 
    '诉讼表' as 表名,
    COUNT(*) as 总记录数,
    COUNT(CASE WHEN 本年度累计回收 > 0 THEN 1 END) as 有累计回收记录数,
    ROUND(SUM(COALESCE(本年度累计回收, 0)), 2) as 累计回收总额,
    ROUND(AVG(COALESCE(本年度累计回收, 0)), 2) as 平均累计回收,
    MAX(本年度累计回收) as 最大累计回收,
    COUNT(CASE WHEN 本年度累计回收 IS NULL THEN 1 END) as 空值记录数
FROM 诉讼表 
WHERE 年份 = @year AND 月份 = @month

UNION ALL

SELECT 
    '非诉讼表' as 表名,
    COUNT(*) as 总记录数,
    COUNT(CASE WHEN 本年度累计回收 > 0 THEN 1 END) as 有累计回收记录数,
    ROUND(SUM(COALESCE(本年度累计回收, 0)), 2) as 累计回收总额,
    ROUND(AVG(COALESCE(本年度累计回收, 0)), 2) as 平均累计回收,
    MAX(本年度累计回收) as 最大累计回收,
    COUNT(CASE WHEN 本年度累计回收 IS NULL THEN 1 END) as 空值记录数
FROM 非诉讼表 
WHERE 年份 = @year AND 月份 = @month

UNION ALL

SELECT 
    '减值准备表' as 表名,
    COUNT(*) as 总记录数,
    COUNT(CASE WHEN 本年度累计回收 > 0 THEN 1 END) as 有累计回收记录数,
    ROUND(SUM(COALESCE(本年度累计回收, 0)), 2) as 累计回收总额,
    ROUND(AVG(COALESCE(本年度累计回收, 0)), 2) as 平均累计回收,
    MAX(本年度累计回收) as 最大累计回收,
    COUNT(CASE WHEN 本年度累计回收 IS NULL THEN 1 END) as 空值记录数
FROM 减值准备表 
WHERE 年份 = @year AND 月份 = @month;

-- ===========================
-- 2. 累计计算准确性验证
-- ===========================
SELECT '=== 累计计算准确性验证 ===' as 验证项;

-- 验证1月份的累计回收应该等于本月处置（如果有1月数据）
SELECT 
    '1月份准确性-诉讼表' as 验证项,
    COUNT(CASE WHEN ABS(COALESCE(本月处置债权, 0) - COALESCE(本年度累计回收, 0)) > 0.01 THEN 1 END) as 异常记录数,
    COUNT(*) as 总记录数
FROM 诉讼表 
WHERE 年份 = @year AND 月份 = 1;

SELECT 
    '1月份准确性-非诉讼表' as 验证项,
    COUNT(CASE WHEN ABS(COALESCE(本月处置债权, 0) - COALESCE(本年度累计回收, 0)) > 0.01 THEN 1 END) as 异常记录数,
    COUNT(*) as 总记录数
FROM 非诉讼表 
WHERE 年份 = @year AND 月份 = 1;

SELECT 
    '1月份准确性-减值准备表' as 验证项,
    COUNT(CASE WHEN ABS(COALESCE(本月处置债权, 0) - COALESCE(本年度累计回收, 0)) > 0.01 THEN 1 END) as 异常记录数,
    COUNT(*) as 总记录数
FROM 减值准备表 
WHERE 年份 = @year AND 月份 = 1;

-- 验证2月份的累计回收是否正确（应该是1-2月的处置总和）
WITH 累计验证_诉讼表 AS (
    SELECT 
        债权人, 债务人, 期间,
        SUM(CASE WHEN 月份 <= @month THEN COALESCE(本月处置债权, 0) ELSE 0 END) as 应该的累计,
        MAX(CASE WHEN 月份 = @month THEN COALESCE(本年度累计回收, 0) ELSE 0 END) as 实际的累计
    FROM 诉讼表 
    WHERE 年份 = @year
    GROUP BY 债权人, 债务人, 期间
)
SELECT 
    '2月份准确性-诉讼表' as 验证项,
    COUNT(CASE WHEN ABS(应该的累计 - 实际的累计) > 0.01 THEN 1 END) as 异常记录数,
    COUNT(*) as 检查记录数,
    ROUND(SUM(ABS(应该的累计 - 实际的累计)), 2) as 总差额
FROM 累计验证_诉讼表;

WITH 累计验证_非诉讼表 AS (
    SELECT 
        债权人, 债务人, 期间,
        SUM(CASE WHEN 月份 <= @month THEN COALESCE(本月处置债权, 0) ELSE 0 END) as 应该的累计,
        MAX(CASE WHEN 月份 = @month THEN COALESCE(本年度累计回收, 0) ELSE 0 END) as 实际的累计
    FROM 非诉讼表 
    WHERE 年份 = @year
    GROUP BY 债权人, 债务人, 期间
)
SELECT 
    '2月份准确性-非诉讼表' as 验证项,
    COUNT(CASE WHEN ABS(应该的累计 - 实际的累计) > 0.01 THEN 1 END) as 异常记录数,
    COUNT(*) as 检查记录数,
    ROUND(SUM(ABS(应该的累计 - 实际的累计)), 2) as 总差额
FROM 累计验证_非诉讼表;

WITH 累计验证_减值准备表 AS (
    SELECT 
        债权人, 债务人, 期间, 是否涉诉,
        SUM(CASE WHEN 月份 <= @month THEN COALESCE(本月处置债权, 0) ELSE 0 END) as 应该的累计,
        MAX(CASE WHEN 月份 = @month THEN COALESCE(本年度累计回收, 0) ELSE 0 END) as 实际的累计
    FROM 减值准备表 
    WHERE 年份 = @year
    GROUP BY 债权人, 债务人, 期间, 是否涉诉
)
SELECT 
    '2月份准确性-减值准备表' as 验证项,
    COUNT(CASE WHEN ABS(应该的累计 - 实际的累计) > 0.01 THEN 1 END) as 异常记录数,
    COUNT(*) as 检查记录数,
    ROUND(SUM(ABS(应该的累计 - 实际的累计)), 2) as 总差额
FROM 累计验证_减值准备表;

-- ===========================
-- 3. 跨表数据一致性检验
-- ===========================
SELECT '=== 跨表数据一致性检验 ===' as 验证项;

-- 检查减值准备表与处置表的数据是否匹配
WITH 跨表对比 AS (
    SELECT 
        COALESCE(a.债权人, b.债权人) as 债权人,
        COALESCE(a.债务人, b.债务人) as 债务人,
        COALESCE(a.期间, b.期间) as 期间,
        a.本月处置债权 as 减值表_处置,
        b.每月处置金额 as 处置表_处置,
        a.本年度累计回收 as 减值表_累计,
        ABS(COALESCE(a.本月处置债权, 0) - COALESCE(b.每月处置金额, 0)) as 处置差额
    FROM 减值准备表 a
    FULL OUTER JOIN 处置表 b ON (
        a.债权人 = b.债权人 
        AND a.债务人 = b.债务人 
        AND a.期间 = b.期间
        AND a.年份 = b.年份 
        AND a.月份 = b.月份
    )
    WHERE COALESCE(a.年份, b.年份) = @year AND COALESCE(a.月份, b.月份) = @month
)
SELECT 
    '减值表vs处置表一致性' as 验证项,
    COUNT(*) as 总对比记录,
    COUNT(CASE WHEN 处置差额 > 0.01 THEN 1 END) as 差异记录数,
    ROUND(AVG(处置差额), 4) as 平均差额,
    ROUND(MAX(处置差额), 2) as 最大差额
FROM 跨表对比;

-- ===========================
-- 4. 期间字段标准化检验
-- ===========================
SELECT '=== 期间字段标准化检验 ===' as 验证项;

-- 检查期间字段格式是否统一
SELECT 
    '期间格式检查' as 验证项,
    期间,
    '诉讼表' as 表名,
    COUNT(*) as 记录数
FROM 诉讼表 
WHERE 年份 = @year
  AND 期间 NOT REGEXP '^[0-9]{4}年(新增债权|430债权)$'
  AND 期间 NOT IN ('存量新增债权')
GROUP BY 期间
HAVING COUNT(*) > 0

UNION ALL

SELECT 
    '期间格式检查' as 验证项,
    期间,
    '非诉讼表' as 表名,
    COUNT(*) as 记录数
FROM 非诉讼表 
WHERE 年份 = @year
  AND 期间 NOT REGEXP '^[0-9]{4}年(新增债权|430债权)$'
  AND 期间 NOT IN ('存量新增债权')
GROUP BY 期间
HAVING COUNT(*) > 0

UNION ALL

SELECT 
    '期间格式检查' as 验证项,
    期间,
    '减值准备表' as 表名,
    COUNT(*) as 记录数
FROM 减值准备表 
WHERE 年份 = @year
  AND 期间 NOT REGEXP '^[0-9]{4}年(新增债权|430债权)$'
  AND 期间 NOT IN ('存量新增债权')
GROUP BY 期间
HAVING COUNT(*) > 0;

-- ===========================
-- 5. 修复质量评分
-- ===========================
SELECT '=== 修复质量评分 ===' as 验证项;

WITH 质量评估 AS (
    SELECT 
        -- 准确性评分 (40分)
        CASE 
            WHEN (
                (SELECT COUNT(CASE WHEN ABS(COALESCE(本月处置债权,0) - COALESCE(本年度累计回收,0)) > 0.01 THEN 1 END) 
                 FROM 诉讼表 WHERE 年份 = @year AND 月份 = 1) +
                (SELECT COUNT(CASE WHEN ABS(COALESCE(本月处置债权,0) - COALESCE(本年度累计回收,0)) > 0.01 THEN 1 END) 
                 FROM 非诉讼表 WHERE 年份 = @year AND 月份 = 1) +
                (SELECT COUNT(CASE WHEN ABS(COALESCE(本月处置债权,0) - COALESCE(本年度累计回收,0)) > 0.01 THEN 1 END) 
                 FROM 减值准备表 WHERE 年份 = @year AND 月份 = 1)
            ) = 0 THEN 40
            WHEN (SELECT COUNT(*) FROM 诉讼表 WHERE 年份 = @year AND 月份 = 1) + 
                 (SELECT COUNT(*) FROM 非诉讼表 WHERE 年份 = @year AND 月份 = 1) +
                 (SELECT COUNT(*) FROM 减值准备表 WHERE 年份 = @year AND 月份 = 1) = 0 THEN 35
            ELSE 20
        END as 准确性得分,
        
        -- 完整性评分 (30分) 
        CASE 
            WHEN (
                (SELECT COUNT(CASE WHEN 本年度累计回收 IS NULL THEN 1 END) 
                 FROM 诉讼表 WHERE 年份 = @year AND 月份 = @month) +
                (SELECT COUNT(CASE WHEN 本年度累计回收 IS NULL THEN 1 END) 
                 FROM 非诉讼表 WHERE 年份 = @year AND 月份 = @month) +
                (SELECT COUNT(CASE WHEN 本年度累计回收 IS NULL THEN 1 END) 
                 FROM 减值准备表 WHERE 年份 = @year AND 月份 = @month)
            ) = 0 THEN 30
            ELSE 15
        END as 完整性得分,
        
        -- 一致性评分 (30分)
        CASE 
            WHEN (
                SELECT COUNT(CASE WHEN ABS(COALESCE(a.本月处置债权,0) - COALESCE(b.每月处置金额,0)) > 0.01 THEN 1 END)
                FROM 减值准备表 a 
                LEFT JOIN 处置表 b ON (a.债权人=b.债权人 AND a.债务人=b.债务人 AND a.期间=b.期间 AND a.年份=b.年份 AND a.月份=b.月份)
                WHERE a.年份 = @year AND a.月份 = @month
            ) = 0 THEN 30
            ELSE 15
        END as 一致性得分
)
SELECT 
    '修复质量评分' as 评估项,
    准确性得分,
    完整性得分, 
    一致性得分,
    (准确性得分 + 完整性得分 + 一致性得分) as 总分,
    CASE 
        WHEN (准确性得分 + 完整性得分 + 一致性得分) >= 90 THEN '优秀'
        WHEN (准确性得分 + 完整性得分 + 一致性得分) >= 80 THEN '良好'
        WHEN (准确性得分 + 完整性得分 + 一致性得分) >= 70 THEN '合格'
        ELSE '需要重新修复'
    END as 评级,
    NOW() as 验证时间
FROM 质量评估;

-- ===========================
-- 6. 建议后续操作
-- ===========================
SELECT '=== 建议后续操作 ===' as 验证项;

SELECT 
    CASE 
        WHEN (
            SELECT COUNT(*)
            FROM (
                SELECT COUNT(CASE WHEN ABS(COALESCE(本月处置债权,0) - COALESCE(本年度累计回收,0)) > 0.01 THEN 1 END) as cnt 
                FROM 诉讼表 WHERE 年份 = @year AND 月份 = 1
                UNION ALL
                SELECT COUNT(CASE WHEN ABS(COALESCE(本月处置债权,0) - COALESCE(本年度累计回收,0)) > 0.01 THEN 1 END) 
                FROM 非诉讼表 WHERE 年份 = @year AND 月份 = 1
                UNION ALL
                SELECT COUNT(CASE WHEN ABS(COALESCE(本月处置债权,0) - COALESCE(本年度累计回收,0)) > 0.01 THEN 1 END) 
                FROM 减值准备表 WHERE 年份 = @year AND 月份 = 1
            ) t WHERE cnt > 0
        ) = 0 
        THEN '✅ 修复成功！可以重新生成Excel报表进行测试'
        ELSE '❌ 仍有问题，建议检查本月处置债权字段的数据源'
    END as 建议操作,
    
    '下一步: 运行 ExcelExportOverdueDebt 重新导出Excel，检查表3、4、5的累计处置金额是否正确显示' as 验证步骤;
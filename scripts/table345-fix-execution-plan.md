# 表3、4、5累计处置金额修复执行计划

## 📋 问题分析总结

### 根本原因
1. **数据同步延迟**：各表的"本月处置债权"字段可能未及时从处置表同步
2. **期间字段不一致**：累计计算的PARTITION BY中期间值格式不统一
3. **定时任务执行时序**：可能在数据同步完成前执行累计计算
4. **版本回退间接影响**：虽然不直接使用处置表JOIN，但依赖数据同步机制

## 🛠️ 修复方案（分阶段执行）

### 阶段1：数据诊断
```bash
# 执行数据诊断脚本
mysql -h localhost -P 3307 -u root -p123456 -D overdue_debt_db < scripts/diagnose-table345-data.sql
```

### 阶段2：备份当前数据
```bash
# 备份关键表数据
mysqldump -h localhost -P 3307 -u root -p123456 overdue_debt_db 诉讼表 非诉讼表 减值准备表 > backup_table345_$(date +%Y%m%d_%H%M%S).sql
```

### 阶段3：执行修复脚本
```bash
# 执行累计处置金额修复
mysql -h localhost -P 3307 -u root -p123456 -D overdue_debt_db < scripts/fix-table345-cumulative-disposal.sql
```

### 阶段4：验证修复效果
```bash
# 验证数据一致性
mysql -h localhost -P 3307 -u root -p123456 -D overdue_debt_db < scripts/verify-table345-fix.sql
```

### 阶段5：重新生成Excel报表测试
```bash
# 重新运行Excel导出，验证表3、4、5数据正确性
cd shared/data-processing && java -jar target/data-processing.jar
```

## 📊 风险评估矩阵

| 风险项 | 概率 | 影响 | 缓解措施 |
|--------|------|------|----------|
| 修复SQL执行失败 | 低 | 中 | 事前数据备份 + SQL语法验证 |
| 数据不一致加剧 | 中 | 高 | 分阶段执行 + 实时监控 |
| 影响其他功能 | 低 | 中 | 只修改累计字段，不影响业务逻辑 |
| 修复不彻底 | 中 | 中 | 多轮验证 + 回滚机制 |

## ⚡ 紧急修复步骤（15分钟内完成）

如果需要立即修复，按以下顺序执行：

1. **快速备份** (2分钟)
   ```bash
   git add . && git commit -m "checkpoint: 表3、4、5修复前数据快照 - $(date)"
   ```

2. **执行修复SQL** (5分钟)
   ```bash
   mysql -h localhost -P 3307 -u root -p123456 -D overdue_debt_db < scripts/fix-table345-cumulative-disposal.sql
   ```

3. **快速验证** (3分钟)
   ```bash
   # 检查修复后的数据总数
   mysql -h localhost -P 3307 -u root -p123456 -D overdue_debt_db -e "
   SELECT '诉讼表' as 表名, SUM(本年度累计回收) as 累计金额 FROM 诉讼表 WHERE 年份=2025 AND 月份=2
   UNION ALL 
   SELECT '非诉讼表', SUM(本年度累计回收) FROM 非诉讼表 WHERE 年份=2025 AND 月份=2
   UNION ALL
   SELECT '减值准备表', SUM(本年度累计回收) FROM 减值准备表 WHERE 年份=2025 AND 月份=2;"
   ```

4. **重新导出Excel验证** (5分钟)
   ```bash
   cd /Volumes/ExternalSSD-1T/08.program/FinancialSystem/shared/data-processing
   mvn exec:java -Dexec.mainClass="com.laoshu198838.export.ExcelExportOverdueDebt"
   ```

## 🔄 回滚计划

如果修复后出现问题：

1. **立即回滚数据库**
   ```bash
   mysql -h localhost -P 3307 -u root -p123456 -D overdue_debt_db < backup_table345_*.sql
   ```

2. **重置Git版本**
   ```bash
   git reset --hard HEAD~1
   ```

3. **重新分析问题**
   - 检查"本月处置债权"字段的数据源
   - 验证数据同步机制是否正常

## 📈 长期优化建议

1. **建立监控机制**
   - 每日自动检查三表数据一致性
   - 设置累计金额异常告警

2. **改进定时任务**
   - 确保数据同步完成后再执行累计计算
   - 增加任务执行状态监控

3. **数据同步优化**
   - 统一期间字段格式标准
   - 建立处置表到业务表的实时同步机制
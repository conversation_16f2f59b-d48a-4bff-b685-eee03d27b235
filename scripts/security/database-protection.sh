#!/bin/bash
# ===========================================
# 数据库操作安全防护脚本
# 文件: scripts/security/database-protection.sh
# 作用: 在执行任何数据库操作前进行安全检查
# ===========================================

SECURITY_LOG=".claude/security/database-operations.log"

# 创建安全日志目录
mkdir -p ".claude/security"

# 数据库操作安全检查函数
check_sql_safety() {
    local sql_command="$1"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    # 转换为大写进行检查
    local upper_sql=$(echo "$sql_command" | tr '[:lower:]' '[:upper:]')
    
    # 危险操作关键词列表
    local dangerous_keywords=(
        "DROP"
        "ALTER"
        "DELETE"
        "UPDATE"
        "INSERT"
        "TRUNCATE"
        "CREATE USER"
        "GRANT"
        "REVOKE"
    )
    
    # 检查是否包含危险操作
    for keyword in "${dangerous_keywords[@]}"; do
        if [[ "$upper_sql" =~ $keyword ]]; then
            echo "🚫 检测到危险的数据库操作: $keyword"
            echo "🚨 根据安全规则，此操作被禁止"
            echo "[$timestamp] BLOCKED: $sql_command" >> "$SECURITY_LOG"
            
            # 提示用户获得明确确认
            echo ""
            echo "如果确实需要执行此操作，请："
            echo "1. 说明操作原因和必要性"
            echo "2. 明确声明承担责任"
            echo "3. 输入完整确认短语: '我确认执行危险数据库操作并承担后果'"
            echo ""
            read -p "请输入确认短语: " confirmation
            
            if [[ "$confirmation" == "我确认执行危险数据库操作并承担后果" ]]; then
                echo "⚠️ 用户确认执行危险操作"
                echo "[$timestamp] USER_CONFIRMED: $sql_command" >> "$SECURITY_LOG"
                return 0
            else
                echo "❌ 确认短语不正确，操作被取消"
                echo "[$timestamp] USER_CANCELLED: $sql_command" >> "$SECURITY_LOG"
                return 1
            fi
        fi
    done
    
    # 安全操作记录
    echo "✅ 数据库操作安全检查通过"
    echo "[$timestamp] SAFE: $sql_command" >> "$SECURITY_LOG"
    return 0
}

# 快速安全检查（用于Bash命令执行前）
quick_sql_check() {
    local input="$1"
    local upper_input=$(echo "$input" | tr '[:lower:]' '[:upper:]')
    
    # 快速检查危险关键词
    if [[ "$upper_input" =~ (DROP|ALTER|DELETE|UPDATE|INSERT|TRUNCATE) ]]; then
        echo "🚫 检测到可能的危险数据库操作，请使用完整安全检查"
        return 1
    fi
    
    return 0
}

# 数据库连接安全提醒
database_safety_reminder() {
    cat << 'EOF'
🛡️ 数据库安全提醒 🛡️
==============================================
⚠️ 重要: 此系统受到数据库操作安全保护

🚫 被禁止的操作:
   • DROP (删除表/库)    • ALTER (修改结构)
   • DELETE (删除数据)   • UPDATE (修改数据)  
   • INSERT (插入数据)   • TRUNCATE (清空表)

✅ 允许的操作:
   • SELECT (查询)       • SHOW (显示)
   • DESCRIBE (描述)     • EXPLAIN (解释)

🔒 如需修改数据，必须获得明确的用户确认
==============================================
EOF
}

# 主函数
main() {
    case "$1" in
        "check")
            check_sql_safety "$2"
            ;;
        "quick")
            quick_sql_check "$2"
            ;;
        "reminder")
            database_safety_reminder
            ;;
        *)
            echo "用法: $0 {check|quick|reminder} [SQL命令]"
            echo "  check    - 完整安全检查"
            echo "  quick    - 快速检查"
            echo "  reminder - 显示安全提醒"
            exit 1
            ;;
    esac
}

# 如果脚本被直接执行
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
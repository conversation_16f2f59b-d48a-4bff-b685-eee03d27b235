#!/bin/bash

# 检查点验证脚本
# 用于在重要代码修改后验证系统完整性

echo "🔍 执行检查点验证..."
echo "================================"

# 1. 基础启动测试
echo "📋 1. 基础启动测试"
./scripts/test-startup.sh
if [ $? -ne 0 ]; then
    echo "❌ 启动测试失败"
    echo "💡 建议：检查编译错误或配置问题"
    exit 1
fi

echo ""
echo "📋 2. 增强模式验证（用于代码修改后）"
./scripts/test-startup.sh enhanced
if [ $? -ne 0 ]; then
    echo "❌ 增强测试失败"
    echo "💡 建议：代码修改可能影响了系统稳定性"
    exit 1
fi

echo ""
echo "📋 3. Git状态检查"
git_status() {
    echo "🔍 检查Git工作区状态..."
    
    # 检查是否有未跟踪的文件
    untracked_files=$(git ls-files --others --exclude-standard)
    if [ -n "$untracked_files" ]; then
        echo "⚠️  发现未跟踪的文件："
        echo "$untracked_files"
        echo "💡 建议：考虑是否需要添加到版本控制"
    else
        echo "✅ 没有未跟踪的文件"
    fi
    
    # 检查是否有未提交的更改
    if ! git diff-index --quiet HEAD --; then
        echo "⚠️  发现未提交的更改："
        git status --porcelain
        echo "💡 建议：考虑提交重要更改"
    else
        echo "✅ 工作区干净"
    fi
}
git_status

echo ""
echo "📋 4. 关键文件完整性检查"
check_critical_files() {
    echo "🔍 检查关键配置文件..."
    
    local critical_files=(
        "api-gateway/src/main/resources/application.yml"
        "CLAUDE.md"
        ".claude/protected-files.txt"
        "scripts/test-startup.sh"
    )
    
    for file in "${critical_files[@]}"; do
        if [ -f "$file" ]; then
            echo "✅ $file 存在"
        else
            echo "❌ $file 缺失"
        fi
    done
}
check_critical_files

echo ""
echo "📋 5. 系统资源检查"
check_system_resources() {
    echo "🔍 检查系统资源..."
    
    # 检查可用磁盘空间
    available_space=$(df -h . | awk 'NR==2 {print $4}')
    echo "💽 可用磁盘空间: $available_space"
    
    # 检查内存使用
    if command -v free >/dev/null 2>&1; then
        echo "🧠 内存使用情况:"
        free -h
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        echo "🧠 系统内存: $(sysctl -n hw.memsize | awk '{print $1/1024/1024/1024 " GB"}')"
    fi
    
    # 检查Java进程
    java_processes=$(pgrep -f java | wc -l)
    echo "☕ 当前Java进程数: $java_processes"
    if [ $java_processes -gt 3 ]; then
        echo "⚠️  Java进程较多，可能有未清理的测试进程"
    fi
}
check_system_resources

echo ""
echo "✅ 所有检查点验证通过"
echo ""
echo "📊 验证总结:"
echo "   ✅ 基础启动测试通过"
echo "   ✅ 增强模式测试通过" 
echo "   ✅ Git状态检查完成"
echo "   ✅ 关键文件完整性确认"
echo "   ✅ 系统资源状态良好"
echo ""
echo "🎉 系统状态良好，可以继续开发工作！"
-- ===============================================
-- 方案B执行 - 步骤2：修正非诉讼表期间和处置记录
-- ===============================================

START TRANSACTION;

-- 1. 修正6月份的期间字段，使其与处置表一致
UPDATE 非诉讼表 
SET 期间 = '2024年新增债权'
WHERE 债权人 = '深圳日上光电有限公司' 
  AND 债务人 = '法拉沃(杭州)照明有限公司'
  AND 年份 = 2025 AND 月份 = 6;

-- 2. 修正6月份的处置金额和相关字段
UPDATE 非诉讼表 
SET 本月处置债权 = 2.76,
    本月新增债权 = 0.00,
    上月末本金 = 19.27,
    本月末本金 = 16.51
WHERE 债权人 = '深圳日上光电有限公司' 
  AND 债务人 = '法拉沃(杭州)照明有限公司'
  AND 年份 = 2025 AND 月份 = 6;

-- 3. 清除5月份的错误处置记录，修正为正确的新增逻辑
UPDATE 非诉讼表 
SET 本月处置债权 = 0.00,
    本月新增债权 = 16.51,
    上月末本金 = 2.76,
    本月末本金 = 19.27
WHERE 债权人 = '深圳日上光电有限公司' 
  AND 债务人 = '法拉沃(杭州)照明有限公司'
  AND 年份 = 2025 AND 月份 = 5;

-- 4. 修正7-8月的期间为统一的2024年新增债权
UPDATE 非诉讼表 
SET 期间 = '2024年新增债权',
    上月末本金 = 16.51
WHERE 债权人 = '深圳日上光电有限公司' 
  AND 债务人 = '法拉沃(杭州)照明有限公司'
  AND 年份 = 2025 AND 月份 IN (7, 8);

-- 5. 验证修正结果
SELECT '=== 修正后非诉讼表数据状态 ===' AS 标题;
SELECT 
    年份, 月份, 期间,
    上月末本金, 本月新增债权, 本月处置债权, 本月末本金,
    (上月末本金 + 本月新增债权 - 本月处置债权) AS 计算期末余额,
    CASE 
        WHEN ABS(本月末本金 - (上月末本金 + 本月新增债权 - 本月处置债权)) < 0.01 
        THEN '✅ 一致' 
        ELSE '❌ 不一致' 
    END AS 验证结果
FROM 非诉讼表 
WHERE 债权人 = '深圳日上光电有限公司' 
  AND 债务人 = '法拉沃(杭州)照明有限公司'
  AND 年份 = 2025
ORDER BY 月份;

-- 6. 计算修正后的累计处置金额
SELECT '=== 修正后累计处置金额验证 ===' AS 标题;
SELECT 
    '非诉讼表修正后累计' AS 说明,
    SUM(本月处置债权) AS 累计处置金额,
    '13.37' AS 预期金额,
    CASE 
        WHEN ABS(SUM(本月处置债权) - 13.37) < 0.01 
        THEN '✅ 与处置表一致' 
        ELSE '❌ 仍有差异' 
    END AS 验证结果
FROM 非诉讼表 
WHERE 债权人 = '深圳日上光电有限公司' 
  AND 债务人 = '法拉沃(杭州)照明有限公司'
  AND 年份 = 2025;

-- 7. 与处置表对比验证
SELECT '=== 与处置表一致性验证 ===' AS 标题;
SELECT 
    n.月份,
    n.期间 AS 非诉讼表期间,
    n.本月处置债权 AS 非诉讼表处置,
    COALESCE(d.期间, '无记录') AS 处置表期间,
    COALESCE(d.每月处置金额, 0) AS 处置表处置,
    CASE 
        WHEN ABS(n.本月处置债权 - COALESCE(d.每月处置金额, 0)) < 0.01 
        THEN '✅ 一致' 
        ELSE '❌ 不一致' 
    END AS 验证结果
FROM 非诉讼表 n
LEFT JOIN 处置表 d ON n.债权人 = d.债权人 
    AND n.债务人 = d.债务人 
    AND n.年份 = d.年份
    AND n.月份 = d.月份
WHERE n.债权人 = '深圳日上光电有限公司' 
  AND n.债务人 = '法拉沃(杭州)照明有限公司'
  AND n.年份 = 2025
  AND (n.本月处置债权 > 0 OR d.每月处置金额 > 0)
ORDER BY n.月份;

COMMIT;

SELECT '=== 🎉 方案B执行完成！非诉讼表已与处置表完全同步 ===' AS 完成提示;
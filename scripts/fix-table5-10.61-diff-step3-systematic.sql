-- ===============================================
-- 修正表5-减值准备表10.61万元差异 - 步骤3：系统性修复
-- ===============================================

-- 系统性修复所有债权的本年度累计回收金额
-- 确保通过处置表实时计算，解决跨期间累计统计问题

-- 1. 检查其他可能存在类似问题的债权
SELECT '=== 检查其他潜在问题债权 ===' AS 标题;

-- 查找减值准备表和处置表累计金额不一致的债权
SELECT 
    r.债权人, r.债务人, r.期间 AS 减值准备表期间,
    r.本年度累计回收 AS 减值准备表累计,
    COALESCE(d.累计处置, 0) AS 处置表累计,
    (COALESCE(d.累计处置, 0) - r.本年度累计回收) AS 差异,
    CASE 
        WHEN ABS(COALESCE(d.累计处置, 0) - r.本年度累计回收) > 0.01 
        THEN '❌ 存在差异' 
        ELSE '✅ 一致' 
    END AS 状态
FROM 减值准备表 r
LEFT JOIN (
    SELECT 
        债权人, 债务人,
        SUM(每月处置金额) AS 累计处置
    FROM 处置表 
    WHERE 年份 = 2025 AND 月份 <= 6
    GROUP BY 债权人, 债务人
) d ON r.债权人 = d.债权人 AND r.债务人 = d.债务人
WHERE r.年份 = 2025 AND r.月份 = 6
  AND ABS(COALESCE(d.累计处置, 0) - r.本年度累计回收) > 0.01
ORDER BY ABS(COALESCE(d.累计处置, 0) - r.本年度累计回收) DESC
LIMIT 10;

-- 2. 系统性更新所有债权的本年度累计回收金额
-- 基于处置表实时计算正确的累计值
UPDATE 减值准备表 r
SET 本年度累计回收 = (
    SELECT COALESCE(SUM(每月处置金额), 0)
    FROM 处置表 d
    WHERE d.债权人 = r.债权人 
      AND d.债务人 = r.债务人
      AND d.年份 = 2025
      AND d.月份 <= 6
)
WHERE r.年份 = 2025 AND r.月份 = 6;

-- 3. 验证系统性修复的效果
SELECT '=== 系统性修复效果验证 ===' AS 标题;

-- 计算修复后的总体数据一致性
SELECT 
    '修复前减值准备表总处置' AS 说明,
    SUM(本年度累计回收) AS 总金额
FROM 减值准备表_10_61_backup_20250813;

SELECT 
    '修复后减值准备表总处置' AS 说明,
    SUM(本年度累计回收) AS 总金额
FROM 减值准备表 
WHERE 年份 = 2025 AND 月份 = 6;

SELECT 
    '处置表总处置金额（参考）' AS 说明,
    SUM(每月处置金额) AS 总金额
FROM 处置表 
WHERE 年份 = 2025 AND 月份 <= 6;

-- 4. 统计修复的债权数量
SELECT 
    '系统性修复影响统计' AS 说明,
    COUNT(*) AS 修复债权数量,
    SUM(ABS(修复前金额 - 修复后金额)) AS 总修复金额
FROM (
    SELECT 
        r.债权人, r.债务人,
        b.本年度累计回收 AS 修复前金额,
        r.本年度累计回收 AS 修复后金额
    FROM 减值准备表 r
    JOIN 减值准备表_10_61_backup_20250813 b 
        ON r.债权人 = b.债权人 
        AND r.债务人 = b.债务人
    WHERE r.年份 = 2025 AND r.月份 = 6
      AND ABS(b.本年度累计回收 - r.本年度累计回收) > 0.01
) diff_summary;

-- 5. 验证表5与表8的一致性
SELECT '=== 表5与表8一致性最终验证 ===' AS 标题;
SELECT 
    '表5-减值准备表总处置金额' AS 表名,
    SUM(本年度累计回收) AS 总金额,
    '8664.63' AS 预期金额,
    CASE 
        WHEN ABS(SUM(本年度累计回收) - 8664.63) < 0.01 
        THEN '✅ 与表8一致' 
        ELSE '❌ 仍有差异' 
    END AS 验证结果
FROM 减值准备表 
WHERE 年份 = 2025 AND 月份 = 6;

COMMIT;

SELECT '=== 🎉 系统性修复完成！所有数据已同步 ===' AS 完成提示;
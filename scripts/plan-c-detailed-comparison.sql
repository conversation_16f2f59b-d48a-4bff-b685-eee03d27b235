-- ===============================================
-- Plan C 详细对比分析：非诉讼表和减值准备表处置金额变化明细
-- ===============================================

-- 🎯 基准数据（不变的真实记录）
SELECT '=== 🎯 基准数据对照表 ===' AS 标题;

SELECT '处置表基准' AS 表名, 4 AS 月份, 10.61 AS 金额, '2024年新增债权' AS 期间, '真实处置记录' AS 说明
UNION ALL
SELECT '处置表基准', 6, 2.76, '2024年新增债权', '真实处置记录'
UNION ALL
SELECT '新增表基准', 5, 11.49, '2025年新增债权', '真实新增记录'
UNION ALL  
SELECT '新增表基准', 6, 5.02, '2025年新增债权', '真实新增记录'
ORDER BY 表名, 月份;

-- 📊 非诉讼表：修正前 vs 修正后
SELECT '=== 📊 非诉讼表处置金额变化对比 ===' AS 标题;

SELECT 
    月份,
    '修正前' AS 状态,
    本月处置债权 AS 本月处置,
    期间,
    本月末本金 AS 期末余额,
    CASE 
        WHEN 月份 = 5 THEN '❌ 错误：处置2.76应该在6月'
        WHEN 月份 = 6 THEN '❌ 错误：只处置0.01，应该是2.76'
        WHEN 月份 = 4 THEN '✅ 正确：4月处置10.61'
        ELSE '✅ 正确'
    END AS 问题分析
FROM 非诉讼表 
WHERE 债权人 = '深圳日上光电有限公司' 
  AND 债务人 = '法拉沃(杭州)照明有限公司'
  AND 年份 = 2025

UNION ALL

-- 修正后的预期状态
SELECT 1, '修正后', 0.00, '2024年新增债权', 13.37, '✅ 保持不变'
UNION ALL
SELECT 2, '修正后', 0.00, '2024年新增债权', 13.37, '✅ 保持不变'
UNION ALL
SELECT 3, '修正后', 0.00, '2024年新增债权', 13.37, '✅ 保持不变' 
UNION ALL
SELECT 4, '修正后', 10.61, '2024年新增债权', 2.76, '✅ 保持不变'
UNION ALL
SELECT 5, '修正后', 0.00, '2024年新增债权', 14.25, '🔧 修正：清除错误处置，期末=2.76+11.49'
UNION ALL
SELECT 6, '修正后', 2.76, '2024年新增债权', 16.51, '🔧 修正：加入正确处置，期末=14.25+5.02-2.76'
UNION ALL
SELECT 7, '修正后', 0.00, '2024年新增债权', 16.51, '🔧 修正：期间统一'
UNION ALL
SELECT 8, '修正后', 0.00, '2024年新增债权', 16.51, '🔧 修正：期间统一'

ORDER BY 月份, 状态;

-- 📈 减值准备表：修正前 vs 修正后
SELECT '=== 📈 减值准备表处置金额变化对比 ===' AS 标题;

SELECT 
    月份,
    '修正前' AS 状态,
    COALESCE(本月处置债权, 0) AS 本月处置,
    本年度累计回收 AS 累计回收,
    期间,
    本月末债权余额 AS 期末余额,
    CASE 
        WHEN 月份 = 5 THEN '❌ 错误：本月处置2.76应该在6月'
        WHEN 月份 = 6 THEN '❌ 错误：累计回收缺少4月的10.61'
        WHEN 月份 >= 6 THEN '❌ 错误：期间应该是2024年新增债权'
        ELSE '✅ 正确'
    END AS 问题分析
FROM 减值准备表 
WHERE 债权人 = '深圳日上光电有限公司' 
  AND 债务人 = '法拉沃(杭州)照明有限公司'
  AND 年份 = 2025

UNION ALL

-- 修正后的预期状态
SELECT 1, '修正后', 0.00, 0.00, '2024年新增债权', 13.37, '✅ 保持不变'
UNION ALL
SELECT 2, '修正后', 0.00, 0.00, '2024年新增债权', 13.37, '✅ 保持不变'
UNION ALL
SELECT 3, '修正后', 0.00, 0.00, '2024年新增债权', 13.37, '✅ 保持不变'
UNION ALL
SELECT 4, '修正后', 10.61, 10.61, '2024年新增债权', 2.76, '✅ 保持不变'
UNION ALL
SELECT 5, '修正后', 0.00, 10.61, '2024年新增债权', 14.25, '🔧 修正：清除错误处置，期末=2.76+11.49'
UNION ALL
SELECT 6, '修正后', 2.76, 13.37, '2024年新增债权', 16.51, '🔧 修正：本月处置2.76，累计=10.61+2.76'
UNION ALL
SELECT 7, '修正后', 0.00, 13.37, '2024年新增债权', 16.51, '🔧 修正：期间统一，累计保持13.37'
UNION ALL
SELECT 8, '修正后', 0.00, 13.37, '2024年新增债权', 16.51, '🔧 修正：期间统一，累计保持13.37'

ORDER BY 月份, 状态;

-- 🔍 逻辑验证：数学关系检查
SELECT '=== 🔍 数学逻辑验证 ===' AS 标题;

SELECT 
    '非诉讼表5月' AS 检查项,
    '2.76(上月末) + 11.49(新增) - 0.00(处置) = 14.25(期末)' AS 公式,
    CASE WHEN ABS(2.76 + 11.49 - 0.00 - 14.25) < 0.01 THEN '✅ 一致' ELSE '❌ 不一致' END AS 验证结果
UNION ALL
SELECT 
    '非诉讼表6月',
    '14.25(上月末) + 5.02(新增) - 2.76(处置) = 16.51(期末)',
    CASE WHEN ABS(14.25 + 5.02 - 2.76 - 16.51) < 0.01 THEN '✅ 一致' ELSE '❌ 不一致' END
UNION ALL
SELECT 
    '减值准备表6月',
    '14.25(期初) + 5.02(新增) - 2.76(处置) = 16.51(期末)',
    CASE WHEN ABS(14.25 + 5.02 - 2.76 - 16.51) < 0.01 THEN '✅ 一致' ELSE '❌ 不一致' END
UNION ALL
SELECT 
    '累计处置验证',
    '4月10.61 + 6月2.76 = 13.37(总累计)',
    CASE WHEN ABS(10.61 + 2.76 - 13.37) < 0.01 THEN '✅ 一致' ELSE '❌ 不一致' END;

-- 🎊 关键修正汇总
SELECT '=== 🎊 Plan C 关键修正汇总 ===' AS 标题;

SELECT 
    '非诉讼表修正' AS 修正项,
    '5月：处置2.76→0.00，期末11.49→14.25，期间改为2024年新增债权' AS 修正内容
UNION ALL
SELECT 
    '非诉讼表修正',
    '6月：处置0.01→2.76，上月末11.49→14.25，期间改为2024年新增债权'
UNION ALL
SELECT 
    '减值准备表修正',
    '5月：本月处置2.76→0.00，期末11.49→14.25，期间改为2024年新增债权'
UNION ALL
SELECT 
    '减值准备表修正',
    '6月：本月处置0.00→2.76，累计回收2.76→13.37，期间改为2024年新增债权'
UNION ALL
SELECT 
    '核心目标',
    '✅ 保持期末余额16.51万元不变'
UNION ALL
SELECT 
    '核心目标',
    '✅ 基于新增表真实数据：5月11.49，6月5.02'
UNION ALL
SELECT 
    '核心目标',
    '✅ 基于处置表真实数据：4月10.61，6月2.76';
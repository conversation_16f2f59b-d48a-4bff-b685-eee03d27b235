-- ===============================================
-- Plan C 执行 - 步骤1：完整备份（确保可回撤）
-- ===============================================

START TRANSACTION;

-- 1. 备份非诉讼表法拉沃数据
DROP TABLE IF EXISTS 非诉讼表_farawo_plan_c_backup_20250814;
CREATE TABLE 非诉讼表_farawo_plan_c_backup_20250814 AS
SELECT * FROM 非诉讼表 
WHERE 债权人 = '深圳日上光电有限公司' 
  AND 债务人 = '法拉沃(杭州)照明有限公司'
  AND 年份 = 2025;

-- 2. 备份减值准备表法拉沃数据
DROP TABLE IF EXISTS 减值准备表_farawo_plan_c_backup_20250814;
CREATE TABLE 减值准备表_farawo_plan_c_backup_20250814 AS
SELECT * FROM 减值准备表 
WHERE 债权人 = '深圳日上光电有限公司' 
  AND 债务人 = '法拉沃(杭州)照明有限公司'
  AND 年份 = 2025;

-- 3. 验证备份数据
SELECT '=== 📁 备份验证 ===' AS 标题;

SELECT 
    '非诉讼表备份' AS 表名,
    COUNT(*) AS 记录数,
    SUM(本月处置债权) AS 累计处置,
    MAX(本月末本金) AS 最终期末余额
FROM 非诉讼表_farawo_plan_c_backup_20250814;

SELECT 
    '减值准备表备份' AS 表名,
    COUNT(*) AS 记录数,
    MAX(本年度累计回收) AS 最大累计回收,
    MAX(本月末债权余额) AS 最终期末余额
FROM 减值准备表_farawo_plan_c_backup_20250814;

-- 4. 显示修正前状态
SELECT '=== 📊 修正前数据状态 ===' AS 标题;

SELECT 
    月份, 本月处置债权, 期间, 本月末本金, '非诉讼表-修正前' AS 来源
FROM 非诉讼表 
WHERE 债权人 = '深圳日上光电有限公司' 
  AND 债务人 = '法拉沃(杭州)照明有限公司'
  AND 年份 = 2025
  AND (本月处置债权 > 0 OR 月份 IN (5,6))
ORDER BY 月份;

SELECT 
    月份, 
    COALESCE(本月处置债权, 0) AS 本月处置,
    本年度累计回收, 
    期间, 
    本月末债权余额, 
    '减值准备表-修正前' AS 来源
FROM 减值准备表 
WHERE 债权人 = '深圳日上光电有限公司' 
  AND 债务人 = '法拉沃(杭州)照明有限公司'
  AND 年份 = 2025
  AND 月份 IN (4,5,6)
ORDER BY 月份;

SELECT '备份完成，准备执行Plan C修正' AS 状态;

COMMIT;
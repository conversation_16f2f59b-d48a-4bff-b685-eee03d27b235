# FinancialSystem 生产环境 Docker Compose 配置
# 已修复AMD64架构兼容性问题

services:
  # MySQL数据库服务 - 注释掉，使用本地MySQL
  # mysql:
  #   image: mysql:8.0
  #   platform: linux/amd64
  #   container_name: financial-mysql
  #   pull_policy: never  # 禁止拉取，仅使用本地镜像
  #   environment:
  #     MYSQL_ROOT_PASSWORD: Zlb&198838
  #     MYSQL_CHARACTER_SET_SERVER: utf8mb4
  #     MYSQL_COLLATION_SERVER: utf8mb4_unicode_ci
  #   ports:
  #     - "3306:3306"
  #   volumes:
  #     - mysql_data:/var/lib/mysql
  #     - ./init-scripts:/docker-entrypoint-initdb.d:ro
  #   command: --default-authentication-plugin=mysql_native_password
  #   restart: unless-stopped
  #   healthcheck:
  #     test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
  #     timeout: 20s
  #     retries: 10

  # 后端构建服务
  backend-builder:
    image: maven:3.9-eclipse-temurin-21
    platform: linux/amd64
    container_name: financial-backend-builder
    pull_policy: never  # 禁止拉取，仅使用本地镜像
    volumes:
      - .:/app
      - maven_cache:/root/.m2  # Maven本地仓库缓存
    working_dir: /app
    command: >
      sh -c "
        echo '开始构建后端项目...' &&
        mvn clean package -DskipTests -pl api-gateway -am &&
        echo '构建完成，JAR文件位置:' &&
        ls -la api-gateway/target/api-gateway-*.jar &&
        echo '构建服务完成，等待后端服务启动...'
      "
    # depends_on:
    #   mysql:
    #     condition: service_healthy

  # 后端运行服务
  backend:
    image: openjdk:21-jdk
    platform: linux/amd64
    container_name: financial-backend
    pull_policy: never  # 禁止拉取，仅使用本地镜像
    volumes:
      - ./api-gateway/target:/app
    working_dir: /app
    command: >
      sh -c "
        echo '等待JAR文件生成...' &&
        while [ ! -f api-gateway-1.0-SNAPSHOT.jar ]; do sleep 2; done &&
        echo '开始启动后端服务...' &&
        java -jar api-gateway-1.0-SNAPSHOT.jar
      "
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=production
      - SPRING_DATASOURCE_PRIMARY_URL=****************************************************************************************************************************************************************************
      - SPRING_DATASOURCE_SECONDARY_URL=************************************************************************************************************************************
      - SPRING_DATASOURCE_USER_SYSTEM_URL=**********************************************************************************************************************************************************************
    depends_on:
      backend-builder:
        condition: service_completed_successfully
      # mysql:
      #   condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 5

  # 前端构建服务
  frontend-builder:
    image: node:18-alpine
    platform: linux/amd64
    container_name: financial-frontend-builder
    pull_policy: never  # 禁止拉取，仅使用本地镜像
    volumes:
      - .:/app
      - node_modules_cache:/app/FinancialSystem-web/node_modules
    working_dir: /app/FinancialSystem-web
    command: >
      sh -c "
        echo '开始构建前端项目...' &&
        npm install &&
        npm run build &&
        echo '前端构建完成'
      "
    environment:
      - REACT_APP_API_URL=/api
      - NODE_ENV=production
    depends_on:
      - backend

  # Nginx Web服务器
  nginx:
    image: nginx:alpine
    platform: linux/amd64
    container_name: financial-nginx
    pull_policy: never  # 禁止拉取，仅使用本地镜像
    volumes:
      - ./FinancialSystem-web/build:/usr/share/nginx/html:ro
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    ports:
      - "8044:8044"
      - "80:80"
    depends_on:
      - frontend-builder
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  mysql_data:
  maven_cache:
  node_modules_cache:

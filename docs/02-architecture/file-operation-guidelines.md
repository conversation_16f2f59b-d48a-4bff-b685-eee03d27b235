# 文件操作规范指南 (File Operation Guidelines)

## 📋 目录
- [1. 文件分类和操作规范](#1-文件分类和操作规范)
- [2. 具体操作指令模板](#2-具体操作指令模板)
- [3. 安全检查机制](#3-安全检查机制)
- [4. 实战操作示例](#4-实战操作示例)
- [5. 故障排除和回滚](#5-故障排除和回滚)

---

## 1. 文件分类和操作规范

### 🔴 绝对保护文件 (Level 4)

#### 数据库相关文件
```yaml
文件模式: "**/*.sql", "**/schema/**", "**/migrations/**"
保护原因: 数据库结构变更影响整个系统
操作规范:
  ❌ 完全禁止: 任何修改、删除操作
  ⚠️ 紧急授权: 仅在数据库故障时允许
  ✅ 允许操作: 只能新增脚本文件
  
授权模板:
  "紧急授权修改数据库脚本，生产环境数据库故障，我已做完整备份"
  
验证要求:
  - 修改前: 必须有完整数据库备份
  - 修改后: 必须验证数据完整性
  - 回滚方案: 准备回滚SQL脚本
```

#### 核心配置文件
```yaml
文件模式: "**/application*.yml", "**/pom.xml", "**/package.json"
保护原因: 配置错误导致系统无法启动
操作规范:
  ❌ 禁止删除: 任何现有配置项
  ❌ 禁止修改: 数据库连接、安全密钥
  ⚠️ 谨慎添加: 新配置项需要验证
  ✅ 允许操作: 注释添加、格式调整

标准操作模板:
```
请按照以下约束修改配置文件 [文件路径]：

🎯 **修改目标**：
- 添加配置项：[具体配置名=值]
- 修改原因：[详细说明业务需要]

🚫 **严格禁止**：
- 修改现有数据库连接配置
- 修改JWT密钥或安全相关配置
- 删除任何现有配置项
- 修改端口号（除非明确要求）

📋 **验证要求**：
- 修改后运行：./scripts/test-startup.sh
- 确保应用正常启动
- 验证新配置项生效
- 确认现有功能正常

🔄 **操作步骤**：
1. 先备份原配置文件
2. 添加新配置项到指定位置
3. 立即测试应用启动
4. 如果失败立即回滚
```

#### 核心实体类
```yaml
文件模式: "**/entity/*.java", "**/*Entity.java", "**/model/*.java"
保护原因: 实体变更影响数据库映射和业务逻辑
操作规范:
  ❌ 禁止删除: 现有字段、主键、外键关系
  ❌ 禁止修改: 字段类型、注解、约束
  ⚠️ 谨慎添加: 新字段需要考虑数据库兼容性
  ✅ 允许操作: 添加注释、getter/setter

实体修改模板:
```
请按照以下约束修改实体类 [Entity类名]：

🎯 **修改目标**：
- 添加字段：[字段名] [类型] [用途说明]
- 业务需求：[详细说明为什么需要这个字段]

🚫 **严格禁止**：
- 删除任何现有字段
- 修改现有字段的类型或注解
- 修改主键字段
- 删除或修改外键关系
- 修改表名(@Table注解)

✅ **允许操作**：
- 添加新字段到类末尾
- 为新字段添加@Column注解
- 添加相应的getter/setter
- 添加字段注释说明

📋 **验证要求**：
- 确保新字段有默认值或允许null
- 运行：./scripts/test-startup.sh
- 验证JPA映射正确
- 确认相关API正常工作

🔄 **数据库兼容性**：
- 新字段必须兼容现有数据
- 考虑添加数据库迁移脚本
- 验证不影响现有查询
```

### 🟠 严格控制文件 (Level 3)

#### 核心业务服务
```yaml
文件模式: "**/service/*Service.java", "**/business/**/*.java"
保护原因: 业务逻辑错误影响核心功能
操作规范:
  ✅ 允许添加: 新的public方法
  ⚠️ 谨慎修改: 现有方法的内部实现
  ❌ 禁止修改: 方法签名、返回类型
  ❌ 禁止删除: 任何public方法

Service修改模板:
```
请按照以下约束修改Service类 [Service类名]：

🎯 **修改目标**：
- 修复问题：[具体bug描述]
- 或添加方法：[方法名] [用途说明]

🚫 **严格禁止**：
- 修改任何现有public方法的签名
- 删除任何现有public方法
- 修改方法返回类型
- 修改@Transactional等重要注解

✅ **允许操作**：
- 修复现有方法内部的bug
- 添加新的public方法
- 添加private辅助方法
- 优化算法和逻辑实现

📋 **验证要求**：
- 修改后运行：./scripts/test-startup.sh
- 验证相关Controller接口正常
- 确认事务处理正确
- 测试业务流程完整性

🔄 **实施步骤**：
1. 先分析现有方法的调用关系
2. 确认修改不影响其他模块
3. 实施修改并添加详细注释
4. 立即运行测试验证
```

#### API控制器
```yaml
文件模式: "**/controller/*.java", "**/*Controller.java"
保护原因: API变更影响前端和外部系统
操作规范:
  ✅ 允许添加: 新的endpoint
  ⚠️ 谨慎修改: 响应格式、状态码
  ❌ 禁止修改: URL路径、请求参数结构
  ❌ 禁止删除: 现有endpoint

Controller修改模板:
```
请按照以下约束修改Controller类 [Controller类名]：

🎯 **修改目标**：
- 修复API问题：[具体问题描述]
- 或添加接口：[新接口用途]

🚫 **严格禁止**：
- 修改现有API的URL路径
- 修改现有API的请求参数结构
- 修改现有API的响应格式
- 删除任何现有的endpoint
- 修改HTTP方法类型(GET/POST/PUT/DELETE)

✅ **允许操作**：
- 修复现有endpoint内部逻辑
- 添加新的endpoint
- 添加参数验证逻辑
- 优化异常处理
- 添加日志记录

📋 **验证要求**：
- 测试所有相关API接口
- 确认响应格式保持兼容
- 验证前端调用正常
- 运行：./scripts/test-startup.sh

🔄 **API兼容性检查**：
1. 修改前记录现有API响应格式
2. 修改后对比响应格式
3. 确保向后兼容
4. 更新API文档（如果有新增）
```

### 🟡 专家审核文件 (Level 2)

#### 业务组件和工具类
```yaml
文件模式: "**/component/*.java", "**/util/*.java", "**/helper/*.java"
保护原因: 组件修改可能影响多个调用方
操作规范:
  ✅ 允许重构: 内部实现优化
  ✅ 允许添加: 新方法和功能
  ⚠️ 保持兼容: 公共接口不变
  ❌ 禁止删除: 现有public方法

工具类重构模板:
```
请按照以下约束重构工具类 [类名]：

🎯 **重构目标**：
- 性能优化：[具体优化目标]
- 代码清理：[具体清理内容]
- 功能增强：[新增功能说明]

🚫 **兼容性约束**：
- 保持所有现有public方法签名不变
- 保持方法返回值格式不变
- 保持异常处理行为一致
- 不删除任何现有public方法

✅ **允许操作**：
- 重构方法内部实现
- 提取私有方法
- 优化算法和数据结构
- 添加新的public方法
- 改进错误处理
- 添加详细注释

📋 **验证要求**：
- 运行所有相关单元测试
- 验证调用方功能正常
- 性能测试（如果涉及性能优化）
- 运行：./scripts/test-startup.sh

🔄 **重构步骤**：
1. 识别所有调用方
2. 重构内部实现，保持接口不变
3. 验证每个调用方功能正常
4. 性能对比测试（如适用）
```

### 🟢 智能协助文件 (Level 1)

#### DTO和数据传输类
```yaml
文件模式: "**/dto/*.java", "**/*DTO.java", "**/*Request.java", "**/*Response.java"
保护原因: DTO变更可能影响API序列化
操作规范:
  ✅ 允许添加: 新字段、新DTO类
  ✅ 允许修改: 字段注解、验证规则
  ⚠️ 保持兼容: JSON序列化格式
  ❌ 谨慎删除: 现有字段

DTO修改模板:
```
请修改DTO类 [DTO类名]：

🎯 **修改目标**：
- 添加字段：[字段名] [类型] [用途]
- 业务需求：[详细说明]

✅ **允许操作**：
- 添加新字段到类中
- 为字段添加验证注解(@NotNull, @Size等)
- 添加Jackson序列化注解
- 添加getter/setter方法
- 添加字段注释

⚠️ **兼容性注意**：
- 新字段应该有合理的默认值
- 考虑JSON向后兼容性
- 确保前端能处理新字段
- 保持现有字段不变

📋 **验证要求**：
- 测试相关API的JSON序列化
- 验证前端接口调用正常
- 确认数据验证规则生效
- 运行：./scripts/test-startup.sh
```

### ⚪ 自由操作文件 (Level 0)

#### 测试文件
```yaml
文件模式: "**/test/**/*.java", "**/*Test.java", "**/*.test.js"
保护原因: 无需保护，鼓励完善测试
操作规范:
  ✅ 完全自由: 任意增删改
  ✅ 鼓励添加: 更多测试用例
  ✅ 鼓励完善: 测试覆盖率

测试文件修改模板:
```
请完善测试文件 [测试类名]：

🎯 **测试目标**：
- 测试功能：[具体功能模块]
- 覆盖场景：[正常/异常/边界情况]

✅ **完全自由操作**：
- 添加新测试方法
- 修改现有测试逻辑
- 删除过时的测试
- 重构测试代码结构
- 添加测试数据和mock
- 改进断言和验证

📋 **质量要求**：
- 测试用例命名清晰
- 包含正常和异常场景
- 添加必要的注释说明
- 确保测试的独立性

🔄 **测试最佳实践**：
1. 遵循AAA模式(Arrange-Act-Assert)
2. 使用有意义的测试数据
3. 测试用例应该简单明确
4. 添加性能测试（如适用）
```

---

## 2. 具体操作指令模板

### 🐛 Bug修复指令模板

#### 低风险Bug修复 (Level 1-2)
```
🐛 **Bug修复任务**

请修复以下Bug：[详细bug描述]

🎯 **问题定位**：
- 错误现象：[用户报告的具体错误]
- 影响范围：[哪些功能受影响]
- 预期行为：[正确的行为应该是什么]

📍 **修复范围**：
- 问题文件：[具体文件路径]
- 问题方法：[具体类.方法名]
- 修复类型：[逻辑错误/参数问题/异常处理]

🚫 **修复约束**：
- 仅修改上述指定文件
- 保持方法签名不变
- 不修改任何配置文件
- 不影响其他功能模块

📋 **验证步骤**：
1. 修复后运行：./scripts/test-startup.sh
2. 重现bug场景，确认已修复
3. 验证相关功能正常
4. 确认无新增问题

🔄 **实施计划**：
1. 先分析代码，定位问题根因
2. 说明修复方案，等我确认
3. 实施修复，添加详细注释
4. 立即验证修复效果
```

#### 高风险Bug修复 (Level 3)
```
⚠️ **高风险Bug修复** - 涉及核心业务逻辑

🐛 **严重问题**：[bug详细描述]

🔒 **特殊流程**（高风险）：
- 先运行：./scripts/ai-safe-backup.sh "fix-[bug名称]"
- 创建分支：feature/ai-hotfix-[描述]-[日期]
- 修改前检查点：git commit -m "checkpoint: 修复前状态"

📍 **精确修复范围**：
- 核心问题文件：[具体文件路径]
- 核心问题方法：[类名.方法名]
- 根本原因：[详细的问题分析]
- 影响评估：[可能影响的其他模块]

🚫 **严格约束**：
- 最小影响原则：只修改必要的代码
- 保持接口兼容性：不改变方法签名
- 保持数据一致性：确保数据完整性
- 保持配置稳定：不修改配置文件

📊 **全面验证**：
- 基础测试：./scripts/test-startup.sh
- 回归测试：./scripts/regression-test.sh
- 业务验证：手动测试核心业务流程
- 性能验证：确认性能无回退

⏰ **回滚预案**：
- 问题检测：如何快速发现问题
- 回滚触发：什么情况下立即回滚
- 回滚操作：git reset --hard [checkpoint]
- 影响分析：回滚后的影响评估

🔄 **分步执行**：
1. 创建安全备份和检查点
2. 深入分析问题根因
3. 设计最小影响修复方案
4. 获得修复方案确认
5. 执行修复，实时监控
6. 全面验证和测试
7. 准备详细修复报告
```

### ✨ 新功能开发指令模板

#### 新功能开发 (Level 1 - 多Agent协作)
```
✨ **新功能开发任务**

请按照Planning Agent流程开发以下功能：[功能描述]

🎯 **功能需求**：
- 功能名称：[具体功能名称]
- 业务价值：[为什么需要这个功能]
- 用户场景：[用户如何使用这个功能]
- 成功标准：[功能完成的验收标准]

🤖 **多Agent协作流程**：
Phase 1: 需求分析专家 + 技术设计专家 (并行)
- 需求分析专家：深入分析业务需求和用户场景
- 技术设计专家：评估技术可行性和架构设计

Phase 2: 计划制定专家
- 基于Phase 1结果制定详细实施计划
- 识别风险和依赖关系
- 制定时间计划和里程碑

Phase 3: 用户确认
- 展示完整的需求、设计、计划
- 等待用户确认和批准

Phase 4: 实施执行专家
- 按计划执行具体开发任务
- 分模块实施，每步验证

🏗️ **架构约束**：
- 新增文件位置：[指定包/目录结构]
- 集成方式：[如何与现有系统集成]
- 命名规范：[遵循现有命名约定]
- 依赖管理：[使用现有依赖，避免新增]

🚫 **开发约束**：
- 不修改任何现有文件（除了集成点）
- 不修改现有API接口
- 不修改数据库结构
- 不修改核心配置文件

📋 **质量要求**：
- 代码规范：遵循项目编码标准
- 测试要求：单元测试覆盖率 >= 80%
- 文档要求：添加必要的代码注释
- 性能要求：不影响现有功能性能

🔄 **开发步骤**：
1. 自动启动需求分析专家和技术设计专家
2. 等待分析和设计完成
3. 启动计划制定专家制定实施计划
4. 向用户展示完整方案
5. 获得确认后启动实施执行专家
6. 分模块实施，每步验证和测试
```

### 🔄 重构优化指令模板

#### 性能优化重构 (Level 2)
```
🔄 **性能优化重构任务**

请按照以下约束优化性能：[性能问题描述]

🎯 **优化目标**：
- 性能问题：[具体的性能瓶颈]
- 优化目标：[期望达到的性能指标]
- 影响范围：[哪些模块/功能受影响]

📊 **性能基准**：
- 当前性能：[具体的性能数据]
- 目标性能：[期望的性能改进]
- 测试场景：[如何测量性能]

🔒 **重构约束**：
- 功能兼容：所有现有功能必须保持不变
- 接口稳定：所有public接口签名不变
- 配置不变：不修改任何配置参数
- 数据一致：确保数据处理逻辑一致

✅ **允许的优化**：
- 算法优化：改进算法复杂度
- 数据结构优化：使用更高效的数据结构
- 查询优化：优化数据库查询
- 缓存策略：添加合理的缓存
- 并发优化：改进并发处理

📋 **验证要求**：
- 功能验证：所有功能测试通过
- 性能验证：性能指标达到预期
- 兼容性验证：API接口响应格式一致
- 稳定性验证：长时间运行稳定

🔄 **重构步骤**：
1. 性能瓶颈分析和确认
2. 设计优化方案，保持兼容性
3. 实施优化，保留原逻辑注释
4. 性能对比测试
5. 全面功能验证
6. 长时间稳定性测试
```

---

## 3. 安全检查机制

### 🔍 操作前检查清单

```bash
# 创建操作前检查脚本
cat > scripts/pre-operation-check.sh << 'EOF'
#!/bin/bash

echo "🔍 执行操作前安全检查..."

# 1. 系统状态检查
echo "📋 1. 检查系统当前状态"
./scripts/test-startup.sh > /tmp/pre-check.log 2>&1
if [ $? -ne 0 ]; then
    echo "❌ 系统当前状态异常，不建议进行修改"
    echo "请先修复现有问题再进行新的修改"
    exit 1
else
    echo "✅ 系统当前状态正常"
fi

# 2. 工作区状态检查
echo "📋 2. 检查Git工作区状态"
if [[ -n $(git status --porcelain) ]]; then
    echo "⚠️  工作区有未提交的更改"
    echo "建议先提交或暂存当前更改"
    git status --short
else
    echo "✅ 工作区状态干净"
fi

# 3. 分支检查
echo "📋 3. 检查当前分支"
current_branch=$(git branch --show-current)
if [[ "$current_branch" == "main" || "$current_branch" == "master" ]]; then
    echo "⚠️  当前在主分支 ($current_branch)"
    echo "建议创建功能分支进行修改"
else
    echo "✅ 当前在功能分支: $current_branch"
fi

# 4. 最近备份检查
echo "📋 4. 检查最近备份"
if [ -d "backups" ]; then
    latest_backup=$(ls -t backups/ | head -1 2>/dev/null)
    if [ -n "$latest_backup" ]; then
        echo "✅ 最近备份: $latest_backup"
    else
        echo "⚠️  没有找到备份文件"
    fi
else
    echo "⚠️  备份目录不存在"
fi

echo ""
echo "🔍 操作前检查完成"
echo "如果有⚠️警告，建议先处理再继续"

EOF
chmod +x scripts/pre-operation-check.sh
```

### 🧪 操作后验证机制

```bash
# 创建操作后验证脚本
cat > scripts/post-operation-validation.sh << 'EOF'
#!/bin/bash

echo "🧪 执行操作后验证..."

# 1. 基础功能验证
echo "📋 1. 基础功能验证"
./scripts/test-startup.sh > /tmp/post-check.log 2>&1
if [ $? -ne 0 ]; then
    echo "❌ 基础功能验证失败"
    echo "修改可能引入了问题，建议立即回滚"
    return 1
else
    echo "✅ 基础功能验证通过"
fi

# 2. API兼容性验证
echo "📋 2. API兼容性验证"
validate_api_compatibility() {
    local api_endpoints=(
        "/api/debts/statistics/collection-status"
        "/api/export/test/completeOverdueReport"
        "/api/users/current"
        "/actuator/health"
    )
    
    for endpoint in "${api_endpoints[@]}"; do
        echo "  测试: $endpoint"
        response=$(curl -s --connect-timeout 5 --max-time 10 \
            "http://localhost:8080$endpoint?year=2025&month=6&company=全部" 2>/dev/null)
        
        if [ $? -eq 0 ] && [[ ${#response} -gt 10 ]]; then
            echo "  ✅ $endpoint 响应正常"
        else
            echo "  ❌ $endpoint 响应异常"
            echo "  响应: $response"
            return 1
        fi
    done
}
validate_api_compatibility

# 3. 配置完整性验证
echo "📋 3. 配置完整性验证"
validate_config_integrity() {
    local config_file="api-gateway/src/main/resources/application.yml"
    
    if [ ! -f "$config_file" ]; then
        echo "❌ 主配置文件丢失: $config_file"
        return 1
    fi
    
    local required_configs=(
        "overdue_debt_db"
        "user_system"
        "kingdee"
        "jwt.secret"
        "server.port"
    )
    
    for config in "${required_configs[@]}"; do
        if grep -q "$config" "$config_file"; then
            echo "  ✅ $config 配置存在"
        else
            echo "  ❌ $config 配置缺失"
            return 1
        fi
    done
}
validate_config_integrity

# 4. 数据一致性检查
echo "📋 4. 数据一致性检查"
check_data_consistency() {
    # 测试核心数据查询
    local debt_stats=$(curl -s -X GET \
        "http://localhost:8080/api/debts/statistics/collection-status?year=2025&month=6月&company=全部" \
        -H "Accept: application/json" 2>/dev/null)
    
    if echo "$debt_stats" | grep -q "yearBeginAmount\|monthCollectionAmount"; then
        echo "✅ 核心数据查询正常"
    else
        echo "❌ 核心数据查询异常"
        echo "响应: $debt_stats"
        return 1
    fi
}
check_data_consistency

# 5. 性能基准检查
echo "📋 5. 性能基准检查"
check_performance_baseline() {
    echo "  测试启动时间..."
    start_time=$(date +%s)
    curl -s --connect-timeout 30 "http://localhost:8080/actuator/health" > /dev/null
    end_time=$(date +%s)
    response_time=$((end_time - start_time))
    
    if [ $response_time -lt 30 ]; then
        echo "  ✅ 启动响应时间正常: ${response_time}s"
    else
        echo "  ⚠️  启动响应时间较慢: ${response_time}s"
    fi
}
check_performance_baseline

echo ""
echo "🧪 操作后验证完成"
echo "如果出现❌错误，建议立即回滚修改"

EOF
chmod +x scripts/post-operation-validation.sh
```

### 🚨 异常检测和告警

```bash
# 创建异常检测脚本
cat > scripts/anomaly-detection.sh << 'EOF'
#!/bin/bash

echo "🚨 执行异常检测..."

# 1. 错误日志检测
echo "📋 1. 错误日志检测"
detect_error_logs() {
    local log_files=(
        "/tmp/backend.log"
        "/tmp/backend_verification.log"
        "logs/application.log"
    )
    
    for log_file in "${log_files[@]}"; do
        if [ -f "$log_file" ]; then
            error_count=$(tail -100 "$log_file" | grep -i "error\|exception\|failed" | wc -l)
            if [ $error_count -gt 5 ]; then
                echo "⚠️  $log_file 发现较多错误: $error_count 条"
                echo "最近错误:"
                tail -100 "$log_file" | grep -i "error\|exception\|failed" | tail -3
            else
                echo "✅ $log_file 错误数量正常: $error_count 条"
            fi
        fi
    done
}
detect_error_logs

# 2. 内存使用检测
echo "📋 2. 内存使用检测"
check_memory_usage() {
    if command -v free >/dev/null 2>&1; then
        memory_usage=$(free | grep Mem | awk '{printf("%.1f", $3/$2 * 100.0)}')
        echo "当前内存使用率: $memory_usage%"
        
        if (( $(echo "$memory_usage > 90" | bc -l) )); then
            echo "⚠️  内存使用率过高"
        else
            echo "✅ 内存使用率正常"
        fi
    else
        echo "ℹ️  无法检测内存使用情况 (Linux环境)"
    fi
}
check_memory_usage

# 3. 端口占用检测
echo "📋 3. 端口占用检测"
check_port_usage() {
    local required_ports=(8080 3000 3306)
    
    for port in "${required_ports[@]}"; do
        if lsof -ti :$port > /dev/null; then
            echo "✅ 端口 $port 正常占用"
        else
            echo "⚠️  端口 $port 未被占用"
        fi
    done
}
check_port_usage

# 4. 文件权限检测
echo "📋 4. 关键文件权限检测"
check_file_permissions() {
    local critical_files=(
        "api-gateway/src/main/resources/application.yml"
        "scripts/test-startup.sh"
        "pom.xml"
    )
    
    for file in "${critical_files[@]}"; do
        if [ -f "$file" ]; then
            permissions=$(ls -l "$file" | awk '{print $1}')
            echo "✅ $file 权限: $permissions"
        else
            echo "❌ 关键文件缺失: $file"
        fi
    done
}
check_file_permissions

echo ""
echo "🚨 异常检测完成"

EOF
chmod +x scripts/anomaly-detection.sh
```

---

## 4. 实战操作示例

### 💼 场景1：添加新的DTO字段 (Level 1)

```bash
# 实际操作示例
用户需求: "在用户注册接口中添加手机号字段"

# 1. 操作前检查
./scripts/pre-operation-check.sh

# 2. 使用标准指令
请修改UserRegisterRequest DTO类，添加手机号字段：

🎯 **修改目标**：
- 添加字段：phoneNumber String 用于用户注册时填写手机号
- 业务需求：用户注册需要验证手机号，增强账户安全性

✅ **允许操作**：
- 在UserRegisterRequest类中添加phoneNumber字段
- 添加@NotBlank和@Pattern验证注解
- 添加getter/setter方法
- 添加字段注释

⚠️ **兼容性注意**：
- 新字段应该是可选的，不影响现有注册流程
- 前端可以选择性传递此字段
- 后端需要兼容处理空值情况

📋 **验证要求**：
- 测试注册API的JSON序列化
- 验证前端注册页面正常
- 确认手机号验证规则生效
- 运行：./scripts/test-startup.sh

# 3. 操作后验证
./scripts/post-operation-validation.sh

# 4. 异常检测
./scripts/anomaly-detection.sh
```

### 🐛 场景2：修复Service层Bug (Level 3)

```bash
# 实际操作示例
用户报告: "债权统计API返回数据不正确，期末余额计算有误"

# 1. 创建安全备份
./scripts/ai-safe-backup.sh "fix-debt-statistics-calculation"

# 2. 创建修复分支
git checkout -b feature/ai-hotfix-debt-calculation-$(date +%Y%m%d)

# 3. 使用Fix Agent指令模板
⚠️ **高风险Bug修复** - 涉及核心业务逻辑

🐛 **严重问题**：债权统计API中期末余额计算错误，影响财务报表准确性

📍 **精确修复范围**：
- 核心问题文件：services/debt-management/src/main/java/com/laoshu198838/service/DebtStatisticsService.java
- 核心问题方法：DebtStatisticsService.calculatePeriodEndAmount()
- 根本原因：期末余额计算公式错误，没有正确处理累计处置金额
- 影响评估：影响所有债权统计报表和数据导出功能

🚫 **严格约束**：
- 仅修改calculatePeriodEndAmount方法内部逻辑
- 保持方法签名不变：BigDecimal calculatePeriodEndAmount(...)
- 保持返回值格式不变
- 不修改任何其他Service方法
- 不修改Controller层或数据库查询

📊 **全面验证**：
- 基础测试：./scripts/test-startup.sh
- API测试：curl测试债权统计API
- 数据验证：验证计算结果的数学正确性
- 回归测试：确认其他统计功能正常

🔄 **分步执行**：
1. 先分析现有计算逻辑，找出具体错误
2. 设计正确的计算公式
3. 修复方法实现，添加详细注释
4. 验证修复效果和数据准确性

# 4. 执行修复后验证
./scripts/post-operation-validation.sh

# 5. 业务逻辑验证
curl -X GET "http://localhost:8080/api/debts/statistics/collection-status?year=2025&month=6月&company=全部"
```

### 🔄 场景3：性能优化重构 (Level 2)

```bash
# 实际操作示例
用户需求: "债权数据导出功能太慢，需要优化性能"

# 1. 性能基准测试
echo "🔍 记录当前性能基准"
time curl -X GET "http://localhost:8080/api/export/test/completeOverdueReport?year=2025&month=6&amount=100" \
  --output /tmp/export-before.xlsx

# 2. 使用Refactor Agent指令模板
🔄 **性能优化重构任务**

🎯 **优化目标**：
- 性能问题：债权数据导出接口响应时间过长（当前30-60秒）
- 优化目标：响应时间缩短到10秒以内
- 影响范围：ExportService.exportCompleteOverdueReport方法

📊 **性能基准**：
- 当前性能：导出100条记录需要30-60秒
- 目标性能：导出100条记录在10秒内完成
- 测试场景：相同数据量的导出请求

🔒 **重构约束**：
- 功能兼容：导出的Excel格式和内容完全一致
- 接口稳定：API路径和参数保持不变
- 配置不变：不修改数据库连接配置
- 数据一致：确保导出数据的准确性

✅ **允许的优化**：
- 数据库查询优化：使用联表查询替代N+1查询
- 批量处理：批量获取数据而非逐条查询
- 内存优化：优化数据处理的内存使用
- Excel生成优化：优化Excel写入性能

📋 **验证要求**：
- 功能验证：导出的Excel内容与优化前一致
- 性能验证：响应时间符合预期目标
- 兼容性验证：API接口调用方式不变
- 稳定性验证：多次导出结果稳定

# 3. 执行重构
# (按照Refactor Agent的指导进行具体实现)

# 4. 性能对比测试
echo "🔍 测试优化后性能"
time curl -X GET "http://localhost:8080/api/export/test/completeOverdueReport?year=2025&month=6&amount=100" \
  --output /tmp/export-after.xlsx

# 5. 结果对比验证
echo "🔍 对比导出文件内容"
# 比较两个Excel文件的数据是否一致
# （这里需要具体的Excel对比逻辑）
```

---

## 5. 故障排除和回滚

### 🔙 快速回滚机制

```bash
# 创建快速回滚脚本
cat > scripts/quick-rollback.sh << 'EOF'
#!/bin/bash

# 快速回滚脚本
quick_rollback() {
    local rollback_type=$1
    local rollback_target=$2
    
    echo "🔙 执行快速回滚..."
    echo "回滚类型: $rollback_type"
    echo "回滚目标: $rollback_target"
    
    case "$rollback_type" in
        "checkpoint")
            echo "📋 回滚到检查点: $rollback_target"
            git reset --hard "$rollback_target"
            ;;
        "commit")
            echo "📋 回滚指定提交数: $rollback_target"
            git reset --hard "HEAD~$rollback_target"
            ;;
        "branch")
            echo "📋 回滚到分支: $rollback_target"
            git checkout "$rollback_target"
            ;;
        "backup")
            echo "📋 恢复备份: $rollback_target"
            if [ -d "backups/$rollback_target" ]; then
                cp -r "backups/$rollback_target"/* .
            else
                echo "❌ 备份不存在: $rollback_target"
                return 1
            fi
            ;;
        *)
            echo "❌ 未知回滚类型: $rollback_type"
            echo "支持的类型: checkpoint, commit, branch, backup"
            return 1
            ;;
    esac
    
    # 回滚后验证
    echo "🧪 回滚后验证..."
    ./scripts/test-startup.sh
    if [ $? -eq 0 ]; then
        echo "✅ 回滚成功，系统恢复正常"
    else
        echo "❌ 回滚后系统仍有问题"
    fi
}

# 使用示例
# ./scripts/quick-rollback.sh checkpoint abc123
# ./scripts/quick-rollback.sh commit 2
# ./scripts/quick-rollback.sh backup ai-backup-20250814_143025

EOF
chmod +x scripts/quick-rollback.sh
```

### 🚨 紧急故障处理

```bash
# 创建紧急故障处理脚本
cat > scripts/emergency-recovery.sh << 'EOF'
#!/bin/bash

echo "🚨 紧急故障处理程序启动"

# 1. 系统状态诊断
echo "📋 1. 系统状态诊断"
diagnose_system_status() {
    echo "  检查应用进程..."
    if lsof -ti :8080 > /dev/null; then
        echo "  ✅ 应用进程运行中"
    else
        echo "  ❌ 应用进程未运行"
        return 1
    fi
    
    echo "  检查数据库连接..."
    if curl -s "http://localhost:8080/actuator/health" | grep -q "UP"; then
        echo "  ✅ 数据库连接正常"
    else
        echo "  ❌ 数据库连接异常"
        return 1
    fi
    
    echo "  检查关键API..."
    if curl -s "http://localhost:8080/api/users/current" > /dev/null; then
        echo "  ✅ 关键API响应正常"
    else
        echo "  ❌ 关键API无响应"
        return 1
    fi
}

if ! diagnose_system_status; then
    echo "🚨 系统状态异常，启动恢复程序"
    
    # 2. 尝试重启应用
    echo "📋 2. 尝试重启应用"
    restart_application() {
        echo "  停止现有进程..."
        lsof -ti :8080 | xargs kill -9 2>/dev/null
        sleep 3
        
        echo "  启动应用..."
        nohup mvn spring-boot:run -pl api-gateway > /tmp/emergency-restart.log 2>&1 &
        
        echo "  等待启动..."
        sleep 30
        
        if lsof -ti :8080 > /dev/null; then
            echo "  ✅ 应用重启成功"
            return 0
        else
            echo "  ❌ 应用重启失败"
            return 1
        fi
    }
    
    if ! restart_application; then
        # 3. 执行自动回滚
        echo "📋 3. 执行自动回滚"
        echo "  查找最近的安全检查点..."
        latest_checkpoint=$(git log --oneline --grep="checkpoint:" -1 --format="%H")
        
        if [ -n "$latest_checkpoint" ]; then
            echo "  回滚到检查点: $latest_checkpoint"
            git reset --hard "$latest_checkpoint"
            
            echo "  重新启动应用..."
            restart_application
            
            if [ $? -eq 0 ]; then
                echo "✅ 自动回滚成功"
            else
                echo "❌ 自动回滚失败，需要人工干预"
                exit 1
            fi
        else
            echo "❌ 未找到安全检查点，需要人工干预"
            exit 1
        fi
    fi
else
    echo "✅ 系统状态正常"
fi

# 4. 生成故障报告
echo "📋 4. 生成故障报告"
generate_incident_report() {
    local report_file="logs/incident-$(date +%Y%m%d_%H%M%S).log"
    
    cat > "$report_file" << 'EOL'
# 故障处理报告

## 故障时间
$(date)

## 故障现象
- 应用状态: $(lsof -ti :8080 > /dev/null && echo "运行中" || echo "未运行")
- 最近错误日志:
$(tail -20 /tmp/backend.log 2>/dev/null | grep -i error || echo "无错误日志")

## 处理措施
- 执行了紧急恢复程序
- 系统当前状态: 正常

## 建议后续行动
- 详细分析故障原因
- 检查最近的代码修改
- 加强监控和预警机制
EOL

    echo "  故障报告已生成: $report_file"
}
generate_incident_report

echo "🚨 紧急故障处理完成"

EOF
chmod +x scripts/emergency-recovery.sh
```

### 📋 故障排除清单

#### 常见问题和解决方案

```yaml
应用无法启动:
  症状: 运行test-startup.sh失败，8080端口无响应
  可能原因:
    - 配置文件语法错误
    - 数据库连接失败
    - 端口被占用
    - JAR文件损坏
  解决步骤:
    1. 检查配置文件语法: yaml-validator application.yml
    2. 检查数据库连接: mysql -u root -p
    3. 检查端口占用: lsof -ti :8080
    4. 查看启动日志: tail -50 /tmp/backend.log
    5. 回滚到最近检查点: git reset --hard HEAD~1

API响应异常:
  症状: curl测试API返回错误或超时
  可能原因:
    - 业务逻辑错误
    - 数据库查询问题
    - 内存不足
    - 依赖服务异常
  解决步骤:
    1. 检查API日志: grep "ERROR" /tmp/backend.log
    2. 测试数据库查询: mysql查询验证
    3. 检查内存使用: free -h
    4. 验证依赖服务: curl测试外部接口
    5. 逐步回滚确定问题范围

配置文件损坏:
  症状: 启动时配置加载失败
  可能原因:
    - YAML语法错误
    - 配置项缺失
    - 文件权限问题
    - 编码问题
  解决步骤:
    1. 语法检查: yamllint application.yml
    2. 对比备份配置: diff application.yml backups/
    3. 检查文件权限: ls -l application.yml
    4. 恢复备份配置: cp backups/application.yml.backup application.yml
    5. 重新启动验证: ./scripts/test-startup.sh

数据不一致:
  症状: API返回数据与预期不符
  可能原因:
    - 计算逻辑错误
    - 数据库数据问题
    - 缓存数据过期
    - 查询条件错误
  解决步骤:
    1. 验证数据库原始数据: SQL查询验证
    2. 检查计算逻辑: 代码逻辑审查
    3. 清理缓存: 重启应用清除缓存
    4. 对比历史数据: 查看Git历史
    5. 逐步排查修改点: 二分法回滚
```

---

## 📚 相关文档

- [权限分层矩阵实施指南](./permission-matrix-implementation.md)
- [分层安全代理系统总体设计](./layered-security-agent-system.md)
- [AI开发安全指南](../ai-development-safety-guide.md)
- [项目架构文档](./architecture.md)
- [故障排除手册](../troubleshooting/)

---

*文档版本: v1.0*  
*创建日期: 2025-08-14*  
*适用项目: FinancialSystem*  
*下次更新: 根据实际使用反馈优化*
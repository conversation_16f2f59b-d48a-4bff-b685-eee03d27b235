# 分层安全开发代理系统 (Layered Security Agent System)

## 📋 目录
- [1. 系统概述](#1-系统概述)
- [2. 架构设计](#2-架构设计)
- [3. 权限分层矩阵](#3-权限分层矩阵)
- [4. 智能代理定义](#4-智能代理定义)
- [5. 决策算法](#5-决策算法)
- [6. 与现有系统融合](#6-与现有系统融合)
- [7. 配置实现](#7-配置实现)
- [8. 使用示例](#8-使用示例)
- [9. 实施路线图](#9-实施路线图)

---

## 1. 系统概述

### 🎯 设计目标
基于**ultrathink深度分析**，设计一个智能化的分层安全代理系统，解决AI开发中"安全性 vs 效率"的核心矛盾：

- **问题**：完全禁止修改 → 开发效率低下、技术债务累积
- **解决方案**：智能分层权限控制 + 专门化代理系统
- **核心理念**：`风险级别 = 文件重要性 × 操作类型 × 影响范围`

### 🧠 核心创新
1. **渐进式安全控制** - 从宽松到严格的层级化权限
2. **智能代理调度** - 根据操作类型自动选择合适的代理
3. **学习型优化** - 基于历史成功率动态调整策略
4. **完全可追溯** - 所有操作都有完整的决策记录

---

## 2. 架构设计

### 🏗️ 整体架构图

```mermaid
graph TB
    User[👤 用户请求] --> SG[🛡️ Security Guard Agent<br/>智能权限判断]
    
    SG --> L0[🟢 Level 0: 自由操作<br/>直接执行]
    SG --> L1[🟡 Level 1: Planning Agent<br/>新功能开发]
    SG --> L2[🟠 Level 2: Refactor Agent<br/>重构优化]
    SG --> L3[🔴 Level 3: Fix Agent<br/>问题修复]
    SG --> L4[⚫ Level 4: 拒绝执行<br/>绝对禁止]
    
    L1 --> Plan[📋 制定实施计划]
    L2 --> Refactor[🔧 安全重构]
    L3 --> Fix[🚨 最小影响修复]
    
    Plan --> Test[🧪 test-startup.sh]
    Refactor --> Test
    Fix --> Test
    
    Test --> Success[✅ 提交代码]
    Test --> Fail[❌ 回滚修复]
```

### 🤖 代理系统层次结构

```yaml
Security Guard Agent (守护层)
├── 权限计算引擎
├── 代理调度中心  
├── 历史模式学习
└── 审计追踪系统

Specialized Agents (执行层)
├── Planning Agent    # Level 1 - 新功能规划
├── Refactor Agent    # Level 2 - 重构优化
├── Fix Agent         # Level 3 - 问题修复
└── Validation System # 通用验证层
```

---

## 3. 权限分层矩阵

### 🎚️ 五级权限控制体系

| 级别 | 权限范围 | 代理类型 | 典型操作 | 验证要求 |
|------|----------|----------|----------|----------|
| **🟢 Level 0** | 自由操作 | 无需代理 | 新增文件/类/方法<br/>添加注释/文档<br/>修改测试代码 | 基础编译检查 |
| **🟡 Level 1** | 智能协助 | Planning Agent | 新功能实现<br/>业务逻辑扩展<br/>UI组件开发 | 规划确认 + 启动测试 |
| **🟠 Level 2** | 专家审核 | Refactor Agent | 重构现有代码<br/>性能优化<br/>架构改进 | 影响分析 + 完整测试 |
| **🔴 Level 3** | 严格控制 | Fix Agent | 修复核心bug<br/>修改配置文件<br/>安全问题修复 | 根因分析 + 回滚预案 |
| **⚫ Level 4** | 绝对禁止 | 系统拒绝 | 修改数据库结构<br/>删除现有API<br/>修改核心架构 | 人工干预 |

### 📊 文件重要性分级

```yaml
🔴 核心文件 (Critical - 自动Level 3+)
  - Entity类: *.java (包含@Entity)
  - 配置文件: application*.yml, pom.xml
  - 安全配置: Security*, Auth*
  - 数据库迁移: *.sql

🟠 重要文件 (Important - 最低Level 2)
  - Service核心类: *Service.java 
  - Repository类: *Repository.java
  - Controller主要端点: *Controller.java
  - 前端核心组件: App.js, index.js

🟡 业务文件 (Business - 最低Level 1)  
  - DTO类: *DTO.java, *Request.java
  - 工具类: *Util.java, *Helper.java
  - 前端业务组件: components/*
  - 样式文件: *.css, *.scss

🟢 自由文件 (Free - Level 0)
  - 测试文件: *Test.java, *.test.js
  - 文档文件: *.md, *.txt
  - 示例代码: examples/*, demo/*
  - 构建脚本: scripts/*
```

---

## 4. 智能代理定义

### 🛡️ Security Guard Agent (安全守护代理)

**职责**：智能权限判断和代理调度中心

```yaml
核心能力:
  - 文件重要性自动评估算法
  - 操作风险实时计算
  - 最优代理智能选择
  - 历史模式学习优化

工作流程:
  1. 接收用户请求 → 解析意图和目标文件
  2. 计算操作风险级别 → 应用权限矩阵
  3. 选择合适代理 → 传递上下文和约束
  4. 监控执行过程 → 记录审计日志
  5. 学习优化策略 → 更新决策模型

决策输入:
  - 目标文件路径和类型
  - 操作类型（新增/修改/删除）
  - 影响范围（单文件/跨模块/系统级）
  - 用户意图（功能开发/bug修复/重构）
  - 历史成功率数据
```

### 🎯 Planning Agent (规划代理)

**适用场景**：Level 1 - 新功能开发和业务扩展

```yaml
专业领域:
  - 新功能架构设计
  - 业务逻辑规划
  - 模块依赖分析
  - 接口设计优化

核心约束:
  - ✅ 允许: 添加新方法、新类、新模块
  - ✅ 允许: 扩展现有业务逻辑（非核心）
  - ✅ 允许: 创建新的API端点
  - ❌ 禁止: 修改现有方法签名
  - ❌ 禁止: 修改核心业务逻辑
  - ❌ 禁止: 删除任何现有代码

工作模式:
  1. 需求分析 → 理解功能要求和业务场景
  2. 架构设计 → 设计实现方案和模块关系
  3. 计划制定 → 分解任务和验证节点
  4. 用户确认 → 展示计划等待确认
  5. 分步实施 → 按模块逐步实现
  6. 持续验证 → 每步完成后运行测试

输出物:
  - 详细实施计划文档
  - 模块依赖关系图
  - API接口设计文档
  - 测试验证方案
```

### 🔧 Refactor Agent (重构代理)

**适用场景**：Level 2 - 代码重构和性能优化

```yaml
专业领域:
  - 代码质量改进
  - 性能瓶颈优化
  - 架构结构清理
  - 技术债务解决

核心约束:
  - ✅ 允许: 优化方法内部实现
  - ✅ 允许: 重构类内部结构
  - ✅ 允许: 提取公共逻辑
  - ⚠️ 限制: 修改公共接口（需要兼容性保证）
  - ❌ 禁止: 改变外部API契约
  - ❌ 禁止: 修改数据模型结构

安全原则:
  - 接口兼容性优先
  - 渐进式重构策略
  - 完整回归测试
  - 性能基准对比

工作模式:
  1. 代码分析 → 识别重构目标和风险点
  2. 影响评估 → 分析依赖关系和兼容性
  3. 重构计划 → 制定分步骤重构方案
  4. 安全实施 → 保持外部接口稳定
  5. 验证测试 → 功能测试 + 性能测试
  6. 回滚预案 → 准备快速回滚方案
```

### 🚨 Fix Agent (修复代理)

**适用场景**：Level 3 - 关键问题修复和配置调整

```yaml
专业领域:
  - 生产环境bug修复
  - 安全漏洞修补
  - 系统配置调整
  - 数据一致性修复

特殊权限:
  - ✅ 允许: 修改核心业务逻辑（限bug修复）
  - ✅ 允许: 调整系统配置参数
  - ✅ 允许: 修复安全问题
  - ⚠️ 谨慎: 修改数据库查询逻辑
  - ❌ 禁止: 修改数据库结构
  - ❌ 禁止: 删除现有功能

修复原则:
  - 最小影响范围
  - 根因彻底分析
  - 完整测试验证
  - 详细回滚预案

工作模式:
  1. 问题诊断 → 深入分析根本原因
  2. 影响评估 → 评估修复方案的风险
  3. 修复设计 → 设计最小影响的修复方案
  4. 测试验证 → 重点测试修复效果
  5. 部署准备 → 准备生产环境部署方案
  6. 监控验证 → 部署后效果监控
```

---

## 5. 决策算法

### 🧮 风险计算公式

```javascript
// 核心风险评估算法
function calculateRiskLevel(operation) {
    const fileImportance = getFileImportance(operation.targetFiles);
    const operationType = getOperationType(operation.action);
    const impactScope = getImpactScope(operation.affectedModules);
    const userIntent = getUserIntent(operation.description);
    
    // 风险级别 = 文件重要性 × 操作类型 × 影响范围 × 意图权重
    const riskScore = fileImportance * operationType * impactScope * userIntent;
    
    // 映射到权限级别
    if (riskScore >= 80) return "Level_4_Forbidden";
    if (riskScore >= 60) return "Level_3_FixAgent";  
    if (riskScore >= 40) return "Level_2_RefactorAgent";
    if (riskScore >= 20) return "Level_1_PlanningAgent";
    return "Level_0_Direct";
}

// 文件重要性评估
function getFileImportance(files) {
    const weights = {
        "Entity": 4,      // 实体类最重要
        "Config": 4,      // 配置文件最重要  
        "Service": 3,     // 服务类重要
        "Controller": 3,  // 控制器重要
        "Repository": 3,  // 数据访问重要
        "DTO": 2,         // 数据传输对象
        "Util": 2,        // 工具类
        "Test": 1,        // 测试文件
        "Doc": 1          // 文档文件
    };
    
    return Math.max(...files.map(file => 
        weights[classifyFile(file)] || 2
    ));
}

// 操作类型评估
function getOperationType(action) {
    const weights = {
        "DELETE": 4,      // 删除操作风险最高
        "MODIFY_CORE": 3, // 修改核心逻辑
        "MODIFY_IMPL": 2, // 修改实现细节
        "ADD_METHOD": 1,  // 添加方法
        "ADD_FILE": 1     // 添加文件
    };
    
    return weights[action] || 2;
}

// 影响范围评估  
function getImpactScope(modules) {
    if (modules.includes("database")) return 4;
    if (modules.includes("security")) return 4;
    if (modules.length > 3) return 3;
    if (modules.length > 1) return 2;
    return 1;
}
```

### 🎯 智能代理选择

```yaml
代理选择决策树:
  用户意图分析:
    - "修复" / "fix" / "bug" → Fix Agent
    - "重构" / "优化" / "refactor" → Refactor Agent  
    - "新增" / "添加" / "实现" → Planning Agent
    - "删除" / "移除" → 高风险评估

  文件类型分析:
    - Entity类被修改 → Fix Agent (强制Level 3)
    - 配置文件被修改 → Fix Agent (强制Level 3)
    - Service类新增方法 → Planning Agent
    - Service类修改现有方法 → Refactor Agent

  操作范围分析:
    - 跨模块操作 → 提升一个风险级别
    - 数据库相关 → 提升一个风险级别
    - 安全相关 → 提升一个风险级别
```

---

## 6. 与现有系统融合

### 🔄 CLAUDE.md 集成方案

在现有的 `## 🤖 Assistant Guidelines` 部分添加：

```markdown
### 🛡️ 分层安全代理系统

#### 自动权限控制
Claude将自动根据操作风险级别选择合适的代理执行任务：

**🟢 Level 0 - 直接执行（无需代理）**
- ✅ 添加新文件、类、方法
- ✅ 修改注释和文档  
- ✅ 修改测试代码
- ✅ 添加工具类和DTO

**🟡 Level 1 - Planning Agent（智能协助）**
- 🤖 新功能开发规划
- 🤖 业务逻辑扩展
- 🤖 UI组件开发
- 🤖 API端点添加

**🟠 Level 2 - Refactor Agent（专家审核）**  
- 🔧 代码重构优化
- 🔧 性能问题解决
- 🔧 架构改进
- 🔧 技术债务清理

**🔴 Level 3 - Fix Agent（严格控制）**
- 🚨 核心bug修复
- 🚨 安全问题修补
- 🚨 配置文件调整
- 🚨 数据一致性修复

**⚫ Level 4 - 系统拒绝（绝对禁止）**
- 🚫 修改数据库结构
- 🚫 删除现有API接口
- 🚫 修改核心架构组件

#### 智能触发机制
```bash
# 用户输入示例 → 自动代理选择
"修复用户登录问题" → Fix Agent (Level 3)
"添加密码重置功能" → Planning Agent (Level 1)  
"优化UserService性能" → Refactor Agent (Level 2)
"重构数据导出模块" → Refactor Agent (Level 2)
"修改Entity字段" → Fix Agent (Level 3，需要特殊理由)
```

#### 强制验证要求
所有代理操作完成后必须执行：
- 🧪 **test-startup.sh** - 完整启动测试
- 📋 **影响分析报告** - 详细记录修改内容
- 🔄 **回滚预案** - Level 2/3操作必须提供
```

### 🎮 Git工作流集成

扩展现有的Git自动化触发器：

```markdown
**🤖 智能代理触发**：
- 用户说：`fix: [问题描述]` 
- Claude执行：自动调用Fix Agent，执行修复流程
- 用户说：`refactor: [优化描述]`
- Claude执行：自动调用Refactor Agent，执行重构流程
- 用户说：`feature: [功能描述]`
- Claude执行：自动调用Planning Agent，执行规划流程
```

### 🔄 与现有系统融合

扩展现有的Git自动化触发器：

```markdown
**🤖 智能代理触发**：
- 用户说：`fix: [问题描述]` 
- Claude执行：自动调用Fix Agent，执行修复流程
- 用户说：`refactor: [优化描述]`
- Claude执行：自动调用Refactor Agent，执行重构流程
- 用户说：`feature: [功能描述]`
- Claude执行：自动调用Planning Agent，执行规划流程
```

## 6.5. 混合实施方案

### 🎯 方案概述

基于实际技术现状，采用**混合实施策略**确保分层安全代理系统的实际可用性：

```yaml
实施策略:
  1. 核心规则 → 直接集成到CLAUDE.md (确保遵守)
  2. 详细配置 → 保留在.claude/agents/ (设计文档)  
  3. 使用引用 → 明确引用配置指导Claude
```

### 🔧 技术现状分析

**Claude Code当前能力**：
- ✅ 自动读取和遵循 `CLAUDE.md` 文件
- ✅ 支持 `@include` 指令引用外部配置
- ❌ 不会自动解析 `.claude/agents/*.yml` 配置
- ❌ 没有内置的YAML配置解释器

**解决方案**：
- **立即生效**: 核心规则直接写入CLAUDE.md
- **长期规划**: 详细配置作为设计文档和未来扩展基础
- **灵活应用**: 开发时明确引用具体规则

### 📋 CLAUDE.md 核心规则集成

在CLAUDE.md的 `## 🤖 Assistant Guidelines` 部分添加：

```markdown
### 🛡️ 分层安全代理系统

#### 文件重要性自动分级
Claude将根据以下规则自动判断操作风险级别和选择合适代理：

**🔴 核心文件 (自动Level 3+ - Fix Agent)**
- Entity类: *.java (包含@Entity)
- 配置文件: application*.yml, pom.xml  
- 安全配置: Security*, Auth*
- 数据库迁移: *.sql

**🟠 重要文件 (最低Level 2 - Refactor Agent)**
- Service核心类: *Service.java
- Repository类: *Repository.java
- Controller主要端点: *Controller.java
- 前端核心组件: App.js, index.js

**🟡 业务文件 (最低Level 1 - Planning Agent)**
- DTO类: *DTO.java, *Request.java
- 工具类: *Util.java, *Helper.java
- 前端业务组件: components/*
- 样式文件: *.css, *.scss

**🟢 自由文件 (Level 0 - 直接执行)**
- 测试文件: *Test.java, *.test.js
- 文档文件: *.md, *.txt
- 示例代码: examples/*, demo/*
- 构建脚本: scripts/*

#### 智能代理自动选择
基于用户意图和文件类型自动选择合适的代理：

```bash
# 意图关键词触发
"修复" / "fix" / "bug" → Fix Agent (Level 3)
"重构" / "优化" / "refactor" → Refactor Agent (Level 2)  
"新增" / "添加" / "实现" → Planning Agent (Level 1)
"删除" / "移除" → 高风险评估或拒绝

# 文件类型强制路由
Entity类修改 → 强制Fix Agent (Level 3)
配置文件修改 → 强制Fix Agent (Level 3)
Service类新增方法 → Planning Agent
Service类修改现有方法 → Refactor Agent
```

#### 强制验证要求
所有Level 1+操作必须执行：
- 🧪 **test-startup.sh** - 完整启动测试
- 📋 **影响分析** - 详细记录修改内容  
- 🔄 **回滚预案** - Level 2/3操作必须提供

#### 操作约束规则
**Planning Agent (Level 1) 约束**:
- ✅ 允许: 添加新文件、类、方法
- ✅ 允许: 扩展现有业务逻辑（非核心）
- ❌ 禁止: 修改现有方法签名
- ❌ 禁止: 修改核心业务逻辑

**Refactor Agent (Level 2) 约束**:
- ✅ 允许: 优化方法内部实现  
- ✅ 允许: 重构类内部结构
- ⚠️ 限制: 修改公共接口（需兼容性保证）
- ❌ 禁止: 改变外部API契约

**Fix Agent (Level 3) 约束**:
- ✅ 允许: 修改核心业务逻辑（限bug修复）
- ✅ 允许: 调整系统配置参数
- ⚠️ 谨慎: 修改数据库查询逻辑
- ❌ 禁止: 修改数据库结构

**系统拒绝 (Level 4)**:
- 🚫 修改数据库结构
- 🚫 删除现有API接口  
- 🚫 修改核心架构组件
```

### 🎮 实际使用指南

#### 开发者使用方式

**1. 依赖自动判断（推荐）**
```bash
用户: "修复用户登录状态丢失问题"
# Claude自动: 识别为fix意图 → Fix Agent → Level 3流程

用户: "添加密码重置功能"  
# Claude自动: 识别为新增功能 → Planning Agent → Level 1流程
```

**2. 明确引用配置**
```bash
用户: "按照.claude/agents/fix-agent.yml的规则修复这个bug"
# Claude会读取并遵循specific配置文件的详细规则

用户: "参考分层安全代理系统文档，重构这个Service"
# Claude会参考设计文档中的detailed guidelines
```

**3. 手动指定代理级别**
```bash
用户: "使用Level 2 Refactor Agent优化这个查询性能"
# 手动指定使用特定代理和级别
```

#### 配置引用机制

**引用详细配置的方法**：
```bash
# 引用完整代理配置
"按照.claude/agents/planning-agent.yml制定实施计划"

# 引用specific规则
"遵循security-guard.yml中的风险评估规则"

# 引用设计文档
"参考layered-security-agent-system.md的工作流程"
```

### 🔄 实施验证方法

#### 验证规则是否生效

**Level 0 验证**（应该直接执行）:
```bash
测试: "添加一个新的工具类Utils.java"
期望: Claude直接执行，无需额外确认
```

**Level 1 验证**（应该使用Planning Agent）:
```bash
测试: "实现用户密码重置功能"  
期望: Claude制定详细计划，等待确认后分步实施
```

**Level 2 验证**（应该使用Refactor Agent）:
```bash
测试: "重构UserService的查询性能"
期望: Claude进行影响分析，保持接口兼容性
```

**Level 3 验证**（应该使用Fix Agent）:
```bash
测试: "修改application.yml中的数据库配置"
期望: Claude要求详细理由，提供回滚预案
```

**Level 4 验证**（应该被拒绝）:
```bash
测试: "删除debt_management表"
期望: Claude直接拒绝，提供替代建议
```

#### 效果评估标准

**自动化程度**:
- ✅ 90%以上的操作能自动选择正确代理
- ✅ 文件类型识别准确率 > 95%
- ✅ 风险级别判断准确率 > 90%

**安全性保障**:
- ✅ 核心文件修改必须经过Level 3流程
- ✅ 所有修改都有完整的测试验证
- ✅ 危险操作能被及时拦截

**开发效率**:
- ✅ Level 0操作无障碍执行
- ✅ Level 1-3操作有清晰指导
- ✅ 整体开发效率提升 > 20%

### 🚀 渐进式实施路径

#### Phase 1: 立即实施（本周）
```yaml
目标: 基础规则生效
行动:
  - ✅ 完成CLAUDE.md核心规则集成
  - ✅ 创建.claude/agents/配置文件
  - ✅ 测试基础的Level判断
  - ✅ 验证自动代理选择
```

#### Phase 2: 优化完善（下周）
```yaml  
目标: 智能化增强
行动:
  - 🔄 根据使用反馈调整规则
  - 🔄 完善代理工作流程
  - 🔄 添加更多验证机制
  - 🔄 优化用户体验
```

#### Phase 3: 高级功能（未来）
```yaml
目标: 学习型系统
行动:
  - 🔮 添加历史模式学习
  - 🔮 实现动态规则调整
  - 🔮 支持团队协作模式
  - 🔮 开发可视化界面
```

### 💡 使用技巧和最佳实践

#### 开发者技巧
1. **信任自动判断** - 系统的风险评估基于extensive analysis
2. **明确表达意图** - 清楚说明是"修复"、"新增"还是"优化"
3. **适时引用配置** - 复杂场景时引用详细配置文件
4. **遵循验证要求** - 不要跳过test-startup.sh等验证步骤

#### 常见问题解决
**Q: Claude没有按expected level执行怎么办？**
A: 明确引用配置，如"按照Level 2 Refactor Agent的规则..."

**Q: 系统拒绝了合理的操作怎么办？**  
A: 提供更详细的context和理由，或者明确说明这是emergency fix

**Q: 想要绕过安全限制怎么办？**
A: 系统设计就是为了防止unsafe operations，建议寻找safer alternatives

### 🔑 高级授权机制

#### Level 4 操作授权绕过
在紧急情况下，可以通过特定关键词授权执行Level 4操作：

**授权关键词**:
```bash
# 紧急授权模式
"紧急授权修改数据库结构"
"管理员模式删除API接口" 
"临时提升权限执行危险操作"

# 责任承担模式
"我承担全部责任，删除测试表"
"已做完整备份，授权修改核心架构"
"生产环境故障，需要紧急修复"
```

**强制安全要求**:
- ✅ **理由说明**: 必须详细说明业务需要和紧急程度
- ✅ **备份确认**: 必须确认已做好数据和代码备份
- ✅ **风险承担**: 必须明确表示承担操作风险
- ✅ **回滚预案**: 必须提供详细的回滚步骤
- ✅ **操作审计**: 所有授权操作都会详细记录

**授权流程**:
```yaml
1. 用户提供授权关键词和详细理由
2. Claude评估紧急程度和合理性
3. 要求用户二次确认操作和风险
4. 执行操作并记录完整审计日志
5. 操作完成后验证系统状态
```

### 🤖 多代理智能编排

#### Planning Agent 生态系统
基于用户现有的专业代理架构，设计智能编排机制：

**现有Planning Agent生态系统集成**:
基于用户现有的专业agent架构(.claude/agents/planning/)，智能编排机制如下：

```yaml
requirements-analyst.md:
  位置: .claude/agents/planning/requirements-analyst.md
  权限: 只读 (Read-Only) 
  工具: Glob, Grep, LS, Read, NotebookRead, WebFetch, TodoWrite, WebSearch
  职责: 
    - 深度需求分析和理解
    - 业务场景挖掘和用户故事编写
    - 数据库结构查询和分析
    - 需求验证和风险评估
  输出: 标准化requirements.md文档

technical-design-agent.md:
  位置: .claude/agents/planning/technical-design-agent.md
  权限: 只读 (Read-Only)  
  工具: Glob, Grep, LS, Read, NotebookRead, WebFetch, TodoWrite, WebSearch
  职责:
    - 技术架构设计和规划
    - 系统集成方案设计
    - 技术选型和性能评估
    - API和数据模型设计
  输出: 技术设计文档和架构图

implementation-planner.md:
  位置: .claude/agents/planning/implementation-planner.md  
  权限: 只读 (Read-Only)
  工具: Glob, Grep, LS, Read, NotebookRead, WebFetch, TodoWrite, WebSearch
  职责:
    - 任务分解和工时估算
    - TDD指导和质量控制
    - 资源规划和风险管理
    - 可执行任务计划制定
    - 资源需求分析和分配
  输出: 项目实施计划和风险评估

implementation-planner.md:
  位置: .claude/agents/planning/implementation-planner.md
  权限: 读写 (Read-Write) - Level 1权限
  工具: Glob, Grep, LS, Read, NotebookRead, WebFetch, TodoWrite, WebSearch + 文件操作
  职责:
    - 具体代码实施和开发
    - 文件创建、修改和管理  
    - TDD任务分解和执行
    - 测试验证和部署集成
  输出: 实际代码、tasks.md和可执行系统
```

#### 自动编排触发机制
```yaml
智能触发条件:
  关键词: ["实现", "开发", "新增功能", "添加特性"]
  文件类型: 涉及新功能开发的任何请求
  
自动执行流程:
  Phase 1 - 并行分析 (5-10分钟):
    - requirements-analyst: 深入分析用户需求
    - technical-design-agent: 评估技术可行性
    
  Phase 2 - 实施规划 (3-5分钟):  
    - implementation-planner: 基于Phase 1结果制定详细实施计划
    
  Phase 3 - 用户确认:
    - 综合展示三个只读agent的分析结果
    - 等待用户确认和批准
    
  Phase 4 - 实施执行:
    - implementation-planner: 按计划执行实际开发
    - 分步骤实施，每步验证
    - 完整测试和部署
```

#### 手动指定机制
```bash
# 使用特定agent (基于现有.claude/agents/planning/路径)
"使用requirements-analyst分析用户注册需求"
"调用technical-design-agent设计支付系统架构"  
"让implementation-planner制定开发计划"
"启动implementation-planner开始编码实施"

# 完整路径引用
"按照.claude/agents/planning/requirements-analyst.md分析需求"
"遵循.claude/agents/planning/technical-design-agent.md进行架构设计"

# 跳过某些阶段
"跳过需求分析，直接启动technical-design-agent"
"已有设计方案，直接调用implementation-planner实施"
```

#### 代理协作机制
```yaml
信息传递:
  requirements-analyst → technical-design-agent: 需求上下文
  technical-design-agent → implementation-planner: 技术设计和约束条件
  
质量保证:
  - 每个agent都会验证上游输出
  - implementation-planner会综合考虑所有前期分析
  - 发现问题时可以回退到特定阶段重新分析
  
冲突解决:
  - 技术设计与需求冲突 → 回到requirements-analyst重新分析
  - 计划与技术方案冲突 → 回到technical-design-agent重新设计
  - 实施遇到问题 → 可以请求任何上游agent重新评估
```

### 📋 权限矩阵更新

**分析类Agent (只读权限) - 基于现有.claude/agents/planning/**:
```yaml
requirements-analyst.md:
  文件位置: .claude/agents/planning/requirements-analyst.md
  ✅ 允许: 读取所有项目文件和文档 (Glob, Grep, LS, Read)
  ✅ 允许: 分析现有代码结构和业务逻辑
  ✅ 允许: 访问API文档和数据库schema  
  ✅ 允许: 生成需求文档 (通过TodoWrite)
  ❌ 禁止: 创建、修改、删除任何代码文件
  ❌ 禁止: 执行任何文件写入操作

technical-design-agent.md:
  文件位置: .claude/agents/planning/technical-design-agent.md
  ✅ 允许: 读取技术文档和架构图  
  ✅ 允许: 分析现有技术栈和依赖
  ✅ 允许: 评估性能和安全影响
  ✅ 允许: 生成设计文档 (通过TodoWrite)
  ❌ 禁止: 修改配置文件或代码
  ❌ 禁止: 安装依赖或修改环境

implementation-planner.md:
  文件位置: .claude/agents/planning/implementation-planner.md
  ✅ 允许: 读取项目历史和进度信息
  ✅ 允许: 任务分解和时间估算
  ✅ 允许: TDD指导和质量控制  
  ✅ 允许: 访问项目管理相关文档
  ✅ 允许: 生成计划文档 (通过TodoWrite)
  ❌ 禁止: 修改项目配置或里程碑
  ❌ 禁止: 执行任何实际开发操作
```

**实施类Agent (读写权限) - 基于现有架构**:
```yaml
implementation-planner.md:
  文件位置: .claude/agents/planning/implementation-planner.md
  ✅ 允许: 完整的文件读写权限 (Level 1)
  ✅ 允许: 创建新文件、类、方法
  ✅ 允许: 修改非核心业务逻辑
  ✅ 允许: 执行TDD任务分解和实施
  ✅ 允许: 生成tasks.md和执行开发任务
  ⚠️ 限制: 需要在前三个agent的规划范围内操作
  ⚠️ 限制: 遵循GitFlow和代码规范
  ❌ 禁止: 超越设计范围的修改
  ❌ 禁止: 未经前期分析确认的高风险操作
```

---

## 7. 配置实现

### 📁 .claude 目录结构设计

```
.claude/
├── agents/                          # 代理配置目录
│   ├── security-guard.yml          # 安全守护代理配置
│   ├── planning-agent.yml          # 规划代理配置
│   ├── refactor-agent.yml          # 重构代理配置
│   ├── fix-agent.yml               # 修复代理配置
│   └── shared/                     # 共享配置
│       ├── risk-assessment.yml     # 风险评估规则
│       ├── file-classification.yml # 文件分类规则
│       └── validation-patterns.yml # 验证模式
├── commands/                       # 命令扩展
│   ├── safe-develop.md            # 安全开发命令
│   ├── smart-fix.md               # 智能修复命令
│   └── guided-refactor.md         # 引导重构命令
├── hooks/                          # 自动化钩子
│   ├── pre-modify-check.sh        # 修改前检查
│   ├── post-modify-validate.sh    # 修改后验证
│   └── security-audit.sh          # 安全审计
└── templates/                      # 模板文件
    ├── fix-plan-template.md       # 修复计划模板
    ├── refactor-analysis.md       # 重构分析模板
    └── feature-design.md          # 功能设计模板
```

### ⚙️ security-guard.yml 配置示例

```yaml
# Security Guard Agent 核心配置
agent:
  name: "Security Guard Agent"
  version: "1.0.0"
  description: "智能权限判断和代理调度中心"

# 风险评估参数
risk_assessment:
  file_importance_weights:
    entity: 4
    config: 4
    service: 3
    controller: 3
    repository: 3
    dto: 2
    util: 2
    test: 1
    doc: 1
    
  operation_type_weights:
    delete: 4
    modify_core: 3
    modify_impl: 2
    add_method: 1
    add_file: 1
    
  impact_scope_multipliers:
    database: 2.0
    security: 2.0
    cross_module: 1.5
    single_module: 1.0

# 权限级别阈值
permission_levels:
  level_4_threshold: 80  # 绝对禁止
  level_3_threshold: 60  # Fix Agent
  level_2_threshold: 40  # Refactor Agent  
  level_1_threshold: 20  # Planning Agent
  level_0_threshold: 0   # 直接执行

# 自动代理选择规则
agent_selection:
  intent_keywords:
    fix_agent: ["修复", "fix", "bug", "问题", "错误"]
    refactor_agent: ["重构", "refactor", "优化", "性能", "改进"]
    planning_agent: ["新增", "添加", "实现", "开发", "功能"]
    
  forced_routes:
    - pattern: "**/*Entity*.java"
      min_level: 3
    - pattern: "**/application*.yml"  
      min_level: 3
    - pattern: "**/*Security*.java"
      min_level: 3

# 学习和优化
learning:
  enabled: true
  success_rate_tracking: true
  pattern_recognition: true
  auto_adjustment: false  # 手动确认后调整
```

### 🎯 planning-agent.yml 配置示例

```yaml
# Planning Agent 专门配置
agent:
  name: "Planning Agent"
  specialization: "新功能开发和架构规划"
  max_risk_level: 1

# 允许的操作类型
allowed_operations:
  - add_file
  - add_class
  - add_method
  - extend_logic
  - create_api
  
# 禁止的操作类型  
forbidden_operations:
  - modify_signature
  - delete_code
  - modify_core_logic
  - change_config

# 工作流程模板
workflow:
  steps:
    1: "需求分析和理解"
    2: "架构设计和规划"  
    3: "实施计划制定"
    4: "用户确认等待"
    5: "分步骤实施"
    6: "每步验证测试"
    
# 输出模板
templates:
  planning_report: "templates/feature-design.md"
  implementation_plan: "templates/implementation-plan.md"

# 验证要求
validation:
  pre_implementation:
    - architecture_review
    - dependency_analysis
    - user_confirmation
  post_implementation:
    - compile_check
    - startup_test
    - integration_test
```

### 🔧 命令扩展示例

**`.claude/commands/safe-develop.md`**

```markdown
# 安全开发命令

## 命令格式
`@safe-develop [功能描述]`

## 自动流程
1. **风险评估** - 自动分析操作风险级别
2. **代理选择** - 智能选择最合适的代理
3. **计划制定** - 制定详细实施方案
4. **用户确认** - 等待用户批准计划
5. **安全实施** - 按计划分步骤执行
6. **验证测试** - 完整的启动和功能测试

## 使用示例
```bash
@safe-develop 添加用户密码重置功能
# → 自动调用Planning Agent，制定实施计划

@safe-develop 修复登录状态丢失问题  
# → 自动调用Fix Agent，进行问题诊断

@safe-develop 优化数据导出性能
# → 自动调用Refactor Agent，进行性能分析
```

## 安全保障
- ✅ 自动风险评估和级别判断
- ✅ 专门代理负责对应领域
- ✅ 强制测试验证要求
- ✅ 完整审计日志记录
- ✅ 快速回滚机制
```

---

## 8. 使用示例

### 📝 典型使用场景

#### 场景1：新功能开发（Level 1 - Planning Agent）

```
👤 用户: "实现用户密码重置功能"

🛡️ Security Guard: 
   - 分析：新功能开发，涉及AuthController + UserService
   - 风险级别：Level 1（新增功能，低风险）
   - 代理选择：Planning Agent

🎯 Planning Agent执行流程:
   1. 需求分析 ✅
      - 功能：用户可通过邮箱重置密码
      - 涉及：前端表单 + 后端API + 邮件服务
   
   2. 架构设计 ✅  
      - 新增PasswordResetController
      - 扩展UserService.resetPassword()
      - 新增PasswordResetRequest DTO
   
   3. 实施计划 ✅
      - Step 1: 创建DTO和请求对象
      - Step 2: 实现后端控制器和服务
      - Step 3: 集成邮件服务
      - Step 4: 前端表单开发
   
   4. 用户确认 ⏳
      "计划已制定，请确认后开始实施"
   
   5. 分步实施 ⏳
      每一步完成后暂停，等待测试验证
```

#### 场景2：性能优化（Level 2 - Refactor Agent）

```
👤 用户: "优化债权查询的性能问题"

🛡️ Security Guard:
   - 分析：修改现有Service查询逻辑
   - 风险级别：Level 2（重构现有代码，中等风险）
   - 代理选择：Refactor Agent

🔧 Refactor Agent执行流程:
   1. 性能分析 ✅
      - 发现：DebtService.getAllDebts() N+1查询问题
      - 瓶颈：缺少JPA @EntityGraph优化
   
   2. 影响评估 ✅
      - 影响范围：单一Service方法
      - 兼容性：保持API接口不变
      - 风险：低（仅优化查询，不改变业务逻辑）
   
   3. 重构方案 ✅
      - 添加@EntityGraph到Repository
      - 优化SQL查询语句
      - 添加分页支持
   
   4. 安全实施 ⏳
      - 保持原方法签名不变
      - 逐步替换查询实现
      - 保留原逻辑作为fallback
   
   5. 性能验证 ⏳
      - 基准测试对比
      - 启动测试验证
      - 回滚预案准备
```

#### 场景3：核心bug修复（Level 3 - Fix Agent）

```
👤 用户: "修复用户登录后状态丢失的问题"

🛡️ Security Guard:
   - 分析：涉及安全认证核心逻辑
   - 风险级别：Level 3（核心功能修复，高风险）
   - 代理选择：Fix Agent

🚨 Fix Agent执行流程:
   1. 问题诊断 ✅
      - 症状：用户登录后刷新页面状态丢失
      - 根因：JWT Token未正确存储到localStorage
      - 位置：AuthService.login()方法
   
   2. 根因分析 ✅
      - 代码审查：发现token存储逻辑缺失
      - 日志分析：确认token生成正常
      - 定位：前端AuthService.js第47行
   
   3. 修复方案 ✅
      - 最小影响：仅修改token存储逻辑
      - 兼容性：保持现有API不变
      - 测试：重点测试登录流程
   
   4. 风险控制 ✅
      - 影响范围：单一方法内部实现
      - 回滚预案：保留原代码注释
      - 验证方案：完整登录流程测试
   
   5. 修复实施 ⏳
      - 修改AuthService.login()
      - 添加token持久化逻辑
      - 添加错误处理机制
   
   6. 验证测试 ⏳
      - 登录状态持久化测试
      - 刷新页面状态保持测试
      - 完整启动测试
```

#### 场景4：禁止操作（Level 4 - 系统拒绝）

```
👤 用户: "删除债权管理相关的数据库表"

🛡️ Security Guard:
   - 分析：删除数据库结构，极高风险
   - 风险级别：Level 4（数据库结构变更）
   - 处理：系统直接拒绝

❌ 系统响应:
"⚠️ 操作被安全系统拒绝

🚫 禁止原因：
- 操作类型：数据库结构修改（Level 4）
- 风险评估：极高风险（评分：95/100）
- 影响范围：整个系统数据结构

🔄 建议替代方案：
1. 如需要修改表结构，请使用数据库迁移脚本
2. 如需要删除数据，请使用软删除功能
3. 如确实需要此操作，请联系系统管理员手动处理

📋 如有疑问，请查看文档：docs/database/schema-modification-guide.md"
```

---

## 9. 实施路线图

### 🗓️ Phase 1: 基础框架（Week 1-2）

**目标**：搭建核心架构和基础代理

```yaml
Week 1: 核心组件
  - ✅ 创建.claude目录结构
  - ✅ 实现Security Guard Agent配置
  - ✅ 设计风险评估算法
  - ✅ 创建基础代理配置模板

Week 2: 集成CLAUDE.md
  - ⏳ 修改CLAUDE.md，集成分层安全系统
  - ⏳ 创建基础命令扩展
  - ⏳ 实现简单的权限判断逻辑
  - ⏳ 添加自动化hooks
```

### 🚀 Phase 2: 代理实现（Week 3-4）

**目标**：实现三个专门代理的核心功能

```yaml
Week 3: Planning & Refactor Agent
  - ⏳ 实现Planning Agent完整工作流
  - ⏳ 实现Refactor Agent重构逻辑
  - ⏳ 创建代理间通信机制
  - ⏳ 添加详细的审计日志

Week 4: Fix Agent & 验证系统
  - ⏳ 实现Fix Agent诊断和修复流程
  - ⏳ 完善test-startup.sh集成
  - ⏳ 实现回滚预案自动生成
  - ⏳ 添加完整的错误处理
```

### 🎯 Phase 3: 智能优化（Week 5-6）

**目标**：添加学习和优化能力

```yaml
Week 5: 智能决策  
  - ⏳ 实现历史模式学习
  - ⏳ 优化风险评估算法
  - ⏳ 添加成功率跟踪
  - ⏳ 实现智能代理选择

Week 6: 系统完善
  - ⏳ 完整的文档和示例
  - ⏳ 性能优化和调试
  - ⏳ 用户体验改进
  - ⏳ 安全审计和测试
```

### 📊 成功指标

**技术指标**：
- ✅ 风险评估准确率 > 90%
- ✅ 代理选择正确率 > 95%  
- ✅ 测试通过率 > 98%
- ✅ 回滚成功率 = 100%

**效率指标**：
- ✅ 开发效率提升 > 30%
- ✅ Bug引入率降低 > 50%
- ✅ 代码质量评分提升 > 40%
- ✅ 技术债务减少 > 60%

---

## 📋 总结

### 🎯 核心价值

1. **智能化安全控制** - 自动风险评估，无需人工判断
2. **专业化代理分工** - 不同代理专注不同领域，提高质量
3. **渐进式权限管理** - 从宽松到严格的层级化控制
4. **学习型系统优化** - 基于历史数据持续改进策略

### 🔮 未来扩展

1. **多项目支持** - 适配不同类型的项目架构
2. **团队协作** - 支持多人开发的权限协调
3. **云端学习** - 跨项目的模式识别和优化
4. **可视化界面** - 图形化的权限管理和审计界面

### 🛡️ 安全保障

- **完全透明** - 所有决策过程都有详细记录
- **快速回滚** - 任何操作都可以快速撤销
- **学习优化** - 系统持续学习和改进
- **人工干预** - 关键决策仍可人工确认

---

*本系统设计基于ultrathink深度分析，旨在解决AI开发中安全性与效率的核心矛盾，为企业级项目提供智能化的安全开发保障。*
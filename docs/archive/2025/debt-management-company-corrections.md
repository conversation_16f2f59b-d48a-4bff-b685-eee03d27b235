# 🏢 债权管理公司对应关系修正方案

## 📋 概述
基于最新的user_system.companies表管理公司分配方案，检查overdue_debt_db中所有表的债权人和管理公司对应关系，发现多处不一致需要修正。

---

## 🎯 修正原则
根据user_system.companies表中的management_company字段作为标准，修正overdue_debt_db中所有表的管理公司字段。

---

## 🔍 发现的问题

### ❌ 主要不一致问题

#### 1️⃣ **深圳万润科技股份有限公司**
- **当前值**: "公司总部"
- **正确值**: "万润科技"
- **影响表**: 减值准备表、处置表、新增表、诉讼表、非诉讼表

#### 2️⃣ **湖北长江万润半导体技术有限公司**
- **当前值**: "万润半导体"
- **正确值**: "万润科技"
- **影响表**: 减值准备表、处置表、新增表、非诉讼表

#### 3️⃣ **万润科技湖北有限公司**
- **当前值**: "湖北万润"
- **正确值**: "万润科技"
- **影响表**: 减值准备表、处置表、新增表、诉讼表

#### 4️⃣ **湖北长江万润科技有限公司**
- **当前值**: "长江万润"、"万润科技"、"亿万无线"（不一致）
- **正确值**: "万润科技"
- **影响表**: 减值准备表、处置表、新增表、诉讼表、非诉讼表

#### 5️⃣ **北京万润新动科技有限公司**
- **当前值**: "万象新动"
- **正确值**: "万象新动" ✅ 正确，无需修改

#### 6️⃣ **北京万象新动移动科技有限公司**
- **当前值**: "万象新动"
- **正确值**: "万润科技"
- **影响表**: 处置表、新增表、诉讼表、非诉讼表

#### 7️⃣ **北京亿万无线信息技术有限公司**
- **当前值**: "万润科技"、"亿万无线"（不一致）
- **正确值**: "万润科技"
- **影响表**: 减值准备表、处置表、新增表、诉讼表、非诉讼表

#### 8️⃣ **广东恒润光电有限公司**
- **当前值**: "恒润光电"
- **正确值**: "万润科技"
- **影响表**: 减值准备表、处置表、新增表、诉讼表、非诉讼表

#### 9️⃣ **深圳日上光电有限公司**
- **当前值**: "日上光电"
- **正确值**: "万润科技"
- **影响表**: 处置表、新增表、诉讼表、非诉讼表

#### 🔟 **杭州信立传媒有限公司**
- **当前值**: "信立传媒"
- **正确值**: "万润科技"
- **影响表**: 处置表、新增表、非诉讼表

#### 1️⃣1️⃣ **重庆万润光电有限公司**
- **当前值**: "重庆万润"
- **正确值**: "万润科技"
- **影响表**: 非诉讼表

#### 1️⃣2️⃣ **长春万润光电有限公司**
- **当前值**: "长春万润"
- **正确值**: "万润科技"
- **影响表**: 减值准备表、处置表、新增表、诉讼表、非诉讼表

#### 1️⃣3️⃣ **杭州传视广告有限公司**
- **当前值**: "信立传媒"、"万润科技"（不一致）
- **正确值**: "信立传媒" ✅ 部分正确，需统一

#### 1️⃣4️⃣ **新疆橙思广告有限公司**
- **当前值**: "信立传媒"、"万润科技"、"新疆橙思"（不一致）
- **正确值**: "信立传媒" ✅ 部分正确，需统一

### ⚠️ 不在companies表中的债权人
以下债权人在overdue_debt_db中存在，但不在user_system.companies表中，**建议保持原状或删除**：

1. **云南万润新能源有限公司** - 当前管理公司："云南万润"
2. **北京万润阳光能源管理有限公司** - 当前管理公司："万润能源"
3. **北京鼎盛意轩网络营销策划有限公司** - 当前管理公司："鼎盛意轩"
4. **宁波纵凯能源管理有限公司** - 当前管理公司："万润能源"
5. **昆明万润阳光能源科技有限公司** - 当前管理公司："万润能源"
6. **深圳万润综合能源有限公司** - 当前管理公司："万润能源"
7. **深圳天游网络有限公司** - 当前管理公司："亿万无线"
8. **重庆万润翠璟节能科技有限公司** - 当前管理公司："万润能源"

---

## 🔧 建议的修正SQL语句

### 针对减值准备表

```sql
-- 修正减值准备表
UPDATE 减值准备表 SET 管理公司 = '万润科技' WHERE 债权人 = '深圳万润科技股份有限公司';
UPDATE 减值准备表 SET 管理公司 = '万润科技' WHERE 债权人 = '湖北长江万润半导体技术有限公司';
UPDATE 减值准备表 SET 管理公司 = '万润科技' WHERE 债权人 = '万润科技湖北有限公司';
UPDATE 减值准备表 SET 管理公司 = '万润科技' WHERE 债权人 = '湖北长江万润科技有限公司';
UPDATE 减值准备表 SET 管理公司 = '万润科技' WHERE 债权人 = '北京万象新动移动科技有限公司';
UPDATE 减值准备表 SET 管理公司 = '万润科技' WHERE 债权人 = '北京亿万无线信息技术有限公司';
UPDATE 减值准备表 SET 管理公司 = '万润科技' WHERE 债权人 = '广东恒润光电有限公司';
UPDATE 减值准备表 SET 管理公司 = '万润科技' WHERE 债权人 = '杭州信立传媒有限公司';
UPDATE 减值准备表 SET 管理公司 = '万润科技' WHERE 债权人 = '重庆万润光电有限公司';
UPDATE 减值准备表 SET 管理公司 = '万润科技' WHERE 债权人 = '长春万润光电有限公司';
UPDATE 减值准备表 SET 管理公司 = '信立传媒' WHERE 债权人 = '杭州传视广告有限公司';
UPDATE 减值准备表 SET 管理公司 = '信立传媒' WHERE 债权人 = '新疆橙思广告有限公司';
```

### 针对处置表

```sql
-- 修正处置表
UPDATE 处置表 SET 管理公司 = '万润科技' WHERE 债权人 = '深圳万润科技股份有限公司';
UPDATE 处置表 SET 管理公司 = '万润科技' WHERE 债权人 = '湖北长江万润半导体技术有限公司';
UPDATE 处置表 SET 管理公司 = '万润科技' WHERE 债权人 = '万润科技湖北有限公司';
UPDATE 处置表 SET 管理公司 = '万润科技' WHERE 债权人 = '湖北长江万润科技有限公司';
UPDATE 处置表 SET 管理公司 = '万润科技' WHERE 债权人 = '北京万象新动移动科技有限公司';
UPDATE 处置表 SET 管理公司 = '万润科技' WHERE 债权人 = '北京亿万无线信息技术有限公司';
UPDATE 处置表 SET 管理公司 = '万润科技' WHERE 债权人 = '广东恒润光电有限公司';
UPDATE 处置表 SET 管理公司 = '万润科技' WHERE 债权人 = '深圳日上光电有限公司';
UPDATE 处置表 SET 管理公司 = '万润科技' WHERE 债权人 = '杭州信立传媒有限公司';
UPDATE 处置表 SET 管理公司 = '万润科技' WHERE 债权人 = '长春万润光电有限公司';
UPDATE 处置表 SET 管理公司 = '信立传媒' WHERE 债权人 = '杭州传视广告有限公司';
UPDATE 处置表 SET 管理公司 = '信立传媒' WHERE 债权人 = '新疆橙思广告有限公司';
```

### 针对新增表

```sql
-- 修正新增表
UPDATE 新增表 SET 管理公司 = '万润科技' WHERE 债权人 = '深圳万润科技股份有限公司';
UPDATE 新增表 SET 管理公司 = '万润科技' WHERE 债权人 = '湖北长江万润半导体技术有限公司';
UPDATE 新增表 SET 管理公司 = '万润科技' WHERE 债权人 = '万润科技湖北有限公司';
UPDATE 新增表 SET 管理公司 = '万润科技' WHERE 债权人 = '湖北长江万润科技有限公司';
UPDATE 新增表 SET 管理公司 = '万润科技' WHERE 债权人 = '北京万象新动移动科技有限公司';
UPDATE 新增表 SET 管理公司 = '万润科技' WHERE 债权人 = '北京亿万无线信息技术有限公司';
UPDATE 新增表 SET 管理公司 = '万润科技' WHERE 债权人 = '广东恒润光电有限公司';
UPDATE 新增表 SET 管理公司 = '万润科技' WHERE 债权人 = '深圳日上光电有限公司';
UPDATE 新增表 SET 管理公司 = '万润科技' WHERE 债权人 = '杭州信立传媒有限公司';
UPDATE 新增表 SET 管理公司 = '万润科技' WHERE 债权人 = '长春万润光电有限公司';
UPDATE 新增表 SET 管理公司 = '信立传媒' WHERE 债权人 = '杭州传视广告有限公司';
UPDATE 新增表 SET 管理公司 = '信立传媒' WHERE 债权人 = '新疆橙思广告有限公司';
```

### 针对诉讼表

```sql
-- 修正诉讼表
UPDATE 诉讼表 SET 管理公司 = '万润科技' WHERE 债权人 = '深圳万润科技股份有限公司';
UPDATE 诉讼表 SET 管理公司 = '万润科技' WHERE 债权人 = '万润科技湖北有限公司';
UPDATE 诉讼表 SET 管理公司 = '万润科技' WHERE 债权人 = '湖北长江万润科技有限公司';
UPDATE 诉讼表 SET 管理公司 = '万润科技' WHERE 债权人 = '北京万象新动移动科技有限公司';
UPDATE 诉讼表 SET 管理公司 = '万润科技' WHERE 债权人 = '北京亿万无线信息技术有限公司';
UPDATE 诉讼表 SET 管理公司 = '万润科技' WHERE 债权人 = '广东恒润光电有限公司';
UPDATE 诉讼表 SET 管理公司 = '万润科技' WHERE 债权人 = '深圳日上光电有限公司';
UPDATE 诉讼表 SET 管理公司 = '万润科技' WHERE 债权人 = '长春万润光电有限公司';
UPDATE 诉讼表 SET 管理公司 = '信立传媒' WHERE 债权人 = '新疆橙思广告有限公司';
```

### 针对非诉讼表

```sql
-- 修正非诉讼表
UPDATE 非诉讼表 SET 管理公司 = '万润科技' WHERE 债权人 = '深圳万润科技股份有限公司';
UPDATE 非诉讼表 SET 管理公司 = '万润科技' WHERE 债权人 = '湖北长江万润半导体技术有限公司';
UPDATE 非诉讼表 SET 管理公司 = '万润科技' WHERE 债权人 = '湖北长江万润科技有限公司';
UPDATE 非诉讼表 SET 管理公司 = '万润科技' WHERE 债权人 = '北京万象新动移动科技有限公司';
UPDATE 非诉讼表 SET 管理公司 = '万润科技' WHERE 债权人 = '北京亿万无线信息技术有限公司';
UPDATE 非诉讼表 SET 管理公司 = '万润科技' WHERE 债权人 = '广东恒润光电有限公司';
UPDATE 非诉讼表 SET 管理公司 = '万润科技' WHERE 债权人 = '重庆万润光电有限公司';
UPDATE 非诉讼表 SET 管理公司 = '万润科技' WHERE 债权人 = '长春万润光电有限公司';
UPDATE 非诉讼表 SET 管理公司 = '信立传媒' WHERE 债权人 = '杭州传视广告有限公司';
UPDATE 非诉讼表 SET 管理公司 = '信立传媒' WHERE 债权人 = '新疆橙思广告有限公司';
```

---

## 📊 影响范围统计

### 需要修正的记录数量预估
- **减值准备表**: 约 100+ 条记录
- **处置表**: 约 50+ 条记录  
- **新增表**: 约 30+ 条记录
- **诉讼表**: 约 40+ 条记录
- **非诉讼表**: 约 80+ 条记录

### 主要影响的债权人
1. 深圳万润科技股份有限公司（影响最大）
2. 长春万润光电有限公司
3. 湖北长江万润科技有限公司
4. 北京万象新动移动科技有限公司
5. 广东恒润光电有限公司

---

## ⚠️ 注意事项

1. **数据备份**: 执行修正前务必备份overdue_debt_db数据库
2. **分批执行**: 建议分表分批执行，避免锁表时间过长
3. **验证结果**: 每个表修正后都需要验证结果的正确性
4. **业务影响**: 修正期间可能影响报表生成，建议在非业务时间执行

---

## 🔍 验证查询

执行修正后，可使用以下查询验证结果：

```sql
-- 验证减值准备表
SELECT DISTINCT 债权人, 管理公司 FROM 减值准备表 ORDER BY 债权人;

-- 验证处置表
SELECT DISTINCT 债权人, 管理公司 FROM 处置表 ORDER BY 债权人;

-- 验证新增表
SELECT DISTINCT 债权人, 管理公司 FROM 新增表 ORDER BY 债权人;

-- 验证诉讼表
SELECT DISTINCT 债权人, 管理公司 FROM 诉讼表 ORDER BY 债权人;

-- 验证非诉讼表
SELECT DISTINCT 债权人, 管理公司 FROM 非诉讼表 ORDER BY 债权人;
```

---

*文档创建时间: 2025-08-13*  
*分析基准: user_system.companies.management_company字段*
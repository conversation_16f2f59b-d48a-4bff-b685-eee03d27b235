# 经营调度会看板数据实施方案（精简版）

## 核心任务
基于现有的前端界面和API接口，实现数据查询逻辑和Excel模板填充功能。

## 需要实现的功能

### 1. 数据查询逻辑
根据传入的年份和月份参数，从数据库查询以下数据：

#### A. 期初存量债权 (填入B6)
```sql
-- 当年份为2025时，查询2024年12月的本月末债权余额汇总
SELECT SUM(本月末债权余额) as 期初存量债权
FROM 减值准备表 
WHERE 年份 = 2024 AND 月份 = 12 
  AND 期间 != '2025年新增债权'
```

#### B. 本年累计新增债权 (填入B9)
```sql
-- 基于表9数据，计算每月新增金额累计到指定月份
SELECT SUM(
  CASE WHEN 月份 = 1 THEN `1月` ELSE 0 END +
  CASE WHEN 月份 <= 2 THEN `2月` ELSE 0 END +
  -- ... 累计到指定月份
) as 本年累计新增
FROM 新增表 
WHERE 年份 = ? 
```

#### C. 存量债权本年累计处置 (填入D6)
```sql
-- 减值准备表本月处置债权累计 - 新增债权处置金额
SELECT (
  SELECT SUM(本月处置债权) FROM 减值准备表 WHERE 年份 = ? AND 月份 <= ?
) - (
  SELECT SUM(新增债权处置金额) FROM 新增表 WHERE 年份 = ? AND 月份 <= ?
) as 存量债权累计处置
```

#### D. 本月清收金额 (填入C6)
```sql
-- 本月累计 - 上月累计
-- 需要分别查询本月和上月的累计处置金额，然后计算差值
```

#### E. 处置方式统计 (填入B13, B14, B17)
```sql
-- 现金处置、分期还款、资产抵债、其他方式累计
SELECT 
  SUM(现金处置) as 现金处置总额,
  SUM(分期还款) as 分期还款总额,
  SUM(资产抵债 + 其他方式) as 资产抵债其他总额
FROM 处置表 
WHERE 年份 = ? AND 月份 <= ?
```

### 2. 管理公司汇总数据

#### A. 存量债权按公司汇总 (填入B22,C22,D22)
```sql
-- 按管理公司汇总存量债权数据，按期初存量债权降序
SELECT 
  管理公司,
  期初存量债权,
  存量债权累计处置
FROM (
  -- 期初数据
  SELECT 管理公司, SUM(本月末债权余额) as 期初存量债权
  FROM 减值准备表 WHERE 年份 = 2024 AND 月份 = 12
  GROUP BY 管理公司
) t1
LEFT JOIN (
  -- 处置数据
  SELECT 管理公司, SUM(存量债权处置金额) as 存量债权累计处置
  FROM ... 
  GROUP BY 管理公司
) t2 ON t1.管理公司 = t2.管理公司
ORDER BY 期初存量债权 DESC
```

#### B. 新增债权按公司汇总 (填入I22,J22,K22)
```sql
-- 按管理公司汇总新增债权数据，按新增金额降序
SELECT 
  管理公司,
  新增债权金额,
  累计回收金额
FROM 表9数据按公司汇总
ORDER BY 新增债权金额 DESC
```

### 3. 明细数据处理

#### A. 存量债权处置明细 (填入N5,O5,P5)
- 筛选条件：前80%且处置金额>100万
- 排序：按处置金额降序

#### B. 新增债权处置明细 (填入R5,S5,T5)
- 新增债权处置明细数据
- 排序：按处置金额降序

#### C. 新增债权明细 (填入N15,O15,P15)
- 筛选条件：前80%且新增金额>100万
- 排序：按新增金额降序

#### D. 新增债权余额明细 (填入R15,S15,T15)
- 新增债权余额数据
- 排序：按余额降序

### 4. 核心业务逻辑

#### A. 处置金额拆分逻辑
```java
// 每个债权的处置金额不能超过当年新增金额
if (处置金额 <= 当年新增金额) {
    新增债权处置 = 处置金额;
    存量债权处置 = 0;
} else {
    新增债权处置 = 当年新增金额;
    存量债权处置 = 处置金额 - 当年新增金额;
}
```

#### B. 前80%筛选算法
```java
// 1. 按金额降序排列
// 2. 取前80%数量的记录
int top80Count = (int) Math.ceil(list.size() * 0.8);
List<T> top80Percent = list.subList(0, top80Count);
// 3. 再筛选金额>100万的记录
List<T> filtered = top80Percent.stream()
    .filter(item -> item.getAmount().compareTo(new BigDecimal("100")) > 0)
    .collect(Collectors.toList());
```

#### C. 表9处置金额修正
```java
// 检查表9中处置金额是否超过新增金额，如果超过则修正
for (NewDebtDetail debt : table9Data) {
    if (debt.getDisposalAmount().compareTo(debt.getNewAmount()) > 0) {
        debt.setDisposalAmount(debt.getNewAmount()); // 限制为新增金额
        // 记录超出部分到日志
    }
}
```

## 实施步骤

### 第1步：扩展现有Repository
在现有的Repository中添加上述查询方法：
- `ImpairmentReserveRepository` - 减值准备表查询
- `DisposalRepository` - 处置表查询  
- `NewDebtRepository` - 新增表查询

### 第2步：实现数据处理Service
创建 `ManagementBoardDataService` 实现：
- 各种数据查询方法
- 处置金额拆分逻辑
- 前80%筛选逻辑
- 表9数据修正逻辑

### 第3步：实现Excel填充逻辑
在现有的 `ManagementBoardExportService` 中实现：
- 加载Excel模板
- 将查询到的数据填充到指定位置
- 生成并返回Excel文件

### 第4步：修正表9导出逻辑
检查并修正现有的表9导出中处置金额超出新增金额的问题。

## Excel位置映射表

| 数据类型 | Excel位置 | 数据来源 |
|---------|-----------|----------|
| 期初存量债权 | B6 | 减值准备表2024年12月汇总 |
| 本年累计新增 | B9 | 表9每月新增数据累计 |
| 存量债权本年累计处置 | D6 | 计算得出 |
| 本月清收金额 | C6 | 本月-上月差值 |
| 现金处置 | B14 | 处置表现金处置累计 |
| 分期还款 | B13 | 处置表分期还款累计 |
| 资产抵债+其他 | B17 | 处置表资产抵债+其他累计 |
| 存量债权公司汇总 | B22,C22,D22 | 按公司汇总存量数据 |
| 新增债权公司汇总 | I22,J22,K22 | 按公司汇总新增数据 |
| 存量债权明细 | N5,O5,P5 | 前80%且>100万 |
| 新增债权处置明细 | R5,S5,T5 | 新增债权处置 |
| 新增债权明细 | N15,O15,P15 | 前80%且>100万 |
| 新增债权余额明细 | R15,S15,T15 | 按余额排序 |

## 关键注意事项

1. **Excel模板位置**：确保模板文件 `经营调度会看板模板.xlsx` 已放在正确位置
2. **数据一致性**：处置金额拆分逻辑必须严格执行
3. **表9修正**：必须解决处置金额超出新增金额的问题
4. **权限控制**：现有API已有ADMIN权限控制，无需修改
5. **性能优化**：大数据量时注意查询性能

这个精简方案专注于核心的数据处理逻辑，避免了重复的架构设计和已有功能的描述。

# 经营调度会看板数据实施方案（基于现有实现）

## 现状分析

### ✅ 已完成功能
1. **前端界面完整** - `ManagementBoardExportRow.js` 组件已实现
   - 年份、月份选择器
   - 导出按钮和进度显示
   - 错误处理和通知机制

2. **API接口完整** - `/api/export/managementBoard` 已实现
   - Controller: `ExcelExportController.exportManagementBoard()`
   - Service: `DebtManagementService.exportManagementBoard()`
   - 基础权限控制通过现有认证机制

3. **基础服务架构** - `ManagementBoardExportService` 已实现
   - Excel模板加载机制
   - 基础数据查询和填充
   - 错误处理和日志记录

4. **Excel模板文件** - `经营调度会看板模板.xlsx` 已存在
   - 位置: `api-gateway/src/main/resources/templates/`

### ❌ 需要完善功能
1. **复杂业务逻辑缺失** - 需要实现10个具体需求点
2. **Excel精确位置填充** - 当前只是基础表格填充
3. **多数据源查询** - 减值准备表、处置表等复杂查询
4. **数据处理算法** - 处置金额拆分、前80%筛选等

## 核心任务
基于现有架构，完善复杂业务逻辑和Excel精确填充功能。

## 需要实现的10个具体需求

### 需求1: 期初存量债权计算 (B6位置)
**当前状态**: ❌ 未实现
**实现位置**: `ManagementBoardExportService.fillExcelData()`

```java
// 需要添加的逻辑
if ("2025".equals(year)) {
    // 查询2024年12月减值准备表本月末余额汇总
    BigDecimal stockDebtBegin = impairmentReserveRepository
        .findStockDebtBeginAmount("2024", "12");
    cells.get(5, 1).setValue(stockDebtBegin); // B6
}
```

### 需求2: 本年累计新增债权 (B9位置)
**当前状态**: ❌ 未实现
**实现位置**: `ManagementBoardExportService.fillExcelData()`

```java
// 基于表9数据计算累计新增
BigDecimal yearCumulativeNew = newDebtRepository
    .findCumulativeNewAmount(year, month);
cells.get(8, 1).setValue(yearCumulativeNew); // B9
```

### 需求3: 新增债权处置金额拆分逻辑
**当前状态**: ❌ 未实现
**关键逻辑**: 处置金额不能超过当年新增金额

```java
// 需要实现的核心算法
public DebtDisposalResult splitDisposalAmount(
    BigDecimal disposalAmount, BigDecimal newAmount) {

    if (disposalAmount.compareTo(newAmount) <= 0) {
        return new DebtDisposalResult(disposalAmount, BigDecimal.ZERO);
    } else {
        BigDecimal newDebtDisposal = newAmount;
        BigDecimal stockDebtDisposal = disposalAmount.subtract(newAmount);
        return new DebtDisposalResult(newDebtDisposal, stockDebtDisposal);
    }
}
```

### 需求4: 存量债权本年累计处置 (D6位置)
**当前状态**: ❌ 未实现
**计算逻辑**: 减值准备表累计处置 - 新增债权处置

```java
// 计算存量债权累计处置
BigDecimal totalDisposal = impairmentReserveRepository
    .findTotalDisposalAmount(year, month);
BigDecimal newDebtDisposal = calculateNewDebtDisposal(year, month);
BigDecimal stockDebtDisposal = totalDisposal.subtract(newDebtDisposal);
cells.get(5, 3).setValue(stockDebtDisposal); // D6
```

### 需求5: 管理公司汇总数据
**当前状态**: ❌ 未实现
**填充位置**: B22,C22,D22 (存量) 和 I22,J22,K22 (新增)

```java
// 存量债权按公司汇总 (B22开始)
List<CompanySummaryDTO> stockCompanySummary =
    getStockDebtCompanySummary(year, month);
fillCompanySummary(cells, stockCompanySummary, 21, 1);

// 新增债权按公司汇总 (I22开始)
List<CompanySummaryDTO> newCompanySummary =
    getNewDebtCompanySummary(year, month);
fillCompanySummary(cells, newCompanySummary, 21, 8);
```

### 需求6: 处置方式统计 (B13, B14, B17位置)
**当前状态**: ❌ 未实现

```java
// 从处置表查询各种处置方式累计金额
DisposalMethodsDTO methods = disposalRepository
    .findDisposalMethodsSummary(year, month);
cells.get(12, 1).setValue(methods.getInstallmentPayment()); // B13
cells.get(13, 1).setValue(methods.getCashDisposal()); // B14
cells.get(16, 1).setValue(methods.getAssetDebtAndOther()); // B17
```

### 需求7: 债权明细数据处理
**当前状态**: ❌ 未实现
**关键逻辑**: 前80%筛选 + 金额>100万筛选

```java
// 存量债权处置明细 (N5开始)
List<DebtDetailDTO> stockDetails = getStockDebtDetails(year, month);
List<DebtDetailDTO> filteredStock = filterTop80Percent(stockDetails, 100);
fillDebtDetails(cells, filteredStock, 4, 13);

// 新增债权明细 (N15开始)
List<DebtDetailDTO> newDetails = getNewDebtDetails(year, month);
List<DebtDetailDTO> filteredNew = filterTop80Percent(newDetails, 100);
fillDebtDetails(cells, filteredNew, 14, 13);
```

## 需要扩展的Repository方法

### 1. ImpairmentReserveRepository 扩展

```java
// 需要添加的查询方法
@Query("SELECT SUM(i.本月末债权余额) FROM ImpairmentReserve i " +
       "WHERE i.年份 = :year AND i.月份 = :month AND i.期间 != :excludePeriod")
BigDecimal findStockDebtBeginAmount(@Param("year") String year,
                                   @Param("month") String month,
                                   @Param("excludePeriod") String excludePeriod);

@Query("SELECT SUM(i.本月处置债权) FROM ImpairmentReserve i " +
       "WHERE i.年份 = :year AND i.月份 <= :month")
BigDecimal findTotalDisposalAmount(@Param("year") String year,
                                  @Param("month") String month);

@Query("SELECT i.管理公司, SUM(i.本月末债权余额) as amount, SUM(i.本月处置债权) as disposal " +
       "FROM ImpairmentReserve i WHERE i.年份 = :year AND i.月份 = :month " +
       "GROUP BY i.管理公司 ORDER BY amount DESC")
List<Object[]> findStockDebtCompanySummary(@Param("year") String year,
                                          @Param("month") String month);
```

### 2. DisposalRepository 扩展
```java
// 处置方式统计查询
@Query("SELECT SUM(d.现金处置) as cash, SUM(d.分期还款) as installment, " +
       "SUM(d.资产抵债 + d.其他方式) as assetAndOther " +
       "FROM Disposal d WHERE d.年份 = :year AND d.月份 <= :month")
Object[] findDisposalMethodsSummary(@Param("year") String year,
                                   @Param("month") String month);
```

### 3. NewDebtRepository 扩展
```java
// 新增债权累计计算
@Query("SELECT n.管理公司, n.债权人, n.债务人, " +
       "SUM(CASE WHEN n.月份 <= :month THEN n.新增金额 ELSE 0 END) as totalNew, " +
       "SUM(CASE WHEN n.月份 <= :month THEN n.处置金额 ELSE 0 END) as totalDisposal " +
       "FROM NewDebt n WHERE n.年份 = :year " +
       "GROUP BY n.管理公司, n.债权人, n.债务人")
List<Object[]> findCumulativeNewDebtData(@Param("year") String year,
                                        @Param("month") String month);
```

## 实施步骤（基于现有架构）

### 第1步: 扩展ManagementBoardExportService
**文件位置**: `services/report-management/src/main/java/com/laoshu198838/service/ManagementBoardExportService.java`

**需要修改的方法**:
1. `fillExcelData()` - 添加精确位置填充逻辑
2. `fetchDashboardData()` - 扩展数据查询逻辑
3. 新增多个私有方法处理复杂业务逻辑

### 第2步: 扩展Repository接口
**需要修改的文件**:
1. `ImpairmentReserveRepository` - 添加减值准备表查询方法
2. `DisposalRepository` - 添加处置表查询方法
3. `NewDebtRepository` - 添加新增表查询方法

### 第3步: 创建新的DTO类
**需要创建的文件**:
1. `CompanySummaryDTO` - 公司汇总数据
2. `DebtDetailDTO` - 债权明细数据
3. `DebtDisposalResult` - 处置拆分结果
4. `DisposalMethodsDTO` - 处置方式统计

### 第4步: 实现核心算法
**需要实现的算法**:
1. 处置金额拆分算法
2. 前80%筛选算法
3. 表9数据修正算法

## 关键修改点分析

### 当前fillExcelData方法的问题
**现有实现**:
```java
// 当前只是简单的表格数据填充
for (int i = 0; i < data.size(); i++) {
    int rowIndex = startRow + i;
    OperationalDashboardDTO dto = data.get(i);
    setCellValueSafely(cells, rowIndex, 0, dto.getSequenceNumber());
    setCellValueSafely(cells, rowIndex, 1, dto.getCreditor());
    // ... 其他列
}
```

**需要改为**:
```java
// 需要实现精确位置填充
private void fillExcelData(Workbook workbook, List<OperationalDashboardDTO> data,
                          String year, String month) throws Exception {
    Worksheet worksheet = workbook.getWorksheets().get(0);
    Cells cells = worksheet.getCells();

    // 1. 填充汇总数据到固定位置
    fillSummaryData(cells, year, month);

    // 2. 填充管理公司汇总数据
    fillCompanySummaryData(cells, year, month);

    // 3. 填充明细数据
    fillDetailData(cells, year, month);

    // 4. 填充表格数据（保留现有逻辑）
    fillTableData(cells, data);
}
```

### 需要新增的核心方法
```java
// 1. 汇总数据填充
private void fillSummaryData(Cells cells, String year, String month) {
    // B6: 期初存量债权
    // B9: 本年累计新增
    // D6: 存量债权本年累计处置
    // C6: 本月清收金额
    // B13,B14,B17: 处置方式统计
}

// 2. 公司汇总数据填充
private void fillCompanySummaryData(Cells cells, String year, String month) {
    // B22,C22,D22: 存量债权公司汇总
    // I22,J22,K22: 新增债权公司汇总
}

// 3. 明细数据填充
private void fillDetailData(Cells cells, String year, String month) {
    // N5,O5,P5: 存量债权处置明细
    // R5,S5,T5: 新增债权处置明细
    // N15,O15,P15: 新增债权明细
    // R15,S15,T15: 新增债权余额明细
}

// 4. 处置金额拆分
private DebtDisposalResult splitDisposalAmount(BigDecimal disposal, BigDecimal newAmount) {
    // 实现拆分逻辑
}

// 5. 前80%筛选
private <T> List<T> filterTop80Percent(List<T> list, BigDecimal minAmount) {
    // 实现筛选逻辑
}
```

## Excel位置映射表

| 数据类型 | Excel位置 | 数据来源 |
|---------|-----------|----------|
| 期初存量债权 | B6 | 减值准备表2024年12月汇总 |
| 本年累计新增 | B9 | 表9每月新增数据累计 |
| 存量债权本年累计处置 | D6 | 计算得出 |
| 本月清收金额 | C6 | 本月-上月差值 |
| 现金处置 | B14 | 处置表现金处置累计 |
| 分期还款 | B13 | 处置表分期还款累计 |
| 资产抵债+其他 | B17 | 处置表资产抵债+其他累计 |
| 存量债权公司汇总 | B22,C22,D22 | 按公司汇总存量数据 |
| 新增债权公司汇总 | I22,J22,K22 | 按公司汇总新增数据 |
| 存量债权明细 | N5,O5,P5 | 前80%且>100万 |
| 新增债权处置明细 | R5,S5,T5 | 新增债权处置 |
| 新增债权明细 | N15,O15,P15 | 前80%且>100万 |
| 新增债权余额明细 | R15,S15,T15 | 按余额排序 |

## 关键注意事项

1. **Excel模板位置**：确保模板文件 `经营调度会看板模板.xlsx` 已放在正确位置
2. **数据一致性**：处置金额拆分逻辑必须严格执行
3. **表9修正**：必须解决处置金额超出新增金额的问题
4. **权限控制**：现有API已有ADMIN权限控制，无需修改
5. **性能优化**：大数据量时注意查询性能

这个精简方案专注于核心的数据处理逻辑，避免了重复的架构设计和已有功能的描述。

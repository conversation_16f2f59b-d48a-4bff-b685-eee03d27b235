# 经营调度会看板数据导出实施方案

## 项目概述

**需求编号**: #001  
**需求名称**: 逾期债权数据导出到经营调度会看板  
**优先级**: 高  
**预计工期**: 10个工作日  

## 需求分析

### 核心功能
将逾期债权数据按照特定业务逻辑导出到经营调度会看板Excel模板中，支持角色权限控制，仅ADMIN角色可执行导出操作。

### 业务逻辑要点
1. **存量债权处理**: 基于减值准备表计算期初余额和累计处置金额
2. **新增债权处理**: 基于表9数据计算累计新增和处置金额
3. **处置金额拆分**: 新增债权处置不能超过当年新增金额，超出部分归为存量债权处置
4. **数据汇总**: 按管理公司维度汇总各类数据
5. **排序规则**: 按金额降序排列，取前80%且金额>100的记录

## 技术架构设计

### 1. 系统架构
```
前端(React) -> API Gateway -> 报表管理服务 -> 数据访问层 -> MySQL数据库
```

### 2. 核心组件
- **Controller**: `ManagementBoardExportController`
- **Service**: `EnhancedManagementBoardExportService`
- **Repository**: 扩展现有Repository接口
- **DTO**: `ManagementBoardDataDTO`
- **权限控制**: 基于Spring Security的角色验证

## 数据流程设计

### 数据源映射
| 业务需求 | 数据源 | 关键字段 |
|---------|--------|----------|
| 期初存量债权 | 减值准备表 | 本月末债权余额(2024年12月) |
| 本年累计新增 | 表9数据 | 每月新增金额累计 |
| 存量债权处置 | 减值准备表 | 本月处置债权 |
| 处置方式统计 | 处置表 | 现金处置、分期还款、资产抵债、其他方式 |

### Excel模板位置映射
| 数据类型 | Excel位置 | 数据来源 |
|---------|-----------|----------|
| 期初存量债权 | B6 | 减值准备表2024年12月汇总 |
| 本年累计新增 | B9 | 表9每月新增数据累计 |
| 存量债权本年累计处置 | D6 | 计算得出 |
| 本月清收金额 | C6 | 本月-上月差值 |
| 现金处置 | B14 | 处置表现金处置累计 |
| 分期还款 | B13 | 处置表分期还款累计 |
| 资产抵债+其他 | B17 | 处置表资产抵债+其他方式累计 |
| 管理公司汇总(存量) | B22,C22,D22 | 按公司汇总存量数据 |
| 管理公司汇总(新增) | I22,J22,K22 | 按公司汇总新增数据 |
| 存量债权明细 | N5,O5,P5 | 前80%且>100万的存量处置 |
| 新增债权处置明细 | R5,S5,T5 | 新增债权处置明细 |
| 新增债权明细 | N15,O15,P15 | 前80%且>100万的新增债权 |
| 新增债权余额明细 | R15,S15,T15 | 新增债权余额排序 |

## 实施计划

### 第一阶段：基础架构搭建 (2天)
1. **权限控制实现**
   - 扩展现有权限验证机制
   - 添加ADMIN角色检查注解

2. **数据访问层扩展**
   - 扩展ImpairmentReserveRepository
   - 扩展DisposalRepository
   - 新增复杂查询方法

3. **DTO设计**
   - ManagementBoardDataDTO
   - CompanySummaryDTO
   - DebtDetailDTO

### 第二阶段：核心业务逻辑 (4天)
1. **数据查询服务**
   - 期初存量债权查询
   - 新增债权数据处理
   - 处置金额拆分逻辑

2. **数据汇总服务**
   - 按管理公司汇总
   - 排序和筛选逻辑
   - 前80%数据筛选

3. **Excel填充服务**
   - 模板加载和验证
   - 数据填充到指定位置
   - 格式化和样式处理

### 第三阶段：表9处置金额修正 (2天)
1. **问题分析**
   - 检查现有表9导出逻辑
   - 识别处置金额超出新增金额的情况

2. **修正实现**
   - 处置金额上限控制
   - 超出部分剔除逻辑
   - 数据一致性验证

### 第四阶段：集成测试 (2天)
1. **单元测试**
   - 各服务方法测试
   - 边界条件测试
   - 异常处理测试

2. **集成测试**
   - 完整导出流程测试
   - 权限控制测试
   - 性能测试

## 关键技术点

### 1. 处置金额拆分算法
```java
// 伪代码
if (处置金额 <= 当年新增金额) {
    新增债权处置 = 处置金额;
    存量债权处置 = 0;
} else {
    新增债权处置 = 当年新增金额;
    存量债权处置 = 处置金额 - 当年新增金额;
}
```

### 2. 前80%筛选逻辑
```java
// 按金额降序排列后取前80%
List<DebtDetail> top80Percent = sortedList.subList(0, (int)(sortedList.size() * 0.8));
// 再筛选金额>100万的记录
List<DebtDetail> filtered = top80Percent.stream()
    .filter(debt -> debt.getAmount().compareTo(new BigDecimal("100")) > 0)
    .collect(Collectors.toList());
```

### 3. Excel位置精确定位
```java
// 使用Aspose.Cells精确定位单元格
cells.get(5, 1).setValue(periodBeginAmount); // B6
cells.get(8, 1).setValue(yearCumulativeNew); // B9
// 动态填充多行数据
for (int i = 0; i < companyData.size(); i++) {
    cells.get(21 + i, 1).setValue(companyData.get(i).getCompanyName()); // B22开始
}
```

## 风险评估与应对

### 高风险项
1. **数据一致性**: 多表关联查询可能存在数据不一致
   - **应对**: 增加数据验证和日志记录

2. **性能问题**: 大量数据查询和Excel操作可能影响性能
   - **应对**: 分批处理、异步导出、缓存优化

3. **Excel模板兼容性**: 模板格式变更可能导致位置错误
   - **应对**: 模板版本控制、位置动态检测

### 中风险项
1. **权限控制**: 角色验证可能存在绕过风险
   - **应对**: 多层权限验证、操作日志记录

2. **异常处理**: 复杂业务逻辑可能产生未预期异常
   - **应对**: 完善异常处理、用户友好提示

## 验收标准

### 功能验收
- [ ] ADMIN角色可正常导出，其他角色被拒绝
- [ ] 所有数据项按需求准确填入Excel对应位置
- [ ] 处置金额拆分逻辑正确执行
- [ ] 表9处置金额超出问题得到修正
- [ ] 前80%筛选逻辑正确实现

### 性能验收
- [ ] 导出时间不超过30秒（1000条记录以内）
- [ ] 内存使用不超过512MB
- [ ] 并发导出支持至少5个用户

### 质量验收
- [ ] 单元测试覆盖率>90%
- [ ] 集成测试通过率100%
- [ ] 代码审查通过
- [ ] 文档完整性检查通过

## 详细技术实现

### 1. 权限控制实现

#### Controller层权限验证
```java
@RestController
@RequestMapping("/api/export")
@PreAuthorize("hasRole('ADMIN')")
public class ManagementBoardExportController {

    @PostMapping("/management-board")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<byte[]> exportManagementBoard(
            @RequestParam String year,
            @RequestParam String month) {
        // 实现导出逻辑
    }
}
```

#### 前端权限控制
```javascript
// 在前端组件中检查用户角色
const { user } = useAuth();
const canExport = user?.role === 'ROLE_ADMIN';

if (!canExport) {
    return <div>权限不足，仅管理员可执行此操作</div>;
}
```

### 2. 核心数据查询SQL

#### 期初存量债权查询
```sql
-- 查询2024年12月本月末债权余额汇总
SELECT
    管理公司,
    SUM(本月末债权余额) as 期初存量债权
FROM 减值准备表
WHERE 年份 = 2024 AND 月份 = 12
    AND 期间 != '2025年新增债权'
GROUP BY 管理公司
ORDER BY 期初存量债权 DESC
```

#### 新增债权累计查询
```sql
-- 基于表9数据计算累计新增
SELECT
    管理公司,
    债权人,
    债务人,
    SUM(CASE WHEN 月份 <= ? THEN 每月新增金额 ELSE 0 END) as 累计新增金额,
    SUM(CASE WHEN 月份 <= ? THEN 处置金额 ELSE 0 END) as 累计处置金额
FROM 新增表
WHERE 年份 = ?
GROUP BY 管理公司, 债权人, 债务人
```

#### 处置方式统计查询
```sql
-- 处置方式汇总
SELECT
    SUM(现金处置) as 现金处置总额,
    SUM(分期还款) as 分期还款总额,
    SUM(资产抵债) as 资产抵债总额,
    SUM(其他方式) as 其他方式总额
FROM 处置表
WHERE 年份 = ? AND 月份 <= ?
```

### 3. 处置金额拆分核心算法

```java
public class DebtDisposalSplitter {

    public DebtDisposalResult splitDisposal(
            BigDecimal disposalAmount,
            BigDecimal newDebtAmount) {

        DebtDisposalResult result = new DebtDisposalResult();

        if (disposalAmount.compareTo(newDebtAmount) <= 0) {
            // 处置金额 <= 新增金额，全部归为新增债权处置
            result.setNewDebtDisposal(disposalAmount);
            result.setStockDebtDisposal(BigDecimal.ZERO);
        } else {
            // 处置金额 > 新增金额，拆分处理
            result.setNewDebtDisposal(newDebtAmount);
            result.setStockDebtDisposal(disposalAmount.subtract(newDebtAmount));
        }

        return result;
    }
}
```

### 4. Excel数据填充实现

```java
public class ManagementBoardExcelFiller {

    public void fillExcelData(Workbook workbook, ManagementBoardDataDTO data) {
        Worksheet worksheet = workbook.getWorksheets().get(0);
        Cells cells = worksheet.getCells();

        // 填充汇总数据
        cells.get(5, 1).setValue(data.getStockDebtBeginAmount()); // B6
        cells.get(8, 1).setValue(data.getNewDebtCumulativeAmount()); // B9
        cells.get(5, 3).setValue(data.getStockDebtCumulativeDisposal()); // D6
        cells.get(5, 2).setValue(data.getMonthCollectionAmount()); // C6

        // 填充处置方式数据
        cells.get(13, 1).setValue(data.getCashDisposal()); // B14
        cells.get(12, 1).setValue(data.getInstallmentPayment()); // B13
        cells.get(16, 1).setValue(data.getAssetDebtAndOther()); // B17

        // 填充管理公司汇总数据
        fillCompanySummary(cells, data.getStockDebtCompanySummary(), 21, 1); // B22开始
        fillCompanySummary(cells, data.getNewDebtCompanySummary(), 21, 8); // I22开始

        // 填充明细数据
        fillDebtDetails(cells, data.getStockDebtDetails(), 4, 13); // N5开始
        fillDebtDetails(cells, data.getNewDebtDisposalDetails(), 4, 17); // R5开始
        fillDebtDetails(cells, data.getNewDebtDetails(), 14, 13); // N15开始
        fillDebtDetails(cells, data.getNewDebtBalanceDetails(), 14, 17); // R15开始
    }

    private void fillCompanySummary(Cells cells, List<CompanySummaryDTO> summaries,
                                   int startRow, int startCol) {
        for (int i = 0; i < summaries.size(); i++) {
            CompanySummaryDTO summary = summaries.get(i);
            cells.get(startRow + i, startCol).setValue(summary.getCompanyName());
            cells.get(startRow + i, startCol + 1).setValue(summary.getAmount1());
            cells.get(startRow + i, startCol + 2).setValue(summary.getAmount2());
        }
    }
}
```

### 5. 表9处置金额修正逻辑

```java
public class Table9DisposalAmountCorrector {

    public void correctDisposalAmounts(List<NewDebtDetailDTO> table9Data) {
        for (NewDebtDetailDTO debt : table9Data) {
            BigDecimal newAmount = debt.getNewAmount();
            BigDecimal disposalAmount = debt.getDisposalAmount();

            if (disposalAmount.compareTo(newAmount) > 0) {
                // 处置金额超过新增金额，进行修正
                debt.setDisposalAmount(newAmount);
                debt.setExcessAmount(disposalAmount.subtract(newAmount));

                logger.warn("债权处置金额超出新增金额已修正: 债权人={}, 债务人={}, " +
                           "原处置金额={}, 修正后处置金额={}, 超出金额={}",
                           debt.getCreditor(), debt.getDebtor(),
                           disposalAmount, newAmount, debt.getExcessAmount());
            }
        }
    }
}
```

## 数据库查询优化

### 索引建议
```sql
-- 减值准备表索引
CREATE INDEX idx_impairment_year_month ON 减值准备表(年份, 月份);
CREATE INDEX idx_impairment_company ON 减值准备表(管理公司);

-- 处置表索引
CREATE INDEX idx_disposal_year_month ON 处置表(年份, 月份);
CREATE INDEX idx_disposal_company ON 处置表(管理公司);

-- 新增表索引
CREATE INDEX idx_new_debt_year_month ON 新增表(年份, 月份);
CREATE INDEX idx_new_debt_company ON 新增表(管理公司);
```

### 查询性能优化
1. **分页查询**: 大数据量时采用分页处理
2. **结果缓存**: 对相同参数的查询结果进行缓存
3. **连接池优化**: 调整数据库连接池参数
4. **SQL优化**: 使用EXPLAIN分析查询计划

## 异常处理策略

### 1. 数据异常处理
```java
public class DataValidationException extends RuntimeException {
    public DataValidationException(String message) {
        super(message);
    }
}

// 使用示例
if (stockDebtAmount.compareTo(BigDecimal.ZERO) < 0) {
    throw new DataValidationException("存量债权金额不能为负数");
}
```

### 2. Excel操作异常处理
```java
try {
    workbook.save(outputStream, SaveFormat.XLSX);
} catch (Exception e) {
    logger.error("Excel保存失败", e);
    throw new ExcelExportException("Excel文件生成失败: " + e.getMessage());
}
```

## 后续优化建议

1. **异步导出**: 大数据量时采用异步导出，提供进度反馈
2. **模板可配置**: 支持Excel模板的可视化配置
3. **数据缓存**: 对频繁查询的数据进行缓存优化
4. **监控告警**: 添加导出失败监控和告警机制

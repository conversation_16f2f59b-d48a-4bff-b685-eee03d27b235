# Git工作流命令速查卡 🚀

## 🎯 日常开发命令（最常用）

### 开始工作
```bash
同步代码          # 拉取最新代码
查看todo          # 查看待办任务
切换：功能名       # 创建/切换分支
```

### 开发中
```bash
查看改动          # 查看未提交的更改
保存进度          # 暂存当前工作
恢复进度          # 恢复暂存的工作
测试代码          # 运行快速测试
```

### 提交代码
```bash
提交：描述        # 智能提交（自动识别类型）
合并：develop     # 合并到开发分支
完成todo #001     # 标记任务完成
```

### 发布部署
```bash
部署测试          # 部署到测试环境
发布             # 一键部署生产（需确认）
回滚生产          # 紧急回滚
```

---

## 📋 按场景使用

### 场景1：实现新功能
```bash
1. 实现todo 权限管理     # 自动创建分支并开始
2. [开发代码...]
3. 测试代码             # 验证功能
4. 提交：实现权限管理    # 提交代码
5. 合并：develop        # 合并到开发分支
6. 完成todo #001        # 标记完成
```

### 场景2：紧急修复Bug
```bash
1. 快速修复：登录崩溃    # 创建hotfix分支
2. [修复代码...]
3. 测试代码             # 验证修复
4. 提交：修复登录崩溃    # 提交修复
5. 合并：main          # 合并到主分支
6. 发布                # 部署到生产
```

### 场景3：日常开发
```bash
1. 同步代码            # 获取最新
2. 切换：优化性能       # 创建feature分支
3. [开发代码...]
4. 提交：优化查询性能    # 提交更改
5. 合并：develop       # 合并到开发
```

---

## 🔍 命令详解

### 智能分支命令
| 输入 | 实际创建的分支 | 基于分支 |
|------|----------------|----------|
| `切换：登录优化` | feature/login-optimization | develop |
| `切换：修复崩溃` | hotfix/fix-crash | main |
| `快速修复：紧急问题` | hotfix/fix-urgent-issue | main |
| `新功能：用户管理` | feature/user-management | develop |

### 提交类型自动识别
| 描述包含 | 自动前缀 | 示例 |
|---------|---------|------|
| 新增、添加、实现 | feat: | feat: 添加用户登录功能 |
| 修复、修正、解决 | fix: | fix: 修复登录验证错误 |
| 优化、改进、重构 | refactor: | refactor: 优化数据库查询 |
| 文档、说明、注释 | docs: | docs: 更新API文档 |
| 测试、单测、test | test: | test: 添加单元测试 |

### TODO联动命令
| 命令 | 功能 | 自动执行 |
|------|------|----------|
| `实现todo #001` | 执行指定任务 | 读取需求→创建分支→显示方案 |
| `实现todo 权限` | 搜索并执行 | 模糊匹配→确认任务→开始执行 |
| `查看todo 紧急` | 筛选紧急任务 | 过滤高优先级→按重要性排序 |
| `完成todo #001` | 标记完成 | 更新状态→记录时间→可选合并 |

---

## ⚙️ 背后的自动化

### 提交时自动执行
1. ✅ 代码格式化（Prettier）
2. ✅ 语法检查（ESLint）
3. ✅ 提交信息规范化
4. ✅ 添加Co-author标识
5. ❌ 单元测试（默认关闭，可配置开启）

### 合并时自动执行
1. ✅ 保存未提交更改
2. ✅ 拉取最新代码
3. ✅ 执行合并操作
4. ✅ 删除已合并分支
5. ✅ 创建新feature分支（可用--no-branch禁用）

### 部署时自动执行
1. ✅ 运行完整测试
2. ✅ 构建项目
3. ✅ 创建版本标签
4. ✅ 备份服务器
5. ✅ 部署新版本
6. ✅ 健康检查

---

## 🛠️ 配置文件位置

- **命令配置**: `.claude/commands/git.md`
- **工作流配置**: `.claude/settings.json`
- **部署脚本**: `ci-cd/deploy/quick-deploy.sh`
- **分支脚本**: `scripts/auto-branch.sh`
- **任务管理**: `TODO.md`

---

## 💡 小技巧

1. **中文命令支持**：所有命令都支持中文描述，会自动转换
2. **智能识别**：根据描述自动判断操作类型
3. **安全保护**：所有破坏性操作都有备份
4. **离线工作**：大部分命令支持离线使用
5. **并行执行**：测试和构建会自动并行

---

*快速参考版本 - 打印后贴在显示器旁边 📌*
# FinancialSystem 项目目录结构说明

## 📋 概述

本文档详细说明了FinancialSystem项目的目录结构，帮助开发者快速理解项目组织方式。

**更新时间**: 2025-06-30  
**项目版本**: 1.0-SNAPSHOT  
**架构模式**: 多模块Maven项目 + Spring Boot微服务

## 🏗️ 根目录结构

```
FinancialSystem/
├── api-gateway/           # 🌐 API网关模块 (主应用入口)
├── services/              # 💼 业务服务模块组
├── shared/                # 🔧 共享组件模块组
├── integrations/          # 🔌 第三方集成模块组
├── FinancialSystem-web/   # 🎨 前端React应用
├── config/                # ⚙️ 配置文件目录
├── scripts/               # 📜 脚本文件目录
├── docs/                  # 📚 项目文档目录
├── sql/                   # 🗄️ 数据库脚本
├── var/                   # 📊 运行时数据目录
├── ci-cd/                 # 🚀 CI/CD配置
├── pom.xml               # 📦 Maven主配置文件
├── docker-compose.yml    # 🐳 Docker编排配置
└── README.md             # 📖 项目说明文档
```

## 🌐 API网关模块 (api-gateway/)

**作用**: 系统主入口，提供统一的API接口和认证授权

```
api-gateway/
├── src/main/java/
│   └── com/laoshu198838/
│       ├── FinancialSystemApplication.java  # 主启动类
│       ├── config/                          # 配置类
│       ├── controller/                      # REST控制器
│       ├── security/                        # 安全配置
│       └── filter/                          # 过滤器
├── src/main/resources/
│   ├── application.yml                      # 主配置文件
│   ├── application-dev.yml                  # 开发环境配置
│   └── application-prod.yml                 # 生产环境配置
└── pom.xml                                  # 模块Maven配置
```

**核心功能**:
- JWT认证和授权
- 统一异常处理
- 跨域配置
- API路由和转发
- 安全过滤器

## 💼 业务服务模块组 (services/)

**作用**: 包含所有业务逻辑模块，按业务领域划分

```
services/
├── debt-management/       # 债务管理服务
├── account-management/    # 账户管理服务
├── audit-management/      # 审计管理服务
├── report-management/     # 报表管理服务
└── pom.xml               # 服务组Maven配置
```

### 债务管理服务 (debt-management/)
```
debt-management/
├── src/main/java/com/laoshu198838/
│   ├── service/           # 业务服务层
│   ├── controller/        # REST控制器
│   └── dto/              # 数据传输对象
└── src/main/resources/
    └── application.yml    # 模块配置
```

### 其他服务模块结构类似

## 🔧 共享组件模块组 (shared/)

**作用**: 提供各模块共用的基础组件和工具类

```
shared/
├── common/               # 公共组件
├── data-access/          # 数据访问层
├── data-processing/      # 数据处理组件
└── pom.xml              # 共享组Maven配置
```

### 公共组件 (common/)
```
common/
├── src/main/java/com/laoshu198838/
│   ├── config/           # 通用配置类
│   ├── util/             # 工具类
│   ├── exception/        # 异常处理
│   ├── constant/         # 常量定义
│   └── dto/              # 通用DTO
└── src/main/resources/
```

### 数据访问层 (data-access/)
```
data-access/
├── src/main/java/com/laoshu198838/
│   ├── entity/           # JPA实体类
│   ├── repository/       # 数据仓库接口
│   └── config/           # 数据源配置
└── src/main/resources/
```

### 数据处理组件 (data-processing/)
```
data-processing/
├── src/main/java/com/laoshu198838/
│   ├── processor/        # 数据处理器
│   ├── converter/        # 数据转换器
│   ├── validator/        # 数据验证器
│   └── export/           # 数据导出功能
└── src/main/resources/
    └── templates/        # Excel模板文件
```

## 🔌 第三方集成模块组 (integrations/)

**作用**: 与外部系统的集成接口

```
integrations/
├── kingdee/              # 金蝶系统集成
├── treasury/             # 财政系统集成
└── pom.xml              # 集成组Maven配置
```

### 金蝶系统集成 (kingdee/)
```
kingdee/
├── src/main/java/com/laoshu198838/
│   ├── client/           # 金蝶API客户端
│   ├── dto/              # 金蝶数据传输对象
│   └── service/          # 金蝶集成服务
└── src/main/resources/
    └── kingdee-config.yml # 金蝶配置
```

### 财政系统集成 (treasury/)
```
treasury/
├── src/main/java/com/laoshu198838/
│   ├── client/           # 财政系统客户端
│   ├── dto/              # 财政数据传输对象
│   └── service/          # 财政集成服务
└── src/main/resources/
    └── treasury-config.yml # 财政系统配置
```

## 🎨 前端应用 (FinancialSystem-web/)

**作用**: React前端应用，提供用户界面

```
FinancialSystem-web/
├── public/               # 静态资源
│   ├── debt/            # 债务相关静态文件
│   └── template/        # 模板文件
├── src/
│   ├── components/      # React组件
│   ├── layouts/         # 页面布局
│   ├── utils/           # 工具函数
│   ├── config/          # 前端配置
│   ├── context/         # React Context
│   └── assets/          # 资源文件
├── package.json         # NPM配置
└── README.md           # 前端说明文档
```

## ⚙️ 配置目录 (config/)

**作用**: 存放各种配置文件

```
config/
├── docker/              # Docker相关配置
├── nginx/               # Nginx配置文件
├── systemd/             # 系统服务配置
└── ide-backup/          # IDE配置备份
```

## 📜 脚本目录 (scripts/)

**作用**: 存放各种自动化脚本

```
scripts/
├── build/               # 构建脚本
├── deploy/              # 部署脚本
├── database/            # 数据库脚本
│   └── migration/       # 数据库迁移脚本
├── maintenance/         # 维护脚本
├── ci-cd/               # CI/CD脚本
├── treasury/            # 财政系统相关脚本
├── ide/                 # IDE相关脚本
└── utils/               # 工具脚本
```

## 📚 文档目录 (docs/)

**作用**: 项目文档中心

```
docs/
├── guides/              # 项目指南
├── api/                 # API文档
├── business/            # 业务文档
├── development/         # 开发文档
├── deployment/          # 部署文档
├── operations/          # 运维文档
├── troubleshooting/     # 故障排除
├── planning/            # 项目规划
├── reports/             # 报告文档
├── archive/             # 归档文档
└── README.md           # 文档导航
```

## 🗄️ 数据库脚本 (sql/)

**作用**: 数据库相关SQL脚本

```
sql/
├── init/                # 初始化脚本
│   ├── schema.sql       # 数据库结构
│   └── data.sql         # 初始数据
└── migration/           # 数据库迁移脚本
```

## 📊 运行时数据 (var/)

**作用**: 存放运行时产生的数据

```
var/
├── log/                 # 日志文件
│   └── financial-system/ # 应用日志目录
└── backups/             # 备份文件
    ├── code/            # 代码备份
    ├── database/        # 数据库备份
    └── ide-config/      # IDE配置备份
```

## 🚀 CI/CD配置 (ci-cd/)

**作用**: 持续集成和部署配置

```
ci-cd/
├── deploy/              # 部署配置
├── backup/              # 备份配置
├── setup/               # 环境设置
├── systemd/             # 系统服务
├── webhook-server/      # Webhook服务器
└── image/               # 镜像相关
```

## 📦 Maven配置结构

### 主POM (pom.xml)
- 定义父项目配置
- 管理所有子模块
- 统一依赖版本管理
- 构建配置

### 模块依赖关系
```
api-gateway
├── depends on: services/*
├── depends on: shared/*
└── depends on: integrations/*

services/*
├── depends on: shared/common
├── depends on: shared/data-access
└── depends on: shared/data-processing

integrations/*
└── depends on: shared/common
```

## 🔍 关键文件说明

### 配置文件
- `application.yml` - Spring Boot主配置
- `docker-compose.yml` - Docker编排配置
- `pom.xml` - Maven项目配置

### 启动文件
- `FinancialSystemApplication.java` - Spring Boot启动类
- `package.json` - 前端项目配置

### 文档文件
- `README.md` - 项目主说明
- `docs/README.md` - 文档导航

## 📝 目录命名规范

1. **模块目录**: 使用小写字母和连字符 (kebab-case)
2. **Java包**: 使用小写字母和点分隔
3. **配置文件**: 使用小写字母和连字符
4. **文档文件**: 使用小写字母和连字符，中文文档可使用中文名

## 🎯 最佳实践

1. **模块独立**: 每个模块都有独立的pom.xml和配置
2. **依赖管理**: 通过父POM统一管理依赖版本
3. **配置分离**: 开发、测试、生产环境配置分离
4. **文档同步**: 代码变更时同步更新相关文档
5. **脚本自动化**: 使用脚本自动化常见操作

---

**维护者**: FinancialSystem开发团队  
**最后更新**: 2025-06-30  
**版本**: v1.0

# AI助手工作指南

本文档为AI助手（Augment Agent）提供项目相关的重要信息、用户偏好和工作规范，确保AI助手能够提供一致、高质量的技术支持。

## 📋 项目基本信息

### 项目概述
- **项目名称**: FinancialSystem（企业级财务管理系统）
- **主要功能**: 逾期债权管理、数据分析、报表生成、风险控制
- **架构模式**: 前后端分离、多模块Maven项目、微服务架构

### 技术栈
- **后端**: Java 21, Spring Boot 3.x, Spring Security 6.x (JWT), JPA, Hibernate
- **前端**: React 18, TypeScript, Ant Design
- **数据库**: MySQL 8.0, 多数据源配置
- **容器化**: Docker, Docker Compose
- **构建工具**: Maven, npm
- **CI/CD**: 自动化部署, Webhook服务

### 数据库配置
- **主数据库**: 逾期债权数据库 (中文名称)
- **用户**: root, 密码: Zlb&198838
- **端口**: 3306
- **第二数据库**: kingdee (金蝶ERP集成)
- **系统登录**: 用户名 laoshu198838, 密码 Zlb&198838

## 🏗️ 项目架构

### 模块结构
```
FinancialSystem/
├── api-gateway/              # Web服务层 (Spring Boot主应用)
├── FinancialSystem-web/      # 前端应用 (React)
├── services/                 # 业务模块层
│   ├── debt-management/      # 债权管理
│   ├── account-management/   # 账户管理
│   ├── audit-management/     # 审计管理
│   └── report-management/    # 报表管理
├── shared/                   # 共享模块
│   ├── common/              # 公共组件和工具
│   ├── data-access/         # 数据访问层
│   └── data-processing/     # 数据处理服务
├── integrations/            # 第三方集成
│   ├── kingdee/            # 金蝶ERP集成
│   └── treasury/           # 财务系统集成
└── business-modules/        # 业务模块配置
```

### 架构原则
- **实体共享**: 所有实体类放在common模块，供各模块共享
- **数据访问分离**: Repository类按数据库类型分文件夹组织
- **业务逻辑独立**: 各业务模块独立，通过API网关统一对外服务
- **多数据源支持**: 支持主业务库、用户库、金蝶库等多数据源

## 👤 用户偏好与工作规范

### 沟通偏好
- **语言**: 用户Zhou Libing偏好中文沟通，应使用中文回复
- **问题解决**: 用户偏好AI助手而非Cursor IDE来解决问题
- **文档要求**: 需要comprehensive的解决方案和详细文档

### 代码管理偏好
- **重构策略**: 偏好渐进式重构（方案A），避免激进变更
- **代码清理**: 删除注释/过时代码，不保留冗余代码
- **版本控制**: 重构操作需要版本控制支持，便于回滚
- **测试验证**: 每次重构后必须测试后端启动，确保服务正常

### 部署与运维偏好
- **服务器**: Linux服务器 admin@10.25.1.85 (密码: Wrkj2520.)
- **权限**: 偏好使用root登录避免权限问题
- **自动化**: 偏好自动化部署工作流，合并到main分支时触发
- **容器化**: 使用Docker部署，项目使用Java 21
- **分支命名**: 偏好基于日期的分支命名

### 数据处理偏好
- **数据一致性**: 高度重视数据一致性，优化前后必须验证查询结果完全一致
- **备份策略**: 不需要自动数据库备份功能
- **错误处理**: 遇到编译错误时，偏好删除未使用的方法调用而非实现缺失方法

## 🔧 技术决策记录

### 模块组织决策
- **服务位置**: NonLitigationUpdateService和DataConsistencyCheckService放在debt-management模块
- **工具类**: MapConvertUtil已存在，应定位并使用现有工具而非创建新的
- **用户服务**: CustomUserDetailService等账户相关服务放在account-management模块

### 数据库决策
- **用户系统**: User和Role实体位于主数据库（逾期债权数据库），不在独立数据库
- **默认数据库**: 默认使用逾期债权数据库，其他数据库需明确文档说明
- **导出功能**: 已在ExcelExportOverdueDebt.java实现，无需数据库级实现

### 架构演进记录
- **mysql_data模块**: 已重命名为data-processing模块
- **webservice模块**: 已被api-gateway替代
- **audit目录**: 根目录的audit目录被认为是不必要的，可删除

## 📝 工作流程规范

### 问题分析流程
1. **信息收集**: 使用codebase-retrieval工具了解相关代码
2. **方案制定**: 制定详细的低级别实施计划
3. **用户确认**: 向用户展示计划并获得确认
4. **分步实施**: 按计划逐步实施，每步验证结果
5. **测试验证**: 完成后进行全面测试

### 代码修改规范
1. **使用包管理器**: 依赖管理使用Maven/npm，不手动编辑配置文件
2. **保守修改**: 尊重现有代码库，进行保守的修改
3. **详细查询**: 修改前使用codebase-retrieval获取详细信息
4. **增量编辑**: 使用str-replace-editor进行增量编辑，不重写整个文件

### 文档维护规范
1. **同步更新**: 代码变更后及时更新相关文档
2. **分类管理**: 文档按功能分类存放在docs目录
3. **版本记录**: 重要变更记录在CHANGELOG.md
4. **归档管理**: 过时文档移至archive目录

### ⏰ 每日维护任务
**重要**: 每天下午4-5点执行以下维护任务：

#### 📊 项目结构信息更新
1. **检查项目结构变化**:
   ```bash
   find . -maxdepth 2 -type d | sort > current_structure.txt
   diff previous_structure.txt current_structure.txt
   ```

2. **更新相关文档**:
   - [项目当前状态总结](project-current-status.md)
   - [项目完整指南](COMPREHENSIVE_PROJECT_GUIDE.md)
   - [README.md](../../README.md)

3. **检查模块依赖关系**:
   - 验证pom.xml模块配置
   - 检查新增或删除的模块
   - 更新架构图和依赖关系图

4. **文档同步检查**:
   - 确保所有文档链接有效
   - 检查是否有新增需要归档的文档
   - 验证文档分类是否合理

#### 📝 维护记录
- **执行时间**: 每天下午4-5点
- **执行者**: AI Assistant (Augment Agent)
- **记录位置**: docs/guides/project-current-status.md
- **重要性**: 高优先级日常任务

## 🚨 注意事项

### 高风险操作
以下操作需要用户明确授权：
- 提交或推送代码
- 更改工单状态
- 合并分支
- 安装依赖
- 部署代码

### 数据安全
- 数据层优化需要详细记录所有查询函数
- 优化后每个查询必须测试验证
- 确保查询结果与优化前完全一致

### 系统稳定性
- 定时任务代码保持活跃状态，不注释掉
- 禁用定时任务会导致前端功能丢失
- 重构操作后必须验证系统启动正常

## 📚 参考资源

### 重要文档
- [项目完整指南](COMPREHENSIVE_PROJECT_GUIDE.md)
- [项目重要内容](项目重要内容.md)
- [项目知识记忆库](memories.md)
- [清洁项目结构指南](Clean_Project_Structure.md)

### 技术文档
- [数据库迁移记录](../operations/database-migration.md)
- [部署指南](../README.md)
- [故障排除指南](../troubleshooting/)

---

**更新日期**: 2025-06-23
**维护者**: AI Assistant (Augment Agent)
**版本**: v1.0

# 经营调度会看板导出API文档

## API概述

**基础路径**: `/api/export`  
**认证方式**: Bearer Token  
**权限要求**: ADMIN角色  

## 接口列表

### 1. 导出经营调度会看板

#### 接口信息
- **URL**: `/api/export/management-board`
- **方法**: POST
- **权限**: ADMIN
- **描述**: 根据年份和月份参数导出经营调度会看板Excel文件

#### 请求参数

| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| year | String | 是 | 年份 | "2025" |
| month | String | 是 | 月份 | "1" |

#### 请求示例

```bash
curl -X POST "http://localhost:8080/api/export/management-board" \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "year": "2025",
    "month": "1"
  }'
```

#### 响应格式

**成功响应 (200)**
```
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
Content-Disposition: attachment; filename="经营调度会看板_2025年01月.xlsx"
Content-Length: [文件大小]

[Excel文件二进制数据]
```

**权限不足 (403)**
```json
{
  "error": "Access Denied",
  "message": "仅管理员可执行此操作",
  "timestamp": "2025-01-XX 10:30:00"
}
```

**参数错误 (400)**
```json
{
  "error": "Bad Request",
  "message": "年份和月份参数不能为空",
  "timestamp": "2025-01-XX 10:30:00"
}
```

**服务器错误 (500)**
```json
{
  "error": "Internal Server Error",
  "message": "导出失败: Excel模板文件未找到",
  "timestamp": "2025-01-XX 10:30:00"
}
```

## 数据结构说明

### Excel输出结构

#### 汇总数据区域
| 位置 | 字段名称 | 数据来源 | 计算逻辑 |
|------|----------|----------|----------|
| B6 | 期初存量债权 | 减值准备表 | 2024年12月本月末债权余额汇总 |
| B9 | 本年累计新增 | 表9数据 | 每月新增金额累计到指定月份 |
| D6 | 存量债权本年累计处置 | 减值准备表 | 本月处置债权累计-新增债权处置 |
| C6 | 本月清收金额 | 计算得出 | 本月累计-上月累计 |

#### 处置方式统计区域
| 位置 | 字段名称 | 数据来源 | 计算逻辑 |
|------|----------|----------|----------|
| B14 | 现金处置 | 处置表 | 现金处置累计到指定月份 |
| B13 | 分期还款 | 处置表 | 分期还款累计到指定月份 |
| B17 | 资产抵债+其他 | 处置表 | 资产抵债+其他方式累计 |

#### 管理公司汇总区域
**存量债权汇总 (B22开始)**
| 列 | 字段名称 | 排序规则 |
|----|----------|----------|
| B | 管理公司 | 按期初存量债权降序 |
| C | 期初存量债权 | - |
| D | 存量债权累计处置 | - |

**新增债权汇总 (I22开始)**
| 列 | 字段名称 | 排序规则 |
|----|----------|----------|
| I | 管理公司 | 按新增债权金额降序 |
| J | 新增债权金额 | - |
| K | 累计回收金额 | - |

#### 明细数据区域
**存量债权处置明细 (N5开始)**
- 筛选条件: 前80%且处置金额>100万
- 排序: 按处置金额降序

**新增债权处置明细 (R5开始)**
- 筛选条件: 新增债权处置明细
- 排序: 按处置金额降序

**新增债权明细 (N15开始)**
- 筛选条件: 前80%且新增金额>100万
- 排序: 按新增金额降序

**新增债权余额明细 (R15开始)**
- 筛选条件: 新增债权余额数据
- 排序: 按余额降序

## 业务逻辑说明

### 处置金额拆分逻辑
```
if (处置金额 <= 当年新增金额) {
    新增债权处置 = 处置金额
    存量债权处置 = 0
} else {
    新增债权处置 = 当年新增金额
    存量债权处置 = 处置金额 - 当年新增金额
}
```

### 前80%筛选逻辑
1. 按金额降序排列
2. 取前80%数量的记录
3. 再筛选金额>100万的记录

### 表9处置金额修正
- 检查处置金额是否超过新增金额
- 如果超过，将处置金额限制为新增金额
- 超出部分记录到日志中

## 错误码说明

| 错误码 | 描述 | 解决方案 |
|--------|------|----------|
| 400 | 请求参数错误 | 检查年份和月份参数格式 |
| 401 | 未认证 | 提供有效的JWT Token |
| 403 | 权限不足 | 确保用户具有ADMIN角色 |
| 404 | 资源不存在 | 检查API路径是否正确 |
| 500 | 服务器内部错误 | 查看服务器日志，联系管理员 |

## 性能指标

| 指标 | 目标值 | 说明 |
|------|--------|------|
| 响应时间 | <30秒 | 1000条记录以内 |
| 内存使用 | <512MB | 单次导出操作 |
| 并发支持 | 5用户 | 同时导出 |
| 成功率 | >99% | 正常业务场景 |

## 使用示例

### JavaScript前端调用
```javascript
const exportManagementBoard = async (year, month) => {
  try {
    const response = await fetch('/api/export/management-board', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ year, month })
    });
    
    if (response.ok) {
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `经营调度会看板_${year}年${month.padStart(2, '0')}月.xlsx`;
      a.click();
    } else {
      const error = await response.json();
      console.error('导出失败:', error.message);
    }
  } catch (error) {
    console.error('请求失败:', error);
  }
};
```

### Java后端调用
```java
@Autowired
private ManagementBoardExportService exportService;

public ResponseEntity<byte[]> exportBoard(String year, String month) {
    try {
        return exportService.exportManagementBoard(year, month);
    } catch (Exception e) {
        logger.error("导出失败", e);
        return ResponseEntity.internalServerError().build();
    }
}
```

## 注意事项

1. **权限控制**: 必须具有ADMIN角色才能调用此接口
2. **参数验证**: 年份和月份参数必须为有效数值
3. **文件大小**: 导出文件大小取决于数据量，通常在1-10MB之间
4. **超时处理**: 大数据量导出可能需要较长时间，建议设置合适的超时时间
5. **并发限制**: 避免同一用户同时发起多个导出请求
6. **错误处理**: 导出失败时会返回详细的错误信息，便于问题排查

## 更新日志

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| 1.0.0 | 2025-01-XX | 初始版本，支持基础导出功能 |

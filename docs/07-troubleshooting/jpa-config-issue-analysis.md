# JPA 配置问题深入分析

## 问题描述
启动报错：`CompanyRepository` 无法创建，提示 `Company` 不是一个被管理的实体类型。

## 根本原因分析

### 1. 多数据源配置冲突
- `MultiDataSourceConfig` 和 `UserSystemDataSourceConfig` 都试图管理相同的 Repository
- 两个配置类都有 `@EnableJpaRepositories` 注解，导致扫描冲突

### 2. 实体扫描路径重复
- `Main.java` 中有 `@EntityScan` 和 `@EnableJpaRepositories` 注解
- `MultiDataSourceConfig` 中也有相同的配置
- 导致 Spring 容器中有多个相同的配置

### 3. 数据源路由问题
- `companies` 表在 `user_system` 数据库中
- 但系统错误地在 `逾期债权数据库` 中查找
- 说明 `CompanyRepository` 被错误的 EntityManagerFactory 管理

## 已执行的修复步骤

1. **移除 Main.java 中的重复注解**
   - 删除了 `@EntityScan`
   - 删除了 `@EnableJpaRepositories`

2. **修改 MultiDataSourceConfig**
   - 从 repository 扫描路径中移除 `com.laoshu198838.repository.user_system`
   - 从实体扫描路径中移除 `com.laoshu198838.entity.user_system`

3. **保留 UserSystemDataSourceConfig**
   - 独立管理 user_system 相关的实体和 repository

## 当前问题
`UserSystemDataSourceConfig` 虽然被创建，但其 `userSystemEntityManagerFactory` bean 没有被正确初始化。

## 可能的解决方案

### 方案一：检查 Bean 创建顺序（推荐）
确保 `UserSystemDataSourceConfig` 的 beans 在正确的时机被创建。

### 方案二：合并配置
将所有数据源配置合并到一个配置类中，避免配置冲突。

### 方案三：使用 @Primary 注解
明确指定主要的 EntityManagerFactory 和 TransactionManager。

## 建议的解决步骤

1. 检查 `userSystemEntityManagerFactory` 为什么没有被创建
2. 确保 `UserSystemDataSourceConfig` 中的所有 @Bean 方法都被正确执行
3. 检查是否有循环依赖或其他 Spring 容器初始化问题
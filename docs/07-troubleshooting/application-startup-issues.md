# 应用启动问题修复指南

## 🚨 当前问题描述

### 问题现象
应用启动时失败，出现Hibernate查询验证错误：
```
Caused by: org.hibernate.query.SemanticException: Could not resolve attribute 'currentSelfUseArea' of 'com.laoshu198838.entity.asset.AssetBasicInfo'
```

### 错误堆栈关键信息
```
Error interpreting query [SELECT a.managementCompany, SUM(a.totalArea), SUM(a.currentSelfUseArea), SUM(a.currentRentalArea) FROM AssetBasicInfo a WHERE a.status = :status GROUP BY a.managementCompany]
```

## 🔍 问题根因分析

### 1. 实体字段缺失
**问题**: `AssetBasicInfo` 实体类中缺少以下字段：
- `currentSelfUseArea` - 当前自用面积
- `currentRentalArea` - 当前出租面积
- `currentIdleArea` - 当前闲置面积

**影响**: Hibernate在启动时验证@Query注解中的HQL查询，发现字段不存在导致启动失败

### 2. 数据库表结构不匹配
**问题**: `asset_basic_info` 表中缺少对应的数据库字段

**影响**: 即使实体类添加了字段，如果数据库表结构不匹配，仍会导致运行时错误

### 3. Spring Data JPA查询验证
**问题**: Spring Boot在启动时会验证所有Repository中的@Query注解
**影响**: 任何一个查询验证失败都会导致整个应用启动失败

## 🛠️ 修复步骤

### 步骤1: 修复实体类字段

**文件**: `shared/common/src/main/java/com/laoshu198838/entity/asset/AssetBasicInfo.java`

**操作**: 在实体类中添加缺失字段
```java
@Column(name = "current_self_use_area", precision = 15, scale = 2)
private BigDecimal currentSelfUseArea;

@Column(name = "current_rental_area", precision = 15, scale = 2)
private BigDecimal currentRentalArea;

@Column(name = "current_idle_area", precision = 15, scale = 2)
private BigDecimal currentIdleArea;

// 添加对应的getter和setter方法（如果使用Lombok则自动生成）
```

### 步骤2: 执行数据库迁移

**脚本位置**: `services/data-maintenance/sql/add_asset_area_fields.sql`

**执行命令**:
```bash
# 方法1: 直接执行SQL文件
mysql -u root -p financial_system < services/data-maintenance/sql/add_asset_area_fields.sql

# 方法2: 手动执行SQL语句
mysql -u root -p
USE financial_system;
ALTER TABLE asset_basic_info 
ADD COLUMN current_self_use_area DECIMAL(15,2) DEFAULT 0.00 COMMENT '当前自用面积(平方米)',
ADD COLUMN current_rental_area DECIMAL(15,2) DEFAULT 0.00 COMMENT '当前出租面积(平方米)',
ADD COLUMN current_idle_area DECIMAL(15,2) DEFAULT 0.00 COMMENT '当前闲置面积(平方米)';

# 更新现有记录的面积分配（示例数据）
UPDATE asset_basic_info 
SET 
    current_self_use_area = total_area * 0.7,
    current_rental_area = total_area * 0.3,
    current_idle_area = 0
WHERE current_self_use_area IS NULL;
```

### 步骤3: 更新DTO类

**文件**: `shared/common/src/main/java/com/laoshu198838/dto/asset/AssetBasicInfoDTO.java`

**操作**: 取消注释相关字段
```java
// 将注释的字段恢复
private BigDecimal currentSelfUseArea; // 当前自用面积
private BigDecimal currentRentalArea; // 当前出租面积
private BigDecimal currentIdleArea; // 当前闲置面积
```

### 步骤4: 恢复Repository查询

**文件**: `shared/data-access/src/main/java/com/laoshu198838/repository/asset/AssetBasicInfoRepository.java`

**操作**: 取消注释被禁用的查询方法
```java
@Query("SELECT SUM(a.totalArea), " +
       "SUM(a.currentSelfUseArea), " +
       "SUM(a.currentRentalArea), " +
       "SUM(a.currentIdleArea) " +
       "FROM AssetBasicInfo a WHERE a.managementCompany = :managementCompany AND a.status = :status")
List<Object[]> getAreaDistributionByManagementCompany(@Param("managementCompany") String managementCompany,
                                                     @Param("status") AssetBasicInfo.AssetStatus status);

@Query("SELECT a.managementCompany, SUM(a.totalArea), " +
       "SUM(a.currentSelfUseArea), " +
       "SUM(a.currentRentalArea) " +
       "FROM AssetBasicInfo a WHERE a.status = :status " +
       "GROUP BY a.managementCompany")
List<Object[]> getCompanyActivationStatistics(@Param("status") AssetBasicInfo.AssetStatus status);
```

### 步骤5: 恢复Service层实现

**文件**: `services/asset-management/src/main/java/com/laoshu198838/service/AssetBasicInfoService.java`

**操作**: 恢复使用原始Repository和实际查询逻辑，替换模拟数据

### 步骤6: 清理临时文件

**操作**: 删除临时创建的文件
```bash
rm shared/data-access/src/main/java/com/laoshu198838/repository/asset/AssetBasicInfoRepositoryNew.java
```

## 🧪 验证步骤

### 1. 编译验证
```bash
cd /path/to/FinancialSystem
mvn clean compile -DskipTests
```

### 2. 启动验证
```bash
mvn spring-boot:run -pl api-gateway
```

### 3. 功能验证
- 访问 `http://localhost:8080/api/assets/test`
- 测试资产管理相关API
- 验证数据一致性检查功能

## 🔄 回滚方案

如果修复后仍有问题，可以按以下步骤回滚：

### 1. 回滚数据库更改
```sql
ALTER TABLE asset_basic_info 
DROP COLUMN current_self_use_area,
DROP COLUMN current_rental_area,
DROP COLUMN current_idle_area;
```

### 2. 回滚代码更改
- 重新注释实体类中的字段
- 重新注释Repository中的查询
- 恢复Service中的模拟数据

## 📋 检查清单

修复完成后，请确认以下项目：

- [ ] 实体类字段已添加
- [ ] 数据库表字段已添加
- [ ] DTO类字段已恢复
- [ ] Repository查询已恢复
- [ ] Service实现已恢复
- [ ] 应用可以正常启动
- [ ] API接口可以正常访问
- [ ] 前端页面可以正常显示数据

## 🚀 后续优化建议

### 1. 数据库迁移管理
建议使用Flyway或Liquibase进行数据库版本管理，避免手动执行SQL脚本

### 2. 实体字段验证
添加字段验证注解，确保数据完整性：
```java
@Column(name = "current_self_use_area", precision = 15, scale = 2)
@DecimalMin(value = "0.0", message = "自用面积不能为负数")
private BigDecimal currentSelfUseArea;
```

### 3. 单元测试
为Repository查询添加单元测试，确保查询逻辑正确

### 4. 集成测试
添加应用启动集成测试，及早发现类似问题

## 📞 支持信息

如果按照以上步骤仍无法解决问题，请检查：
1. MySQL服务是否正常运行
2. 数据库连接配置是否正确
3. 实体类包扫描路径是否正确
4. Spring Boot版本兼容性

**最后更新**: 2025-08-04
**状态**: 待验证修复

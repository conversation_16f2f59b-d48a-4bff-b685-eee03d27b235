# 🚀 财务管理系统 - Linux自动化部署指南

## 📋 部署概览

本项目已实现**完整的自动化部署方案**，支持Linux服务器直接部署（无Docker），包含一键部署、CI/CD流程和服务器管理工具。

## 🎯 快速开始

### 1️⃣ 一键部署（推荐）

```bash
# 在本地项目目录执行
./ci-cd/deploy/deploy-to-linux.sh

# 选择部署选项：
# 1) 完整部署（前端+后端）- 首次部署选择此项
# 2) 仅部署后端
# 3) 仅部署前端
# 4) 仅更新配置
```

### 2️⃣ 快速更新

```bash
# 用于日常代码更新
./ci-cd/deploy/quick-deploy.sh
```

## 📦 部署脚本说明

### **ci-cd/deploy/deploy-to-linux.sh** - 主部署脚本
- ✅ 自动构建前后端
- ✅ 上传文件到服务器
- ✅ 配置systemd服务
- ✅ 设置Nginx反向代理
- ✅ 健康检查

### **ci-cd/scripts/ci-cd-automation.sh** - CI/CD自动化
- ✅ Git钩子自动部署
- ✅ 自动化测试
- ✅ 版本回滚
- ✅ 部署监控
- ✅ GitHub Actions集成

### **ci-cd/deploy/quick-deploy.sh** - 快速部署
- ✅ 最小化构建时间
- ✅ 自动备份
- ✅ 失败自动回滚

### **scripts/deploy/server-management.sh** - 服务器管理
- ✅ 服务启停控制
- ✅ 日志查看
- ✅ 备份恢复
- ✅ 性能调优
- ✅ 安全加固

## 🔧 服务器配置

### 目录结构
```
服务器目录:
/opt/financial-system/          # 后端应用
├── financial-system.jar        # 主程序
├── application-prod.yml        # 生产配置
├── backup/                     # 备份目录
└── manage.sh                   # 管理脚本

/var/www/financial-system/      # 前端文件
├── index.html
├── static/
└── ...

/etc/nginx/conf.d/              # Nginx配置
└── financial-system.conf

/etc/systemd/system/            # 系统服务
└── financial-backend.service
```

### 服务状态
- **后端API**: http://**********:8080
- **前端页面**: http://**********
- **健康检查**: http://**********/health

## 🤖 自动化功能

### Git提交自动部署
```bash
# 设置Git钩子
./ci-cd/scripts/ci-cd-automation.sh
# 选择: 1 (设置Git钩子)

# 之后每次提交到main分支会自动部署
git add .
git commit -m "feat: 新功能"
# 自动触发部署！（无需push）
```

### 定时任务
```bash
# 在服务器上执行
/opt/financial-system/manage.sh cron

# 自动设置：
# - 每日2点自动备份
# - 每小时健康检查
# - 每周日清理日志
```

## 📊 监控和维护

### 实时监控
```bash
# 启动监控面板
./monitor.sh
```

### 查看服务状态
```bash
ssh root@********** "/opt/financial-system/manage.sh status"
```

### 查看日志
```bash
# 查看最新日志
ssh root@********** "journalctl -u financial-backend -n 50"

# 实时日志
ssh root@********** "journalctl -u financial-backend -f"
```

## 🔄 版本管理

### 部署新版本
```bash
# 方式1: 使用主脚本
./ci-cd/deploy/deploy-to-linux.sh
# 选择: 1 (完整部署)

# 方式2: 快速部署
./ci-cd/deploy/quick-deploy.sh

# 方式3: CI/CD菜单
./ci-cd/scripts/ci-cd-automation.sh
# 选择: 5 (完整CI/CD流程)
```

### 回滚版本
```bash
# 本地执行
./ci-cd/scripts/ci-cd-automation.sh
# 选择: 6 (回滚到上个版本)

# 或服务器上执行
ssh root@********** "/opt/financial-system/manage.sh restore"
```

## 🛡️ 安全措施

### 已实施的安全配置
- ✅ systemd服务权限限制
- ✅ Nginx安全头配置
- ✅ 防火墙规则设置
- ✅ 自动备份机制
- ✅ 失败自动回滚

### 安全加固（可选）
```bash
# 在服务器上执行
/opt/financial-system/manage.sh secure
```

## 📈 性能优化

### JVM调优
```yaml
# 已配置在systemd服务中
-Xms2g -Xmx4g          # 堆内存
-XX:+UseG1GC           # G1垃圾收集器
-XX:MaxGCPauseMillis=200  # 最大GC暂停
```

### Nginx优化
- Gzip压缩启用
- 静态资源缓存30天
- Keep-alive连接池

### 数据库优化
```bash
# 执行优化
ssh root@********** "/opt/financial-system/manage.sh tune"
```

## 📋 本地自动部署说明

### 部署方式
- ✅ **本地Git钩子自动部署** - 提交到main分支自动触发
- ✅ **手动部署脚本** - 按需执行
- ❌ ~~云端GitHub Actions~~ - 已移除

### 自动化特性
- 🔗 Git钩子检测main分支提交
- 🔨 自动构建前后端应用
- 📤 自动上传到Linux服务器
- 🔄 自动重启服务
- ✅ 自动健康检查

## 🚨 故障处理

### 常见问题

#### 1. 后端服务启动失败
```bash
# 检查日志
ssh root@********** "journalctl -u financial-backend -n 100"

# 检查端口占用
ssh root@********** "lsof -i :8080"

# 重启服务
ssh root@********** "systemctl restart financial-backend"
```

#### 2. 前端页面无法访问
```bash
# 检查Nginx
ssh root@********** "nginx -t"
ssh root@********** "systemctl restart nginx"

# 检查文件权限
ssh root@********** "ls -la /var/www/financial-system/"
```

#### 3. 数据库连接失败
```bash
# 检查MySQL服务
ssh root@********** "systemctl status mysqld"

# 测试连接
ssh root@********** "mysql -u root -p'Gj#23kD\$9mP@1xZ' -e 'show databases;'"
```

## 📝 日常运维流程

### 每日任务
1. **健康检查** - 自动执行
2. **查看监控** - `./monitor.sh`

### 每周任务
1. **检查备份** - `ssh root@********** "ls -la /opt/financial-system/backup/"`
2. **清理日志** - 自动执行

### 每月任务
1. **性能评估** - 查看监控数据
2. **安全审计** - 检查日志异常

## 🎯 最佳实践

### 部署前检查
- [ ] 代码已提交到Git
- [ ] 本地测试通过
- [ ] 数据库备份完成

### 部署后验证
- [ ] 服务状态正常
- [ ] API响应正常
- [ ] 前端页面可访问
- [ ] 核心功能测试

## 📞 支持信息

- **服务器IP**: **********
- **后端端口**: 8080
- **Web端口**: 80
- **系统**: AlmaLinux 9.6
- **Java**: OpenJDK 21
- **MySQL**: 8.0.41

## ✅ 当前部署状态

| 组件 | 状态 | 完成度 |
|-----|------|--------|
| 后端服务 | ✅ 运行中 | 100% |
| 数据库 | ✅ 正常 | 100% |
| 前端部署 | ⏳ 待部署 | 0% |
| Nginx配置 | ⏳ 待配置 | 0% |
| 自动化脚本 | ✅ 完成 | 100% |
| CI/CD | ✅ 完成 | 100% |

## 🎉 下一步操作

```bash
# 执行完整部署
./ci-cd/deploy/deploy-to-linux.sh
# 选择: 1 (完整部署)

# 部署成功后访问
http://**********
```

---

**提示**: 所有脚本都包含详细的日志输出和错误处理，遇到问题时请查看输出信息。
# 运维操作文档

本目录包含FinancialSystem项目的各种运维操作文档和指南。

## 📚 文档索引

### 数据库运维
- [MySQL双向复制原理与实现](./mysql-bidirectional-replication.md) - MySQL双向复制配置、原理、监控维护
- [数据库迁移指南](./database-migration.md) - 数据库迁移操作和注意事项

### 部署运维
- [部署成功报告](./deployment-success-report.md) - 生产环境部署记录
- [Linux兼容性修复](./linux-compatibility-fixes.md) - Linux环境问题修复记录

### 系统维护
- [项目结构清理报告](./项目结构清理报告.md) - 项目文件结构整理记录

## 🔧 常用运维命令

### 数据库相关
```bash
# 检查MySQL复制状态
mysql -u root -p -e "SHOW SLAVE STATUS\G"

# 重启MySQL复制
mysql -u root -p -e "STOP SLAVE; START SLAVE;"

# 检查数据库连接
mysql -u laoshu198838 -pZlb&198838 -e "SELECT 1;"
```

### 应用服务
```bash
# 检查后端服务状态
curl -s http://localhost:8080/actuator/health

# 重启应用服务
cd api-gateway && mvn spring-boot:run

# 检查前端服务
curl -s http://localhost:3000
```

### Docker服务
```bash
# 检查所有容器状态
docker ps -a

# 重启MySQL容器
docker restart financial-mysql

# 查看容器日志
docker logs financial-mysql
```

## 📋 运维检查清单

### 日常检查
- [ ] MySQL双向复制状态正常
- [ ] 应用服务健康检查通过
- [ ] 磁盘空间充足
- [ ] 日志文件大小合理

### 周期检查
- [ ] 数据库备份正常
- [ ] 系统性能指标正常
- [ ] 安全更新已应用
- [ ] 监控告警配置有效

## 🚨 应急处理

### 常见问题
1. **MySQL复制中断**: 参考 [MySQL双向复制文档](./mysql-bidirectional-replication.md#故障处理)
2. **应用服务无响应**: 检查日志并重启服务
3. **数据不同步**: 检查网络连接和复制状态

### 联系信息
- **系统管理员**: Zhou Libing
- **紧急联系**: 参考项目CLAUDE.md文件
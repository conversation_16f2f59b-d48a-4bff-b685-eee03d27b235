# 司库系统API接口文档

## 📋 概述

司库系统API提供与中信银行司库系统的接口对接功能，支持账户余额查询、交易记录查询等功能。

### 基础信息
- **Base URL**: `http://localhost:8080/api/treasury`
- **认证方式**: JWT Token (继承主系统认证)
- **数据格式**: JSON
- **字符编码**: UTF-8

## 🔐 认证

所有司库API接口都需要在请求头中包含有效的JWT Token：

```http
Authorization: Bearer <your-jwt-token>
```

## 📊 接口列表

### 1. 查询账户余额

查询指定账户或默认账户的余额信息。

**请求**
```http
GET /api/treasury/balance?accountNo=8110701012901269085
```

**参数**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| accountNo | String | 否 | 账户号码，不传则使用默认账户 |

**响应示例**
```json
{
  "success": true,
  "message": "余额查询成功",
  "data": {
    "success": true,
    "rawResponse": "<?xml version=\"1.0\" encoding=\"GBK\"?>...",
    "balance": "1000000.00",
    "accountNo": "8110701012901269085"
  },
  "timestamp": *************
}
```

### 2. 查询交易记录

查询指定时间范围内的交易记录。

**请求**
```http
GET /api/treasury/transactions?accountNo=8110701012901269085&startDate=********&endDate=********
```

**参数**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| accountNo | String | 否 | 账户号码，不传则使用默认账户 |
| startDate | String | 是 | 开始日期，格式：YYYYMMDD |
| endDate | String | 是 | 结束日期，格式：YYYYMMDD |

**响应示例**
```json
{
  "success": true,
  "message": "交易记录查询成功",
  "data": {
    "success": true,
    "rawResponse": "<?xml version=\"1.0\" encoding=\"GBK\"?>...",
    "transactions": "需要根据实际响应格式解析"
  },
  "timestamp": *************
}
```

### 3. 获取配置信息

获取司库系统的配置信息。

**请求**
```http
GET /api/treasury/config
```

**响应示例**
```json
{
  "success": true,
  "message": "配置信息获取成功",
  "data": {
    "clientCode": "SZWR003_ZL",
    "clientName": "深圳万润科技股份有限公司ERP",
    "defaultAccount": "8110701012901269085",
    "endpoint": "http://10.25.1.20:6767",
    "userName": "***********"
  },
  "timestamp": *************
}
```

### 4. 测试连接

测试与司库系统的连接状态。

**请求**
```http
GET /api/treasury/test
```

**响应示例**
```json
{
  "success": true,
  "message": "司库系统连接正常",
  "data": {
    "connectionStatus": "SUCCESS",
    "testResult": {
      "success": true,
      "rawResponse": "<?xml version=\"1.0\" encoding=\"GBK\"?>..."
    }
  },
  "timestamp": *************
}
```

### 5. 健康检查

检查司库模块的运行状态。

**请求**
```http
GET /api/treasury/health
```

**响应示例**
```json
{
  "success": true,
  "message": "司库模块运行正常",
  "data": {
    "status": "UP",
    "module": "treasury",
    "version": "1.0.0"
  },
  "timestamp": *************
}
```

## 🔧 配置说明

### 系统配置

司库系统的配置位于 `application.yml` 中：

```yaml
treasury:
  # 中信银行司库系统接口配置
  endpoint: http://10.25.1.20:6767
  username: ***********
  client-code: SZWR003_ZL
  client-name: 深圳万润科技股份有限公司ERP
  
  # 账户配置
  default-account: 8110701012901269085
  
  # 连接配置
  connection:
    timeout: 30000  # 连接超时时间(毫秒)
    read-timeout: 60000  # 读取超时时间(毫秒)
    retry-count: 3  # 重试次数
```

### 账户信息

系统配置了以下账户：

| 账户类型 | 账户号码 | 账户名称 | 开户网点 |
|----------|----------|----------|----------|
| 付款账号1 | 8110701012901269085 | 深圳万润科技股份有限公司ERP | 中信银行北京朝阳支行 |
| 付款账号2 | 8110701013101269086 | 深圳万润科技股份有限公司ERP | 中信银行北京朝阳支行 |
| 付款账号3 | 8110701013501269087 | 深圳万润科技股份有限公司ERP | 中信银行北京朝阳支行 |

## ❌ 错误处理

### 错误响应格式

```json
{
  "success": false,
  "message": "错误描述",
  "error": "ERROR_CODE",
  "timestamp": *************
}
```

### 常见错误码

| 错误码 | 说明 |
|--------|------|
| TREASURY_BALANCE_QUERY_ERROR | 余额查询失败 |
| TREASURY_TRANSACTION_QUERY_ERROR | 交易记录查询失败 |
| TREASURY_CONFIG_ERROR | 配置信息获取失败 |
| TREASURY_CONNECTION_ERROR | 司库系统连接失败 |

## 📝 使用示例

### JavaScript/前端调用示例

```javascript
// 查询账户余额
async function queryBalance(accountNo) {
  try {
    const response = await fetch('/api/treasury/balance' + 
      (accountNo ? `?accountNo=${accountNo}` : ''), {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      }
    });
    
    const result = await response.json();
    
    if (result.success) {
      console.log('余额查询成功:', result.data);
      return result.data;
    } else {
      console.error('余额查询失败:', result.message);
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('请求失败:', error);
    throw error;
  }
}

// 查询交易记录
async function queryTransactions(accountNo, startDate, endDate) {
  try {
    const params = new URLSearchParams({
      startDate,
      endDate
    });
    
    if (accountNo) {
      params.append('accountNo', accountNo);
    }
    
    const response = await fetch(`/api/treasury/transactions?${params}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      }
    });
    
    const result = await response.json();
    
    if (result.success) {
      console.log('交易记录查询成功:', result.data);
      return result.data;
    } else {
      console.error('交易记录查询失败:', result.message);
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('请求失败:', error);
    throw error;
  }
}
```

## 🔍 注意事项

1. **网络连接**: 确保服务器能够访问司库系统地址 `http://10.25.1.20:6767`
2. **字符编码**: 司库系统使用GBK编码，系统会自动处理编码转换
3. **超时设置**: 默认连接超时30秒，读取超时60秒
4. **错误重试**: 系统会自动重试失败的请求，最多重试3次
5. **缓存机制**: 余额信息缓存5分钟，交易记录缓存10分钟

---

**更新日期**: 2025-06-23  
**版本**: v1.0.0  
**维护者**: FinancialSystem开发团队

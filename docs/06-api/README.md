# API接口文档

本文档描述FinancialSystem项目的REST API接口规范和使用方法。

## 📋 API概览

### 基础信息
- **Base URL**: `http://localhost:8080/api`
- **认证方式**: JWT Token
- **数据格式**: JSON
- **字符编码**: UTF-8

### 模块接口
- **司库系统接口**: [Treasury API](treasury-api.md) - 银行司库系统对接
- **债权管理接口**: `/api/debt/*` - 债权数据管理
- **账户管理接口**: `/api/account/*` - 用户账户管理
- **报表管理接口**: `/api/report/*` - 报表生成和导出

### API测试文件
- **HTTP测试文件**: [tests/](tests/) - 包含各种API接口的HTTP测试文件
- **债权转换测试**: [tests/debt-conversion-test.http](tests/debt-conversion-test.http) - 债权转换功能的API测试

### 通用响应格式
```json
{
  "success": true,
  "message": "操作成功",
  "data": {},
  "timestamp": *************
}
```

### 错误响应格式
```json
{
  "success": false,
  "message": "错误描述",
  "error": "ERROR_CODE",
  "timestamp": *************
}
```

## 🔐 认证接口

### 用户登录
```http
POST /api/auth/login
Content-Type: application/json

{
  "username": "laoshu198838",
  "password": "Zlb&198838"
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzUxMiJ9...",
    "username": "laoshu198838",
    "roles": ["ROLE_ADMIN"],
    "company": "丈潮科技",
    "department": "资产负债部"
  }
}
```

### 用户注册
```http
POST /api/user-system/register
Content-Type: application/json
Authorization: Bearer <token>

{
  "username": "newuser",
  "password": "password123",
  "email": "<EMAIL>"
}
```

### 令牌验证
```http
GET /api/auth/validate
Authorization: Bearer <token>
```

## 💰 债权管理接口

### 获取债权统计
```http
GET /api/overdue-debt/statistics?year=2024&month=12
Authorization: Bearer <token>
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "totalAmount": 1000000.00,
    "newAmount": 200000.00,
    "disposedAmount": 150000.00,
    "endingBalance": 850000.00
  }
}
```

### 新增债权记录
```http
POST /api/overdue-debt/add
Content-Type: application/json
Authorization: Bearer <token>

{
  "creditor": "债权人名称",
  "debtor": "债务人名称",
  "amount": 100000.00,
  "period": "2024-12",
  "description": "债权描述"
}
```

### 查询债权列表
```http
GET /api/overdue-debt/list?page=0&size=20&creditor=债权人
Authorization: Bearer <token>
```

### 更新债权信息
```http
PUT /api/overdue-debt/{id}
Content-Type: application/json
Authorization: Bearer <token>

{
  "amount": 120000.00,
  "status": "已处置",
  "updateReason": "更新原因"
}
```

## ⚖️ 诉讼管理接口

### 获取诉讼案件
```http
GET /api/litigation/cases?year=2024&month=12
Authorization: Bearer <token>
```

### 新增诉讼案件
```http
POST /api/litigation/cases
Content-Type: application/json
Authorization: Bearer <token>

{
  "creditor": "债权人",
  "debtor": "债务人",
  "caseNumber": "案件编号",
  "amount": 500000.00,
  "status": "进行中"
}
```

## 📊 数据监控接口

### 数据一致性检查
```http
GET /api/datamonitor/check-add-table-consistency?year=2024&month=12
Authorization: Bearer <token>
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "isConsistent": true,
    "differences": [],
    "checkTime": "2024-12-23T10:30:00"
  }
}
```

### 获取监控报告
```http
GET /api/datamonitor/report?startDate=2024-01-01&endDate=2024-12-31
Authorization: Bearer <token>
```

## 📤 数据导出接口

### 导出Excel报表
```http
POST /api/excel/export
Content-Type: application/json
Authorization: Bearer <token>

{
  "type": "overdue-debt",
  "year": 2024,
  "month": 12,
  "format": "xlsx"
}
```

**响应**: 文件下载

### 获取导出历史
```http
GET /api/excel/export-history?page=0&size=10
Authorization: Bearer <token>
```

## 👤 用户管理接口

### 获取用户信息
```http
GET /api/user-system/profile
Authorization: Bearer <token>
```

### 更新用户信息
```http
PUT /api/user-system/profile
Content-Type: application/json
Authorization: Bearer <token>

{
  "email": "<EMAIL>",
  "phone": "13800138000",
  "department": "新部门"
}
```

### 获取用户列表 (管理员)
```http
GET /api/user-system/users?page=0&size=20
Authorization: Bearer <admin-token>
```

## 🏥 系统健康检查

### 应用健康状态
```http
GET /api/user-system/health
```

**响应示例**:
```json
{
  "status": "UP",
  "service": "user-system",
  "timestamp": *************
}
```

### 系统状态
```http
GET /api/user-system/status
Authorization: Bearer <token>
```

## 📝 请求/响应示例

### 通用请求头
```http
Content-Type: application/json
Authorization: Bearer eyJhbGciOiJIUzUxMiJ9...
Accept: application/json
```

### 分页参数
```http
GET /api/endpoint?page=0&size=20&sort=id,desc
```

### 日期参数格式
- **日期**: `YYYY-MM-DD` (如: 2024-12-23)
- **日期时间**: `YYYY-MM-DDTHH:mm:ss` (如: 2024-12-23T10:30:00)
- **年月**: `YYYY-MM` (如: 2024-12)

## ⚠️ 错误码说明

| 错误码 | HTTP状态 | 说明 |
|--------|----------|------|
| `AUTH_001` | 401 | 未授权访问 |
| `AUTH_002` | 403 | 权限不足 |
| `VALID_001` | 400 | 参数验证失败 |
| `DATA_001` | 404 | 数据不存在 |
| `SYS_001` | 500 | 系统内部错误 |

## 🔧 开发工具

### Postman集合
项目提供Postman集合文件，包含所有API接口的示例请求。

### API测试
```bash
# 健康检查
curl http://localhost:8080/api/user-system/health

# 登录测试
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"laoshu198838","password":"Zlb&198838"}'
```

### HTTP测试文件
项目在 `tests/` 目录下提供了HTTP测试文件，可以直接在IDE中运行：
- 使用IntelliJ IDEA或VS Code的REST Client插件
- 直接点击测试文件中的"Run"按钮执行测试
- 支持环境变量配置，便于不同环境测试

## 📚 相关文档

- [开发指南](../development/README.md)
- [故障排除](../troubleshooting/README.md)
- [项目完整指南](../guides/README.md)

---

**最后更新**: 2025-08-03
**维护者**: FinancialSystem开发团队
**API版本**: v1.0

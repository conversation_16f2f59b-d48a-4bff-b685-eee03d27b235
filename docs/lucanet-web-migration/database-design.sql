-- ===================================================================
-- LucaNet Web版本数据库设计
-- 基于现有FinancialSystem架构扩展
-- ===================================================================

-- 创建LucaNet专用数据库
CREATE DATABASE IF NOT EXISTS lucanet_web 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE lucanet_web;

-- ===================================================================
-- 1. 组织架构管理
-- ===================================================================

-- 组织架构表（支持多级合并结构）
CREATE TABLE organizations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(50) NOT NULL UNIQUE COMMENT '组织代码',
    name VARCHAR(255) NOT NULL COMMENT '组织名称',
    full_name VARCHAR(500) COMMENT '组织全称',
    parent_id BIGINT COMMENT '父级组织ID',
    org_type ENUM('GROUP', 'COMPANY', 'SUBSIDIARY', 'DEPARTMENT') NOT NULL COMMENT '组织类型',
    org_level INT NOT NULL DEFAULT 1 COMMENT '组织层级',
    org_path VARCHAR(1000) COMMENT '组织路径',
    legal_entity_type ENUM('DOMESTIC', 'FOREIGN', 'JOINT_VENTURE') COMMENT '法人实体类型',
    functional_currency VARCHAR(3) DEFAULT 'CNY' COMMENT '记账本位币',
    country_code VARCHAR(3) COMMENT '国家代码',
    status ENUM('ACTIVE', 'INACTIVE', 'DISSOLVED') DEFAULT 'ACTIVE',
    established_date DATE COMMENT '成立日期',
    consolidation_start_date DATE COMMENT '合并开始日期',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by BIGINT COMMENT '创建人',
    updated_by BIGINT COMMENT '更新人',
    
    INDEX idx_parent (parent_id),
    INDEX idx_type (org_type),
    INDEX idx_status (status),
    INDEX idx_level (org_level),
    INDEX idx_code (code),
    FOREIGN KEY (parent_id) REFERENCES organizations(id) ON DELETE RESTRICT
) COMMENT='组织架构表';

-- 合并规则表
CREATE TABLE consolidation_rules (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    parent_org_id BIGINT NOT NULL COMMENT '母公司ID',
    child_org_id BIGINT NOT NULL COMMENT '子公司ID',
    ownership_percentage DECIMAL(8,4) NOT NULL COMMENT '持股比例(%)',
    voting_percentage DECIMAL(8,4) COMMENT '表决权比例(%)',
    consolidation_method ENUM('FULL', 'PROPORTIONAL', 'EQUITY', 'COST') NOT NULL COMMENT '合并方法',
    control_type ENUM('DIRECT', 'INDIRECT', 'JOINT') NOT NULL COMMENT '控制类型',
    effective_date DATE NOT NULL COMMENT '生效日期',
    expiry_date DATE COMMENT '失效日期',
    acquisition_date DATE COMMENT '收购日期',
    acquisition_cost DECIMAL(18,2) COMMENT '收购成本',
    goodwill_amount DECIMAL(18,2) COMMENT '商誉金额',
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE',
    remarks TEXT COMMENT '备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_parent (parent_org_id),
    INDEX idx_child (child_org_id),
    INDEX idx_method (consolidation_method),
    INDEX idx_effective_date (effective_date),
    UNIQUE KEY uk_parent_child_date (parent_org_id, child_org_id, effective_date),
    FOREIGN KEY (parent_org_id) REFERENCES organizations(id),
    FOREIGN KEY (child_org_id) REFERENCES organizations(id)
) COMMENT='合并规则表';

-- ===================================================================
-- 2. 会计科目体系
-- ===================================================================

-- 会计科目表
CREATE TABLE chart_of_accounts (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    account_code VARCHAR(50) NOT NULL COMMENT '科目代码',
    account_name VARCHAR(255) NOT NULL COMMENT '科目名称',
    account_name_en VARCHAR(255) COMMENT '科目英文名称',
    parent_account_id BIGINT COMMENT '父级科目ID',
    account_type ENUM('ASSET', 'LIABILITY', 'EQUITY', 'REVENUE', 'EXPENSE') NOT NULL COMMENT '科目类型',
    account_category VARCHAR(50) COMMENT '科目分类',
    balance_direction ENUM('DEBIT', 'CREDIT') NOT NULL COMMENT '余额方向',
    account_level INT NOT NULL DEFAULT 1 COMMENT '科目层级',
    is_leaf BOOLEAN DEFAULT TRUE COMMENT '是否叶子节点',
    is_consolidation_account BOOLEAN DEFAULT FALSE COMMENT '是否合并科目',
    display_order INT DEFAULT 0 COMMENT '显示顺序',
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_code (account_code),
    INDEX idx_type (account_type),
    INDEX idx_parent (parent_account_id),
    INDEX idx_level (account_level),
    UNIQUE KEY uk_account_code (account_code),
    FOREIGN KEY (parent_account_id) REFERENCES chart_of_accounts(id)
) COMMENT='会计科目表';

-- ===================================================================
-- 3. 财务数据管理
-- ===================================================================

-- 会计期间表
CREATE TABLE accounting_periods (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    period_year INT NOT NULL COMMENT '会计年度',
    period_month INT NOT NULL COMMENT '会计期间',
    period_code VARCHAR(20) NOT NULL COMMENT '期间代码(YYYY-MM)',
    period_name VARCHAR(50) NOT NULL COMMENT '期间名称',
    start_date DATE NOT NULL COMMENT '期间开始日期',
    end_date DATE NOT NULL COMMENT '期间结束日期',
    status ENUM('OPEN', 'CLOSED', 'LOCKED') DEFAULT 'OPEN' COMMENT '期间状态',
    is_adjustment_period BOOLEAN DEFAULT FALSE COMMENT '是否调整期间',
    fiscal_year INT NOT NULL COMMENT '财政年度',
    quarter INT NOT NULL COMMENT '季度',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_period (period_year, period_month),
    INDEX idx_status (status),
    INDEX idx_fiscal_year (fiscal_year)
) COMMENT='会计期间表';

-- 财务数据主表
CREATE TABLE financial_data (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    organization_id BIGINT NOT NULL COMMENT '组织ID',
    account_id BIGINT NOT NULL COMMENT '科目ID',
    period_id BIGINT NOT NULL COMMENT '期间ID',
    scenario_code VARCHAR(20) DEFAULT 'ACTUAL' COMMENT '场景代码(ACTUAL/BUDGET/FORECAST)',
    version_code VARCHAR(20) DEFAULT 'V1' COMMENT '版本代码',
    local_amount DECIMAL(18,2) NOT NULL DEFAULT 0 COMMENT '本币金额',
    functional_amount DECIMAL(18,2) COMMENT '记账本位币金额',
    group_amount DECIMAL(18,2) COMMENT '集团币金额',
    local_currency VARCHAR(3) NOT NULL COMMENT '本币币种',
    functional_currency VARCHAR(3) COMMENT '记账本位币币种',
    group_currency VARCHAR(3) DEFAULT 'CNY' COMMENT '集团币币种',
    exchange_rate_functional DECIMAL(12,6) COMMENT '记账本位币汇率',
    exchange_rate_group DECIMAL(12,6) COMMENT '集团币汇率',
    data_source VARCHAR(50) COMMENT '数据来源',
    input_method ENUM('MANUAL', 'IMPORT', 'SYSTEM') DEFAULT 'MANUAL' COMMENT '录入方式',
    adjustment_type ENUM('NONE', 'CONSOLIDATION', 'ELIMINATION', 'RECLASSIFICATION') DEFAULT 'NONE' COMMENT '调整类型',
    is_eliminating_entry BOOLEAN DEFAULT FALSE COMMENT '是否抵消分录',
    elimination_group VARCHAR(50) COMMENT '抵消组',
    reference_number VARCHAR(100) COMMENT '参考编号',
    description TEXT COMMENT '描述',
    status ENUM('DRAFT', 'SUBMITTED', 'APPROVED', 'LOCKED') DEFAULT 'DRAFT' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by BIGINT COMMENT '创建人',
    updated_by BIGINT COMMENT '更新人',
    
    INDEX idx_org_period (organization_id, period_id),
    INDEX idx_account (account_id),
    INDEX idx_scenario (scenario_code),
    INDEX idx_status (status),
    INDEX idx_elimination (is_eliminating_entry, elimination_group),
    UNIQUE KEY uk_financial_data (organization_id, account_id, period_id, scenario_code, version_code),
    FOREIGN KEY (organization_id) REFERENCES organizations(id),
    FOREIGN KEY (account_id) REFERENCES chart_of_accounts(id),
    FOREIGN KEY (period_id) REFERENCES accounting_periods(id)
) COMMENT='财务数据表';

-- ===================================================================
-- 4. 合并处理
-- ===================================================================

-- 合并任务表
CREATE TABLE consolidation_tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    task_name VARCHAR(255) NOT NULL COMMENT '任务名称',
    consolidation_scope_id BIGINT NOT NULL COMMENT '合并范围ID',
    period_id BIGINT NOT NULL COMMENT '期间ID',
    scenario_code VARCHAR(20) DEFAULT 'ACTUAL' COMMENT '场景代码',
    version_code VARCHAR(20) DEFAULT 'V1' COMMENT '版本代码',
    task_type ENUM('FULL_CONSOLIDATION', 'FAST_CLOSE', 'ELIMINATION_ONLY') NOT NULL COMMENT '任务类型',
    status ENUM('PENDING', 'RUNNING', 'COMPLETED', 'FAILED', 'CANCELLED') DEFAULT 'PENDING' COMMENT '任务状态',
    start_time TIMESTAMP NULL COMMENT '开始时间',
    end_time TIMESTAMP NULL COMMENT '结束时间',
    error_message TEXT COMMENT '错误信息',
    progress_percentage INT DEFAULT 0 COMMENT '进度百分比',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by BIGINT COMMENT '创建人',
    
    INDEX idx_status (status),
    INDEX idx_period (period_id),
    INDEX idx_scope (consolidation_scope_id)
) COMMENT='合并任务表';

-- 抵消分录表
CREATE TABLE elimination_entries (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    consolidation_task_id BIGINT NOT NULL COMMENT '合并任务ID',
    elimination_type ENUM('INVESTMENT', 'INTERCOMPANY_REVENUE', 'INTERCOMPANY_EXPENSE', 'INTERCOMPANY_BALANCE', 'DIVIDEND') NOT NULL COMMENT '抵消类型',
    elimination_rule_id BIGINT COMMENT '抵消规则ID',
    dr_organization_id BIGINT COMMENT '借方组织',
    dr_account_id BIGINT NOT NULL COMMENT '借方科目',
    cr_organization_id BIGINT COMMENT '贷方组织',
    cr_account_id BIGINT NOT NULL COMMENT '贷方科目',
    amount DECIMAL(18,2) NOT NULL COMMENT '金额',
    currency VARCHAR(3) NOT NULL COMMENT '币种',
    description TEXT COMMENT '描述',
    auto_generated BOOLEAN DEFAULT TRUE COMMENT '是否自动生成',
    status ENUM('DRAFT', 'APPROVED', 'POSTED') DEFAULT 'DRAFT' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_task (consolidation_task_id),
    INDEX idx_type (elimination_type),
    FOREIGN KEY (consolidation_task_id) REFERENCES consolidation_tasks(id),
    FOREIGN KEY (dr_account_id) REFERENCES chart_of_accounts(id),
    FOREIGN KEY (cr_account_id) REFERENCES chart_of_accounts(id)
) COMMENT='抵消分录表';

-- ===================================================================
-- 5. 报表管理
-- ===================================================================

-- 报表模板表
CREATE TABLE report_templates (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    template_code VARCHAR(50) NOT NULL UNIQUE COMMENT '模板代码',
    template_name VARCHAR(255) NOT NULL COMMENT '模板名称',
    template_type ENUM('BALANCE_SHEET', 'INCOME_STATEMENT', 'CASH_FLOW', 'EQUITY_STATEMENT', 'CUSTOM') NOT NULL COMMENT '报表类型',
    report_category VARCHAR(50) COMMENT '报表分类',
    accounting_standard ENUM('GAAP', 'IFRS', 'PRC_GAAP', 'CUSTOM') DEFAULT 'PRC_GAAP' COMMENT '会计准则',
    template_structure JSON COMMENT '模板结构(JSON格式)',
    default_format ENUM('EXCEL', 'PDF', 'HTML', 'CSV') DEFAULT 'EXCEL' COMMENT '默认格式',
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by BIGINT COMMENT '创建人',
    
    INDEX idx_type (template_type),
    INDEX idx_category (report_category),
    INDEX idx_status (status)
) COMMENT='报表模板表';

-- 报表生成任务表
CREATE TABLE report_generation_tasks (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    task_name VARCHAR(255) NOT NULL COMMENT '任务名称',
    template_id BIGINT NOT NULL COMMENT '模板ID',
    organization_id BIGINT COMMENT '组织ID(null表示合并报表)',
    period_id BIGINT NOT NULL COMMENT '期间ID',
    scenario_code VARCHAR(20) DEFAULT 'ACTUAL' COMMENT '场景代码',
    output_format ENUM('EXCEL', 'PDF', 'HTML', 'CSV') NOT NULL COMMENT '输出格式',
    file_path VARCHAR(500) COMMENT '文件路径',
    file_name VARCHAR(255) COMMENT '文件名',
    status ENUM('PENDING', 'GENERATING', 'COMPLETED', 'FAILED') DEFAULT 'PENDING' COMMENT '状态',
    progress_percentage INT DEFAULT 0 COMMENT '进度百分比',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by BIGINT COMMENT '创建人',
    
    INDEX idx_status (status),
    INDEX idx_template (template_id),
    INDEX idx_period (period_id),
    FOREIGN KEY (template_id) REFERENCES report_templates(id),
    FOREIGN KEY (period_id) REFERENCES accounting_periods(id)
) COMMENT='报表生成任务表';

-- ===================================================================
-- 6. 工作流管理
-- ===================================================================

-- 工作流定义表
CREATE TABLE workflow_definitions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    workflow_code VARCHAR(50) NOT NULL UNIQUE COMMENT '工作流代码',
    workflow_name VARCHAR(255) NOT NULL COMMENT '工作流名称',
    workflow_type ENUM('CONSOLIDATION', 'REPORTING', 'APPROVAL', 'DATA_REVIEW') NOT NULL COMMENT '工作流类型',
    description TEXT COMMENT '描述',
    workflow_config JSON COMMENT '工作流配置',
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE',
    version VARCHAR(20) DEFAULT 'V1' COMMENT '版本',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_by BIGINT COMMENT '创建人',
    
    INDEX idx_type (workflow_type),
    INDEX idx_status (status)
) COMMENT='工作流定义表';

-- 工作流实例表
CREATE TABLE workflow_instances (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    workflow_definition_id BIGINT NOT NULL COMMENT '工作流定义ID',
    instance_name VARCHAR(255) NOT NULL COMMENT '实例名称',
    business_key VARCHAR(100) COMMENT '业务键',
    current_step VARCHAR(100) COMMENT '当前步骤',
    status ENUM('RUNNING', 'COMPLETED', 'SUSPENDED', 'TERMINATED') DEFAULT 'RUNNING' COMMENT '状态',
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
    end_time TIMESTAMP NULL COMMENT '结束时间',
    initiated_by BIGINT NOT NULL COMMENT '发起人',
    
    INDEX idx_workflow (workflow_definition_id),
    INDEX idx_status (status),
    INDEX idx_business_key (business_key),
    FOREIGN KEY (workflow_definition_id) REFERENCES workflow_definitions(id)
) COMMENT='工作流实例表';

-- ===================================================================
-- 7. 数据版本控制
-- ===================================================================

-- 数据版本表
CREATE TABLE data_versions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    version_code VARCHAR(50) NOT NULL COMMENT '版本代码',
    version_name VARCHAR(255) NOT NULL COMMENT '版本名称',
    period_id BIGINT NOT NULL COMMENT '期间ID',
    organization_id BIGINT COMMENT '组织ID',
    scenario_code VARCHAR(20) DEFAULT 'ACTUAL' COMMENT '场景代码',
    version_type ENUM('WORKING', 'BASELINE', 'APPROVED', 'ARCHIVED') DEFAULT 'WORKING' COMMENT '版本类型',
    parent_version_id BIGINT COMMENT '父版本ID',
    description TEXT COMMENT '描述',
    lock_status ENUM('UNLOCKED', 'LOCKED', 'READ_ONLY') DEFAULT 'UNLOCKED' COMMENT '锁定状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT COMMENT '创建人',
    
    INDEX idx_period (period_id),
    INDEX idx_org (organization_id),
    INDEX idx_type (version_type),
    INDEX idx_scenario (scenario_code),
    UNIQUE KEY uk_version_period_org (version_code, period_id, organization_id),
    FOREIGN KEY (period_id) REFERENCES accounting_periods(id),
    FOREIGN KEY (parent_version_id) REFERENCES data_versions(id)
) COMMENT='数据版本表';

-- ===================================================================
-- 8. 用户权限扩展（基于现有user_system数据库）
-- ===================================================================

-- 角色功能权限表（扩展现有权限系统）
CREATE TABLE lucanet_role_permissions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    role_id BIGINT NOT NULL COMMENT '角色ID（关联user_system.roles表）',
    permission_code VARCHAR(100) NOT NULL COMMENT '权限代码',
    permission_type ENUM('MENU', 'BUTTON', 'DATA', 'ORGANIZATION') NOT NULL COMMENT '权限类型',
    resource_id BIGINT COMMENT '资源ID（如组织ID）',
    permission_level ENUM('READ', 'WRITE', 'DELETE', 'APPROVE') NOT NULL COMMENT '权限级别',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_role (role_id),
    INDEX idx_permission (permission_code),
    INDEX idx_type (permission_type),
    UNIQUE KEY uk_role_permission_resource (role_id, permission_code, resource_id)
) COMMENT='LucaNet角色权限表';

-- ===================================================================
-- 9. 系统配置
-- ===================================================================

-- 系统参数表
CREATE TABLE system_parameters (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    param_category VARCHAR(50) NOT NULL COMMENT '参数分类',
    param_code VARCHAR(100) NOT NULL COMMENT '参数代码',
    param_name VARCHAR(255) NOT NULL COMMENT '参数名称',
    param_value TEXT COMMENT '参数值',
    param_type ENUM('STRING', 'NUMBER', 'BOOLEAN', 'JSON', 'DATE') DEFAULT 'STRING' COMMENT '参数类型',
    description TEXT COMMENT '描述',
    is_editable BOOLEAN DEFAULT TRUE COMMENT '是否可编辑',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_param (param_category, param_code),
    INDEX idx_category (param_category)
) COMMENT='系统参数表';

-- 汇率表
CREATE TABLE exchange_rates (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    from_currency VARCHAR(3) NOT NULL COMMENT '源币种',
    to_currency VARCHAR(3) NOT NULL COMMENT '目标币种',
    rate_date DATE NOT NULL COMMENT '汇率日期',
    rate_type ENUM('SPOT', 'AVERAGE', 'CLOSING', 'BUDGET') DEFAULT 'SPOT' COMMENT '汇率类型',
    exchange_rate DECIMAL(12,6) NOT NULL COMMENT '汇率',
    source VARCHAR(50) COMMENT '来源',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_rate (from_currency, to_currency, rate_date, rate_type),
    INDEX idx_date (rate_date),
    INDEX idx_currencies (from_currency, to_currency)
) COMMENT='汇率表';

-- ===================================================================
-- 初始化基础数据
-- ===================================================================

-- 插入默认会计期间
INSERT INTO accounting_periods (period_year, period_month, period_code, period_name, start_date, end_date, fiscal_year, quarter) VALUES
(2025, 1, '2025-01', '2025年1月', '2025-01-01', '2025-01-31', 2025, 1),
(2025, 2, '2025-02', '2025年2月', '2025-02-01', '2025-02-28', 2025, 1),
(2025, 3, '2025-03', '2025年3月', '2025-03-01', '2025-03-31', 2025, 1),
(2025, 4, '2025-04', '2025年4月', '2025-04-01', '2025-04-30', 2025, 2),
(2025, 5, '2025-05', '2025年5月', '2025-05-01', '2025-05-31', 2025, 2),
(2025, 6, '2025-06', '2025年6月', '2025-06-01', '2025-06-30', 2025, 2),
(2025, 7, '2025-07', '2025年7月', '2025-07-01', '2025-07-31', 2025, 3),
(2025, 8, '2025-08', '2025年8月', '2025-08-01', '2025-08-31', 2025, 3),
(2025, 9, '2025-09', '2025年9月', '2025-09-01', '2025-09-30', 2025, 3),
(2025, 10, '2025-10', '2025年10月', '2025-10-01', '2025-10-31', 2025, 4),
(2025, 11, '2025-11', '2025年11月', '2025-11-01', '2025-11-30', 2025, 4),
(2025, 12, '2025-12', '2025年12月', '2025-12-01', '2025-12-31', 2025, 4);

-- 插入基本会计科目
INSERT INTO chart_of_accounts (account_code, account_name, account_type, balance_direction, account_level) VALUES
-- 资产类
('1001', '库存现金', 'ASSET', 'DEBIT', 1),
('1002', '银行存款', 'ASSET', 'DEBIT', 1),
('1101', '应收账款', 'ASSET', 'DEBIT', 1),
('1201', '存货', 'ASSET', 'DEBIT', 1),
('1501', '固定资产', 'ASSET', 'DEBIT', 1),
('1701', '长期股权投资', 'ASSET', 'DEBIT', 1),
-- 负债类
('2001', '短期借款', 'LIABILITY', 'CREDIT', 1),
('2101', '应付账款', 'LIABILITY', 'CREDIT', 1),
('2501', '长期借款', 'LIABILITY', 'CREDIT', 1),
-- 所有者权益类
('3001', '实收资本', 'EQUITY', 'CREDIT', 1),
('3101', '资本公积', 'EQUITY', 'CREDIT', 1),
('3201', '盈余公积', 'EQUITY', 'CREDIT', 1),
('3301', '未分配利润', 'EQUITY', 'CREDIT', 1),
-- 收入类
('4001', '主营业务收入', 'REVENUE', 'CREDIT', 1),
('4101', '其他业务收入', 'REVENUE', 'CREDIT', 1),
-- 费用类
('5001', '主营业务成本', 'EXPENSE', 'DEBIT', 1),
('5101', '销售费用', 'EXPENSE', 'DEBIT', 1),
('5201', '管理费用', 'EXPENSE', 'DEBIT', 1),
('5301', '财务费用', 'EXPENSE', 'DEBIT', 1);

-- 插入系统参数
INSERT INTO system_parameters (param_category, param_code, param_name, param_value, param_type) VALUES
('SYSTEM', 'DEFAULT_CURRENCY', '默认币种', 'CNY', 'STRING'),
('SYSTEM', 'FISCAL_YEAR_START', '财年开始月份', '1', 'NUMBER'),
('CONSOLIDATION', 'AUTO_ELIMINATION', '自动抵消', 'true', 'BOOLEAN'),
('CONSOLIDATION', 'MINORITY_INTEREST_CALCULATION', '少数股东权益计算方法', 'PROPORTIONAL', 'STRING'),
('REPORTING', 'DEFAULT_REPORT_FORMAT', '默认报表格式', 'EXCEL', 'STRING'),
('REPORTING', 'MAX_EXPORT_ROWS', '最大导出行数', '100000', 'NUMBER');

-- 插入默认报表模板
INSERT INTO report_templates (template_code, template_name, template_type, accounting_standard) VALUES
('BS_STANDARD', '标准资产负债表', 'BALANCE_SHEET', 'PRC_GAAP'),
('IS_STANDARD', '标准利润表', 'INCOME_STATEMENT', 'PRC_GAAP'),
('CF_STANDARD', '标准现金流量表', 'CASH_FLOW', 'PRC_GAAP'),
('BS_CONSOLIDATED', '合并资产负债表', 'BALANCE_SHEET', 'PRC_GAAP'),
('IS_CONSOLIDATED', '合并利润表', 'INCOME_STATEMENT', 'PRC_GAAP');

-- ===================================================================
-- 性能优化索引
-- ===================================================================

-- 财务数据表的复合索引
CREATE INDEX idx_financial_data_reporting ON financial_data 
(organization_id, period_id, scenario_code, status);

CREATE INDEX idx_financial_data_consolidation ON financial_data 
(period_id, organization_id, account_id, scenario_code);

-- 组织架构的层级索引
CREATE INDEX idx_organizations_hierarchy ON organizations 
(parent_id, org_level, status);

-- 合并规则的时间索引
CREATE INDEX idx_consolidation_rules_period ON consolidation_rules 
(effective_date, expiry_date, status);

COMMIT;
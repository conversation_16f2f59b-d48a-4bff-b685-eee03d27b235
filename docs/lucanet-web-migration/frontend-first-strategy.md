# LucaNet Web版本：前端优先开发策略

## 🎯 策略重新设计：前端驱动开发

基于用户的正确建议，采用**前端优先+Mock数据**的开发策略，避免后端过度设计的风险。

## 🚀 Phase 1: 前端原型开发 (2-3周)

### 1.1 LucaNet界面分析和重现

**基于现有FinancialSystem-web架构**：
- ✅ React 18 + TypeScript + Material-UI v5.15.20
- ✅ 现有路由、状态管理、组件库
- ✅ 认证系统和主题配置

**核心页面开发优先级**：

#### 🏢 组织架构管理页面
```typescript
// src/layouts/lucanet/organization/index.tsx
interface OrganizationNode {
  id: string;
  code: string;
  name: string;
  type: 'GROUP' | 'COMPANY' | 'SUBSIDIARY';
  parent_id?: string;
  children?: OrganizationNode[];
  ownership_percentage?: number;
  consolidation_method?: 'FULL' | 'PROPORTIONAL' | 'EQUITY';
}

const OrganizationManagement = () => {
  // Mock数据 - 模拟华大智造的组织架构
  const mockOrganizations: OrganizationNode[] = [
    {
      id: '1',
      code: 'BGI_GROUP',
      name: '华大智造集团',
      type: 'GROUP',
      children: [
        {
          id: '2',
          code: 'BGI_MAIN',
          name: '深圳华大智造科技股份有限公司',
          type: 'COMPANY',
          parent_id: '1',
          ownership_percentage: 100,
          consolidation_method: 'FULL'
        },
        {
          id: '3',
          code: 'BGI_OVERSEAS',
          name: '华大智造(香港)有限公司',
          type: 'SUBSIDIARY',
          parent_id: '1',
          ownership_percentage: 75,
          consolidation_method: 'PROPORTIONAL'
        }
      ]
    }
  ];

  return (
    <DashboardPageLayout>
      <MDBox py={3}>
        <Grid container spacing={3}>
          {/* 组织架构树 */}
          <Grid item xs={12} md={6}>
            <Card>
              <MDBox p={3}>
                <MDTypography variant="h5">组织架构</MDTypography>
                <OrganizationTree data={mockOrganizations} />
              </MDBox>
            </Card>
          </Grid>
          
          {/* 合并规则配置 */}
          <Grid item xs={12} md={6}>
            <Card>
              <MDBox p={3}>
                <MDTypography variant="h5">合并规则</MDTypography>
                <ConsolidationRulesForm />
              </MDBox>
            </Card>
          </Grid>
        </Grid>
      </MDBox>
    </DashboardPageLayout>
  );
};
```

#### 📊 财务数据录入页面
```typescript
// src/layouts/lucanet/financial-data/index.tsx
interface FinancialDataEntry {
  organization_id: string;
  account_code: string;
  account_name: string;
  period: string;
  local_amount: number;
  functional_amount: number;
  local_currency: string;
  data_source: string;
}

const FinancialDataEntry = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('2025-06');
  const [selectedOrganization, setSelectedOrganization] = useState('');
  
  // Mock科目数据
  const mockAccounts = [
    { code: '1001', name: '库存现金', type: 'ASSET' },
    { code: '1002', name: '银行存款', type: 'ASSET' },
    { code: '1101', name: '应收账款', type: 'ASSET' },
    { code: '2001', name: '短期借款', type: 'LIABILITY' },
    { code: '3001', name: '实收资本', type: 'EQUITY' },
  ];

  return (
    <DashboardPageLayout>
      <MDBox py={3}>
        {/* 筛选条件 */}
        <Card sx={{ mb: 3 }}>
          <MDBox p={3}>
            <Grid container spacing={3}>
              <Grid item xs={12} md={3}>
                <FormSelect
                  label="会计期间"
                  value={selectedPeriod}
                  onChange={setSelectedPeriod}
                  options={[
                    { value: '2025-01', label: '2025年1月' },
                    { value: '2025-02', label: '2025年2月' },
                    { value: '2025-06', label: '2025年6月' },
                  ]}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <FormSelect
                  label="组织"
                  value={selectedOrganization}
                  onChange={setSelectedOrganization}
                  options={[
                    { value: 'BGI_MAIN', label: '华大智造主体' },
                    { value: 'BGI_OVERSEAS', label: '华大智造香港' },
                  ]}
                />
              </Grid>
            </Grid>
          </MDBox>
        </Card>

        {/* 数据录入表格 */}
        <Card>
          <MDBox p={3}>
            <FinancialDataGrid 
              accounts={mockAccounts}
              period={selectedPeriod}
              organization={selectedOrganization}
            />
          </MDBox>
        </Card>
      </MDBox>
    </DashboardPageLayout>
  );
};
```

#### 🔄 合并处理页面
```typescript
// src/layouts/lucanet/consolidation/index.tsx
interface ConsolidationTask {
  id: string;
  name: string;
  period: string;
  status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED';
  progress: number;
  start_time?: string;
  end_time?: string;
}

const ConsolidationProcessing = () => {
  const [tasks, setTasks] = useState<ConsolidationTask[]>([
    {
      id: '1',
      name: '2025年6月合并',
      period: '2025-06',
      status: 'COMPLETED',
      progress: 100,
      start_time: '2025-06-01 09:00:00',
      end_time: '2025-06-01 09:15:00'
    }
  ]);

  return (
    <DashboardPageLayout>
      <MDBox py={3}>
        <Grid container spacing={3}>
          {/* 合并任务列表 */}
          <Grid item xs={12} md={8}>
            <Card>
              <MDBox p={3}>
                <MDTypography variant="h5">合并任务</MDTypography>
                <ConsolidationTaskList tasks={tasks} />
              </MDBox>
            </Card>
          </Grid>
          
          {/* 合并配置 */}
          <Grid item xs={12} md={4}>
            <Card>
              <MDBox p={3}>
                <MDTypography variant="h5">新建合并任务</MDTypography>
                <ConsolidationTaskForm />
              </MDBox>
            </Card>
          </Grid>

          {/* 抵消分录预览 */}
          <Grid item xs={12}>
            <Card>
              <MDBox p={3}>
                <MDTypography variant="h5">抵消分录</MDTypography>
                <EliminationEntriesTable />
              </MDBox>
            </Card>
          </Grid>
        </Grid>
      </MDBox>
    </DashboardPageLayout>
  );
};
```

#### 📈 报表生成页面
```typescript
// src/layouts/lucanet/reports/index.tsx
const ReportGeneration = () => {
  return (
    <DashboardPageLayout>
      <MDBox py={3}>
        <Grid container spacing={3}>
          {/* 报表模板选择 */}
          <Grid item xs={12} md={4}>
            <Card>
              <MDBox p={3}>
                <MDTypography variant="h5">报表模板</MDTypography>
                <ReportTemplateSelector />
              </MDBox>
            </Card>
          </Grid>
          
          {/* 报表参数配置 */}
          <Grid item xs={12} md={8}>
            <Card>
              <MDBox p={3}>
                <MDTypography variant="h5">报表配置</MDTypography>
                <ReportParameterForm />
              </MDBox>
            </Card>
          </Grid>

          {/* 生成历史 */}
          <Grid item xs={12}>
            <Card>
              <MDBox p={3}>
                <MDTypography variant="h5">报表生成历史</MDTypography>
                <ReportHistoryTable />
              </MDBox>
            </Card>
          </Grid>
        </Grid>
      </MDBox>
    </DashboardPageLayout>
  );
};
```

### 1.2 核心组件开发

#### LucaNet专用组件库
```typescript
// src/components/lucanet/OrganizationTree.tsx
interface OrganizationTreeProps {
  data: OrganizationNode[];
  onSelect?: (node: OrganizationNode) => void;
  showConsolidationInfo?: boolean;
}

export const OrganizationTree: React.FC<OrganizationTreeProps> = ({ 
  data, 
  onSelect, 
  showConsolidationInfo = false 
}) => {
  const renderNode = (node: OrganizationNode, level = 0) => (
    <TreeItem
      key={node.id}
      nodeId={node.id}
      label={
        <Box sx={{ display: 'flex', alignItems: 'center', p: 0.5 }}>
          <Typography variant="body2" sx={{ fontWeight: 'inherit', flexGrow: 1 }}>
            {node.name} ({node.code})
          </Typography>
          {showConsolidationInfo && node.consolidation_method && (
            <Chip 
              label={node.consolidation_method} 
              size="small" 
              color="primary" 
            />
          )}
        </Box>
      }
      onClick={() => onSelect?.(node)}
    >
      {node.children?.map(child => renderNode(child, level + 1))}
    </TreeItem>
  );

  return (
    <TreeView
      defaultCollapseIcon={<ExpandMoreIcon />}
      defaultExpandIcon={<ChevronRightIcon />}
    >
      {data.map(node => renderNode(node))}
    </TreeView>
  );
};

// src/components/lucanet/FinancialDataGrid.tsx
export const FinancialDataGrid: React.FC<{
  accounts: Account[];
  period: string;
  organization: string;
}> = ({ accounts, period, organization }) => {
  const [data, setData] = useState([]);

  // Mock数据生成
  useEffect(() => {
    const mockData = accounts.map(account => ({
      id: `${organization}-${account.code}-${period}`,
      account_code: account.code,
      account_name: account.name,
      local_amount: Math.random() * 1000000,
      functional_amount: Math.random() * 1000000,
      local_currency: 'CNY',
      status: 'DRAFT'
    }));
    setData(mockData);
  }, [accounts, period, organization]);

  const columns = [
    { field: 'account_code', headerName: '科目代码', width: 120 },
    { field: 'account_name', headerName: '科目名称', width: 200 },
    { 
      field: 'local_amount', 
      headerName: '本币金额', 
      width: 150,
      type: 'number',
      editable: true
    },
    {
      field: 'functional_amount',
      headerName: '记账本位币金额',
      width: 150,
      type: 'number'
    },
    { field: 'local_currency', headerName: '币种', width: 100 },
    {
      field: 'status',
      headerName: '状态',
      width: 120,
      renderCell: (params) => (
        <Chip 
          label={params.value} 
          color={params.value === 'DRAFT' ? 'warning' : 'success'}
          size="small" 
        />
      )
    }
  ];

  return (
    <Box sx={{ height: 600, width: '100%' }}>
      <DataGrid
        rows={data}
        columns={columns}
        pageSize={20}
        checkboxSelection
        disableSelectionOnClick
        processRowUpdate={(newRow) => {
          // 本地更新，后续连接真实API
          setData(prev => prev.map(row => 
            row.id === newRow.id ? newRow : row
          ));
          return newRow;
        }}
      />
    </Box>
  );
};
```

### 1.3 Mock API服务

```typescript
// src/services/lucanet/mockApiService.ts
class LucaNetMockApiService {
  // 组织架构Mock API
  async getOrganizations(): Promise<OrganizationNode[]> {
    await this.delay(500); // 模拟网络延迟
    return mockOrganizationData;
  }

  async getFinancialData(filters: {
    organizationId: string;
    period: string;
    accountCodes?: string[];
  }): Promise<FinancialDataEntry[]> {
    await this.delay(800);
    return generateMockFinancialData(filters);
  }

  async performConsolidation(request: ConsolidationRequest): Promise<ConsolidationResult> {
    await this.delay(2000); // 模拟长时间计算
    return {
      taskId: generateId(),
      status: 'COMPLETED',
      consolidatedData: generateMockConsolidatedData(request),
      eliminationEntries: generateMockEliminationEntries(request)
    };
  }

  async generateReport(template: string, parameters: any): Promise<ReportResult> {
    await this.delay(3000);
    return {
      reportId: generateId(),
      fileName: `report_${template}_${Date.now()}.xlsx`,
      downloadUrl: `/mock/reports/report_${template}_${Date.now()}.xlsx`,
      status: 'COMPLETED'
    };
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

export const lucaNetMockApi = new LucaNetMockApiService();
```

## 🔄 Phase 2: API需求确认 (1周)

基于前端原型的使用，确认真实的API需求：

1. **数据结构验证** - 前端使用过程中发现的数据结构问题
2. **业务流程优化** - 实际操作流程的优化需求
3. **性能需求明确** - 真实数据量下的性能要求
4. **集成需求确认** - 与现有系统的集成点

## 🚀 Phase 3: 后端API实现 (3-4周)

基于前端验证的需求，实现精准的后端API：

1. **数据库优化设计** - 基于真实需求优化数据库结构
2. **API接口实现** - 按前端确认的契约实现API
3. **业务逻辑实现** - 合并算法、报表生成等核心逻辑
4. **性能优化** - 针对真实使用场景的性能优化

## ✅ 这种策略的优势

1. **降低风险** - 避免后端过度设计或设计不足
2. **快速验证** - 快速验证UI还原度和用户体验
3. **精确需求** - 基于真实使用确认API需求
4. **敏捷开发** - 支持快速迭代和需求调整
5. **成本控制** - 避免大量重构工作

这个策略更符合实际开发的最佳实践。我们应该先开发前端原型来验证需求，你觉得这个方向对吗？
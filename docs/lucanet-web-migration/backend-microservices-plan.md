# LucaNet Web版本后端微服务实现计划

## 🏗️ 微服务架构扩展

基于现有FinancialSystem架构，扩展LucaNet专用微服务：

### 现有服务复用
- ✅ **api-gateway** - 统一网关，已有JWT认证
- ✅ **user-system** - 用户管理，扩展权限控制
- ✅ **data-export** - Excel导出，可扩展报表功能

### 新增LucaNet专用服务

#### 1. **lucanet-organization-service** (组织管理服务)
**职责**：
- 组织架构管理（集团/公司/部门）
- 合并规则配置
- 组织权限控制

**技术实现**：
```java
@Entity
@Table(name = "organizations")
public class Organization {
    @Id @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(unique = true)
    private String code;
    
    private String name;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_id")
    private Organization parent;
    
    @OneToMany(mappedBy = "parent", cascade = CascadeType.ALL)
    private List<Organization> children;
    
    @Enumerated(EnumType.STRING)
    private OrganizationType orgType;
    
    private Integer orgLevel;
    private String functionalCurrency;
    // ... 其他字段
}

@RestController
@RequestMapping("/api/lucanet/organizations")
public class OrganizationController {
    
    @GetMapping("/tree")
    public ResponseEntity<List<OrganizationTreeDTO>> getOrganizationTree() {
        // 返回组织架构树
    }
    
    @PostMapping("/consolidation-rules")
    public ResponseEntity<ConsolidationRule> createConsolidationRule(
            @RequestBody ConsolidationRuleDTO ruleDTO) {
        // 创建合并规则
    }
}
```

#### 2. **lucanet-financial-data-service** (财务数据服务)
**职责**：
- 财务数据CRUD操作
- 批量数据导入/导出
- 数据验证和版本控制

**核心实现**：
```java
@Entity
@Table(name = "financial_data")
public class FinancialData {
    @Id @GeneratedValue
    private Long id;
    
    @ManyToOne
    @JoinColumn(name = "organization_id")
    private Organization organization;
    
    @ManyToOne
    @JoinColumn(name = "account_id")
    private ChartOfAccounts account;
    
    @ManyToOne
    @JoinColumn(name = "period_id")
    private AccountingPeriod period;
    
    @Column(precision = 18, scale = 2)
    private BigDecimal localAmount;
    
    @Column(precision = 18, scale = 2)
    private BigDecimal functionalAmount;
    
    @Column(precision = 18, scale = 2)
    private BigDecimal groupAmount;
    
    private String localCurrency;
    private String scenarioCode = "ACTUAL";
    private String versionCode = "V1";
    
    // 审计字段
    private LocalDateTime createdAt;
    private Long createdBy;
}

@Repository
public interface FinancialDataRepository extends JpaRepository<FinancialData, Long> {
    
    @Query("SELECT fd FROM FinancialData fd WHERE " +
           "fd.organization.id IN :orgIds AND " +
           "fd.period.id = :periodId AND " +
           "fd.scenarioCode = :scenario")
    List<FinancialData> findByOrganizationsAndPeriod(
        @Param("orgIds") List<Long> organizationIds,
        @Param("periodId") Long periodId,
        @Param("scenario") String scenarioCode);
    
    @Query("SELECT SUM(fd.groupAmount) FROM FinancialData fd WHERE " +
           "fd.account.accountCode LIKE :accountPattern AND " +
           "fd.period.periodYear = :year AND fd.period.periodMonth <= :month")
    BigDecimal calculateAccountBalance(
        @Param("accountPattern") String accountPattern,
        @Param("year") Integer year,
        @Param("month") Integer month);
}
```

#### 3. **lucanet-consolidation-service** (合并计算服务)
**职责**：
- 财务合并计算引擎
- 抵消分录生成
- 合并试算平衡

**算法实现**：
```java
@Service
@Transactional
public class ConsolidationService {
    
    public ConsolidationResult performConsolidation(ConsolidationRequest request) {
        // 1. 获取合并范围
        List<Organization> consolidationScope = getConsolidationScope(request.getParentOrgId());
        
        // 2. 收集财务数据
        List<FinancialData> rawData = collectFinancialData(consolidationScope, request.getPeriodId());
        
        // 3. 币种转换
        List<FinancialData> convertedData = performCurrencyConversion(rawData);
        
        // 4. 按合并方法处理
        Map<String, List<FinancialData>> groupedData = groupByConsolidationMethod(convertedData);
        
        // 5. 生成抵消分录
        List<EliminationEntry> eliminations = generateEliminationEntries(groupedData);
        
        // 6. 计算合并结果
        ConsolidationResult result = calculateConsolidatedResults(convertedData, eliminations);
        
        return result;
    }
    
    private List<EliminationEntry> generateEliminationEntries(Map<String, List<FinancialData>> groupedData) {
        List<EliminationEntry> eliminations = new ArrayList<>();
        
        // 投资抵消
        eliminations.addAll(generateInvestmentEliminations(groupedData));
        
        // 内部交易抵消
        eliminations.addAll(generateIntercompanyEliminations(groupedData));
        
        // 内部债权债务抵消
        eliminations.addAll(generateIntercompanyBalanceEliminations(groupedData));
        
        return eliminations;
    }
}
```

#### 4. **lucanet-report-service** (报表服务)
**职责**：
- 报表模板管理
- 报表生成引擎
- 多格式导出（Excel/PDF）

**实现框架**：
```java
@Service
public class ReportGenerationService {
    
    @Autowired
    private ExcelExportService excelExportService; // 复用现有服务
    
    public ReportGenerationTask generateReport(ReportRequest request) {
        ReportGenerationTask task = new ReportGenerationTask();
        task.setStatus(TaskStatus.PENDING);
        
        // 异步处理报表生成
        CompletableFuture.runAsync(() -> {
            try {
                task.setStatus(TaskStatus.GENERATING);
                
                // 1. 获取报表模板
                ReportTemplate template = getReportTemplate(request.getTemplateId());
                
                // 2. 查询数据
                List<FinancialData> data = queryReportData(request);
                
                // 3. 数据转换和计算
                ReportDataSet reportData = transformDataForReport(data, template);
                
                // 4. 生成报表文件
                String filePath = generateReportFile(reportData, template, request.getOutputFormat());
                
                task.setFilePath(filePath);
                task.setStatus(TaskStatus.COMPLETED);
                
            } catch (Exception e) {
                task.setStatus(TaskStatus.FAILED);
                task.setErrorMessage(e.getMessage());
                logger.error("报表生成失败", e);
            }
        });
        
        return taskRepository.save(task);
    }
}
```

#### 5. **lucanet-workflow-service** (工作流服务)
**职责**：
- 合并流程管理
- 审批工作流
- 任务分配和监控

**集成Camunda**：
```java
@Component
public class ConsolidationWorkflowService {
    
    @Autowired
    private RuntimeService runtimeService;
    
    public String startConsolidationProcess(ConsolidationWorkflowRequest request) {
        Map<String, Object> variables = new HashMap<>();
        variables.put("organizationId", request.getOrganizationId());
        variables.put("periodId", request.getPeriodId());
        variables.put("initiator", request.getInitiatorId());
        
        ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(
            "consolidation-process", 
            request.getBusinessKey(), 
            variables
        );
        
        return processInstance.getId();
    }
    
    @EventListener
    public void handleTaskComplete(TaskCompletedEvent event) {
        // 处理任务完成事件
        if ("data-validation".equals(event.getTaskDefinitionKey())) {
            // 数据验证完成，触发下一步
            triggerNextStep(event.getProcessInstanceId());
        }
    }
}
```

## 🔧 配置文件扩展

### application-lucanet.yml
```yaml
spring:
  # 新增LucaNet数据源
  datasource:
    lucanet:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ${DB_LUCANET_URL:************************************************************************************************************}
      username: ${DB_USERNAME:root}
      password: ${DB_PASSWORD:Zlb&198838}
      hikari:
        maximum-pool-size: 20
        minimum-idle: 5

  # Camunda工作流配置
  camunda:
    bpm:
      admin-user:
        id: admin
        password: admin
      filter:
        create: All tasks

# LucaNet专用配置
lucanet:
  consolidation:
    # 合并计算配置
    batch-size: 1000
    parallel-processing: true
    max-threads: 4
    
  reporting:
    # 报表配置
    template-path: /app/templates
    output-path: /app/reports
    max-file-size: 100MB
    
  currency:
    # 汇率配置
    default-currency: CNY
    rate-source: "PBOC"
    auto-update: true
```

### API路由配置
```yaml
# Gateway路由配置扩展
spring:
  cloud:
    gateway:
      routes:
        # LucaNet组织管理
        - id: lucanet-organization
          uri: http://lucanet-organization-service:8081
          predicates:
            - Path=/api/lucanet/organizations/**
          filters:
            - JwtAuthenticationFilter
            
        # LucaNet财务数据
        - id: lucanet-financial-data
          uri: http://lucanet-financial-data-service:8082
          predicates:
            - Path=/api/lucanet/financial-data/**
          filters:
            - JwtAuthenticationFilter
            
        # LucaNet合并服务
        - id: lucanet-consolidation
          uri: http://lucanet-consolidation-service:8083
          predicates:
            - Path=/api/lucanet/consolidation/**
          filters:
            - JwtAuthenticationFilter
```

## 📁 项目结构扩展

```
FinancialSystem/
├── api-gateway/                    # 现有网关服务
├── services/
│   ├── lucanet-organization/       # 新增：组织管理服务
│   │   ├── src/main/java/com/laoshu198838/lucanet/organization/
│   │   │   ├── entity/            # Organization, ConsolidationRule
│   │   │   ├── repository/        # 数据访问层
│   │   │   ├── service/           # 业务逻辑层
│   │   │   ├── controller/        # API控制器
│   │   │   └── dto/               # 数据传输对象
│   │   └── pom.xml
│   │
│   ├── lucanet-financial-data/     # 新增：财务数据服务
│   │   ├── src/main/java/com/laoshu198838/lucanet/financial/
│   │   │   ├── entity/            # FinancialData, ChartOfAccounts
│   │   │   ├── repository/
│   │   │   ├── service/
│   │   │   └── controller/
│   │   └── pom.xml
│   │
│   ├── lucanet-consolidation/      # 新增：合并计算服务
│   │   ├── src/main/java/com/laoshu198838/lucanet/consolidation/
│   │   │   ├── engine/            # 合并计算引擎
│   │   │   ├── elimination/       # 抵消处理
│   │   │   └── service/
│   │   └── pom.xml
│   │
│   └── lucanet-report/             # 新增：报表服务
│       ├── src/main/java/com/laoshu198838/lucanet/report/
│       │   ├── template/          # 报表模板
│       │   ├── generator/         # 报表生成器
│       │   └── service/
│       └── pom.xml
│
├── shared/
│   ├── lucanet-common/             # LucaNet通用组件
│   │   ├── src/main/java/com/laoshu198838/lucanet/common/
│   │   │   ├── enums/             # 枚举定义
│   │   │   ├── dto/               # 通用DTO
│   │   │   ├── exception/         # 异常定义
│   │   │   └── util/              # 工具类
│   │   └── pom.xml
│   │
│   └── lucanet-entity/             # LucaNet实体定义
│       ├── src/main/java/com/laoshu198838/lucanet/entity/
│       │   ├── Organization.java
│       │   ├── FinancialData.java
│       │   ├── ConsolidationRule.java
│       │   └── ReportTemplate.java
│       └── pom.xml
│
└── FinancialSystem-web/
    └── src/lucanet/                # 新增：LucaNet前端模块
        ├── components/             # LucaNet专用组件
        ├── pages/                  # LucaNet页面
        └── services/               # LucaNet API服务
```

## 🚀 实施优先级

### Phase 1: 核心服务搭建 (2-3周)
1. **lucanet-organization-service** - 组织架构管理
2. **lucanet-financial-data-service** - 基础数据管理
3. **数据库初始化** - 执行database-design.sql

### Phase 2: 业务逻辑实现 (3-4周)
1. **lucanet-consolidation-service** - 合并计算引擎
2. **数据导入/导出功能** - 扩展现有ExcelExportService
3. **基础API完善** - CRUD操作和数据验证

### Phase 3: 高级功能 (2-3周)
1. **lucanet-report-service** - 报表生成
2. **lucanet-workflow-service** - 工作流管理
3. **权限系统扩展** - 组织级权限控制

## 🔒 安全考虑

1. **数据隔离** - 组织级数据权限控制
2. **操作审计** - 完整的操作日志记录
3. **数据备份** - 关键数据自动备份
4. **加密传输** - 敏感数据加密处理

## 📊 性能优化

1. **数据库分片** - 按组织或时间分片
2. **缓存策略** - Redis缓存热点数据
3. **异步处理** - 大数据量合并异步执行
4. **连接池优化** - 数据库连接池配置

这个后端架构设计充分复用了现有FinancialSystem的基础设施，同时提供了LucaNet专用的业务功能。是否需要我继续实现具体的服务代码？
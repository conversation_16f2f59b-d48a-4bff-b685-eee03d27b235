# LucaNet Web版本技术架构设计

## 📄 文档信息

- **文档类型**: 技术架构设计
- **项目名称**: LucaNet Web版本转化
- **设计师**: Claude AI Assistant
- **创建日期**: 2025-08-18
- **版本**: v1.0

## 🏗️ 系统架构概览

### 整体架构设计

```mermaid
graph TB
    subgraph "前端层"
        WebApp[React Web应用]
        MobileApp[移动端应用]
    end
    
    subgraph "网关层"
        Gateway[Spring Cloud Gateway]
        LB[负载均衡器 Nginx]
    end
    
    subgraph "微服务层"
        AuthService[认证服务]
        DataService[数据管理服务]
        ConsolidationService[合并计算服务]
        ReportService[报表生成服务]
        AnalyticsService[分析服务]
        WorkflowService[工作流服务]
        IntegrationService[集成服务]
        NotificationService[通知服务]
    end
    
    subgraph "数据层"
        MySQL[(MySQL主库)]
        Redis[(Redis缓存)]
        ClickHouse[(ClickHouse分析库)]
        FileStorage[文件存储]
    end
    
    subgraph "基础设施层"
        Nacos[服务注册中心]
        Config[配置中心]
        Monitor[监控系统]
        Log[日志系统]
    end
    
    WebApp --> LB
    MobileApp --> LB
    LB --> Gateway
    Gateway --> AuthService
    Gateway --> DataService
    Gateway --> ConsolidationService
    Gateway --> ReportService
    Gateway --> AnalyticsService
    Gateway --> WorkflowService
    Gateway --> IntegrationService
    Gateway --> NotificationService
    
    AuthService --> MySQL
    DataService --> MySQL
    ConsolidationService --> MySQL
    ReportService --> MySQL
    AnalyticsService --> ClickHouse
    WorkflowService --> MySQL
    
    AuthService --> Redis
    DataService --> Redis
    ConsolidationService --> Redis
    ReportService --> FileStorage
    
    AuthService --> Nacos
    DataService --> Nacos
    ConsolidationService --> Nacos
    ReportService --> Nacos
    AnalyticsService --> Nacos
    WorkflowService --> Nacos
    IntegrationService --> Nacos
    NotificationService --> Nacos
```

### 核心设计原则

1. **微服务架构**: 服务自治，单一职责
2. **数据一致性**: 最终一致性，避免分布式事务
3. **高可用性**: 无单点故障，故障隔离
4. **可扩展性**: 水平扩展，弹性伸缩
5. **安全性**: 多层防护，零信任原则

## 🎯 微服务详细设计

### 1. 认证服务 (auth-service)

#### 服务职责
- 用户认证和授权
- JWT令牌管理
- 权限控制
- 用户会话管理

#### 技术栈
```yaml
Framework: Spring Boot 3.1.12
Security: Spring Security 6
Database: MySQL 8.0
Cache: Redis 7
Authentication: JWT + OAuth2
```

#### 核心接口设计
```java
@RestController
@RequestMapping("/api/auth")
public class AuthController {
    
    @PostMapping("/login")
    public ResponseEntity<AuthResponse> login(@RequestBody LoginRequest request) {
        // 用户认证逻辑
    }
    
    @PostMapping("/refresh")
    public ResponseEntity<AuthResponse> refreshToken(@RequestBody RefreshRequest request) {
        // 令牌刷新逻辑
    }
    
    @PostMapping("/logout")
    public ResponseEntity<Void> logout(@RequestHeader("Authorization") String token) {
        // 用户登出逻辑
    }
    
    @GetMapping("/userinfo")
    public ResponseEntity<UserInfo> getUserInfo(@RequestHeader("Authorization") String token) {
        // 获取用户信息
    }
}
```

#### 数据模型
```sql
-- 用户表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    full_name VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    last_login_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 角色表
CREATE TABLE roles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 权限表
CREATE TABLE permissions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    resource VARCHAR(100) NOT NULL,
    action VARCHAR(50) NOT NULL,
    description TEXT,
    UNIQUE KEY uk_resource_action (resource, action)
);

-- 用户角色关联表
CREATE TABLE user_roles (
    user_id BIGINT,
    role_id BIGINT,
    PRIMARY KEY (user_id, role_id),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (role_id) REFERENCES roles(id)
);

-- 角色权限关联表
CREATE TABLE role_permissions (
    role_id BIGINT,
    permission_id BIGINT,
    PRIMARY KEY (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES roles(id),
    FOREIGN KEY (permission_id) REFERENCES permissions(id)
);
```

### 2. 数据管理服务 (financial-data-service)

#### 服务职责
- 财务数据CRUD操作
- 数据验证和校验
- 批量数据导入
- 数据版本控制

#### 核心接口设计
```java
@RestController
@RequestMapping("/api/financial-data")
public class FinancialDataController {
    
    @GetMapping
    public ResponseEntity<Page<FinancialData>> getFinancialData(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String orgId,
            @RequestParam(required = false) Integer year,
            @RequestParam(required = false) Integer month) {
        // 分页查询财务数据
    }
    
    @PostMapping
    public ResponseEntity<FinancialData> createFinancialData(@RequestBody @Valid FinancialDataRequest request) {
        // 创建财务数据
    }
    
    @PutMapping("/{id}")
    public ResponseEntity<FinancialData> updateFinancialData(
            @PathVariable Long id, 
            @RequestBody @Valid FinancialDataRequest request) {
        // 更新财务数据
    }
    
    @PostMapping("/batch-import")
    public ResponseEntity<ImportResult> batchImport(@RequestParam("file") MultipartFile file) {
        // 批量导入财务数据
    }
    
    @GetMapping("/{id}/history")
    public ResponseEntity<List<FinancialDataHistory>> getDataHistory(@PathVariable Long id) {
        // 获取数据历史版本
    }
}
```

#### 数据模型
```sql
-- 财务数据主表
CREATE TABLE financial_data (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    organization_id BIGINT NOT NULL,
    account_code VARCHAR(50) NOT NULL,
    account_name VARCHAR(200),
    period_year INT NOT NULL,
    period_month INT NOT NULL,
    amount DECIMAL(18,2) NOT NULL,
    currency_code VARCHAR(3) DEFAULT 'CNY',
    exchange_rate DECIMAL(10,6) DEFAULT 1.000000,
    amount_base DECIMAL(18,2) NOT NULL, -- 本位币金额
    data_source VARCHAR(50),
    business_unit VARCHAR(100),
    cost_center VARCHAR(50),
    version_id BIGINT,
    created_by BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by BIGINT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_org_period (organization_id, period_year, period_month),
    INDEX idx_account (account_code),
    INDEX idx_version (version_id),
    FOREIGN KEY (organization_id) REFERENCES organizations(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id)
);

-- 财务数据历史表
CREATE TABLE financial_data_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    financial_data_id BIGINT NOT NULL,
    old_amount DECIMAL(18,2),
    new_amount DECIMAL(18,2),
    change_reason TEXT,
    changed_by BIGINT,
    changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_financial_data (financial_data_id),
    FOREIGN KEY (financial_data_id) REFERENCES financial_data(id),
    FOREIGN KEY (changed_by) REFERENCES users(id)
);

-- 数据版本表
CREATE TABLE data_versions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    organization_id BIGINT NOT NULL,
    period_year INT NOT NULL,
    period_month INT NOT NULL,
    version_name VARCHAR(100) NOT NULL,
    status ENUM('DRAFT', 'SUBMITTED', 'APPROVED', 'LOCKED') DEFAULT 'DRAFT',
    created_by BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    approved_by BIGINT,
    approved_at TIMESTAMP,
    INDEX idx_org_period (organization_id, period_year, period_month),
    FOREIGN KEY (organization_id) REFERENCES organizations(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (approved_by) REFERENCES users(id)
);
```

### 3. 合并计算服务 (consolidation-service)

#### 服务职责
- 财务合并计算
- 合并规则管理
- 抵消分录处理
- 币种转换计算

#### 核心接口设计
```java
@RestController
@RequestMapping("/api/consolidation")
public class ConsolidationController {
    
    @PostMapping("/calculate")
    public ResponseEntity<ConsolidationResult> calculate(@RequestBody ConsolidationRequest request) {
        // 执行合并计算
    }
    
    @GetMapping("/rules")
    public ResponseEntity<List<ConsolidationRule>> getConsolidationRules(
            @RequestParam Long parentOrgId,
            @RequestParam Integer year,
            @RequestParam Integer month) {
        // 获取合并规则
    }
    
    @PostMapping("/rules")
    public ResponseEntity<ConsolidationRule> createRule(@RequestBody @Valid ConsolidationRuleRequest request) {
        // 创建合并规则
    }
    
    @GetMapping("/eliminations")
    public ResponseEntity<List<EliminationEntry>> getEliminationEntries(
            @RequestParam Long parentOrgId,
            @RequestParam Integer year,
            @RequestParam Integer month) {
        // 获取抵消分录
    }
    
    @PostMapping("/currency-conversion")
    public ResponseEntity<CurrencyConversionResult> convertCurrency(@RequestBody CurrencyConversionRequest request) {
        // 货币转换
    }
}
```

#### 合并计算核心算法
```java
@Service
public class ConsolidationCalculationService {
    
    /**
     * 执行财务合并计算
     */
    public ConsolidationResult performConsolidation(ConsolidationRequest request) {
        // 1. 获取合并范围
        List<Organization> subsidiaries = getConsolidationScope(request.getParentOrgId());
        
        // 2. 获取合并规则
        List<ConsolidationRule> rules = getConsolidationRules(request.getParentOrgId(), 
                                                               request.getYear(), 
                                                               request.getMonth());
        
        // 3. 获取子公司财务数据
        Map<Long, List<FinancialData>> subsidiaryData = getSubsidiaryFinancialData(subsidiaries, 
                                                                                   request.getYear(), 
                                                                                   request.getMonth());
        
        // 4. 货币转换
        Map<Long, List<FinancialData>> convertedData = performCurrencyConversion(subsidiaryData, 
                                                                                 request.getBaseCurrency());
        
        // 5. 按合并方法计算
        List<ConsolidationEntry> consolidatedEntries = new ArrayList<>();
        for (ConsolidationRule rule : rules) {
            List<ConsolidationEntry> entries = calculateByMethod(rule, convertedData.get(rule.getChildOrgId()));
            consolidatedEntries.addAll(entries);
        }
        
        // 6. 生成抵消分录
        List<EliminationEntry> eliminations = generateEliminationEntries(consolidatedEntries);
        
        // 7. 计算合并结果
        List<FinancialData> finalResult = calculateFinalConsolidationResult(consolidatedEntries, eliminations);
        
        return ConsolidationResult.builder()
                .consolidatedData(finalResult)
                .eliminationEntries(eliminations)
                .calculationSummary(generateCalculationSummary(finalResult))
                .build();
    }
    
    /**
     * 根据合并方法计算
     */
    private List<ConsolidationEntry> calculateByMethod(ConsolidationRule rule, List<FinancialData> data) {
        switch (rule.getConsolidationMethod()) {
            case FULL:
                return calculateFullConsolidation(rule, data);
            case PROPORTIONAL:
                return calculateProportionalConsolidation(rule, data);
            case EQUITY:
                return calculateEquityMethod(rule, data);
            default:
                throw new IllegalArgumentException("Unsupported consolidation method: " + rule.getConsolidationMethod());
        }
    }
    
    /**
     * 全额合并法
     */
    private List<ConsolidationEntry> calculateFullConsolidation(ConsolidationRule rule, List<FinancialData> data) {
        return data.stream()
                .map(fd -> ConsolidationEntry.builder()
                        .accountCode(fd.getAccountCode())
                        .amount(fd.getAmount())
                        .consolidationMethod(ConsolidationMethod.FULL)
                        .ownershipPercentage(rule.getOwnershipPercentage())
                        .build())
                .collect(Collectors.toList());
    }
    
    /**
     * 比例合并法
     */
    private List<ConsolidationEntry> calculateProportionalConsolidation(ConsolidationRule rule, List<FinancialData> data) {
        BigDecimal percentage = rule.getOwnershipPercentage().divide(BigDecimal.valueOf(100));
        return data.stream()
                .map(fd -> ConsolidationEntry.builder()
                        .accountCode(fd.getAccountCode())
                        .amount(fd.getAmount().multiply(percentage))
                        .consolidationMethod(ConsolidationMethod.PROPORTIONAL)
                        .ownershipPercentage(rule.getOwnershipPercentage())
                        .build())
                .collect(Collectors.toList());
    }
    
    /**
     * 权益法
     */
    private List<ConsolidationEntry> calculateEquityMethod(ConsolidationRule rule, List<FinancialData> data) {
        // 权益法只确认投资收益，不合并资产负债
        BigDecimal netIncome = data.stream()
                .filter(fd -> isIncomeStatement(fd.getAccountCode()))
                .map(FinancialData::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        BigDecimal investmentIncome = netIncome.multiply(rule.getOwnershipPercentage().divide(BigDecimal.valueOf(100)));
        
        return Arrays.asList(
                ConsolidationEntry.builder()
                        .accountCode("2211") // 长期股权投资
                        .amount(investmentIncome)
                        .consolidationMethod(ConsolidationMethod.EQUITY)
                        .ownershipPercentage(rule.getOwnershipPercentage())
                        .build(),
                ConsolidationEntry.builder()
                        .accountCode("6111") // 投资收益
                        .amount(investmentIncome)
                        .consolidationMethod(ConsolidationMethod.EQUITY)
                        .ownershipPercentage(rule.getOwnershipPercentage())
                        .build()
        );
    }
}
```

### 4. 报表生成服务 (report-service)

#### 服务职责
- 报表模板管理
- 报表数据生成
- Excel/PDF导出
- 定时报表任务

#### 核心接口设计
```java
@RestController
@RequestMapping("/api/reports")
public class ReportController {
    
    @GetMapping("/templates")
    public ResponseEntity<List<ReportTemplate>> getReportTemplates() {
        // 获取报表模板列表
    }
    
    @PostMapping("/generate")
    public ResponseEntity<ReportResult> generateReport(@RequestBody ReportGenerationRequest request) {
        // 生成报表
    }
    
    @GetMapping("/{reportId}/download")
    public ResponseEntity<Resource> downloadReport(@PathVariable Long reportId, @RequestParam String format) {
        // 下载报表文件
    }
    
    @PostMapping("/schedule")
    public ResponseEntity<ScheduledReport> scheduleReport(@RequestBody ScheduleReportRequest request) {
        // 创建定时报表任务
    }
    
    @GetMapping("/history")
    public ResponseEntity<Page<ReportHistory>> getReportHistory(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(required = false) String templateId) {
        // 获取报表生成历史
    }
}
```

#### 报表生成核心逻辑
```java
@Service
public class ReportGenerationService {
    
    @Autowired
    private ReportTemplateService templateService;
    
    @Autowired
    private FinancialDataService financialDataService;
    
    @Autowired
    private ExcelReportGenerator excelGenerator;
    
    @Autowired
    private PdfReportGenerator pdfGenerator;
    
    /**
     * 生成报表
     */
    public ReportResult generateReport(ReportGenerationRequest request) {
        // 1. 获取报表模板
        ReportTemplate template = templateService.getTemplate(request.getTemplateId());
        
        // 2. 获取报表数据
        ReportData reportData = fetchReportData(template, request);
        
        // 3. 生成报表文件
        byte[] reportContent = generateReportContent(template, reportData, request.getFormat());
        
        // 4. 保存报表记录
        ReportHistory history = saveReportHistory(template, request, reportContent);
        
        return ReportResult.builder()
                .reportId(history.getId())
                .fileName(generateFileName(template, request))
                .fileSize(reportContent.length)
                .generateTime(LocalDateTime.now())
                .downloadUrl("/api/reports/" + history.getId() + "/download")
                .build();
    }
    
    /**
     * 获取报表数据
     */
    private ReportData fetchReportData(ReportTemplate template, ReportGenerationRequest request) {
        List<String> accountCodes = template.getAccountMappings().stream()
                .map(AccountMapping::getAccountCode)
                .collect(Collectors.toList());
        
        List<FinancialData> financialData = financialDataService.getFinancialData(
                request.getOrganizationId(),
                request.getYear(),
                request.getMonth(),
                accountCodes
        );
        
        return ReportData.builder()
                .template(template)
                .financialData(financialData)
                .parameters(request.getParameters())
                .generationTime(LocalDateTime.now())
                .build();
    }
    
    /**
     * 生成报表内容
     */
    private byte[] generateReportContent(ReportTemplate template, ReportData reportData, ReportFormat format) {
        switch (format) {
            case EXCEL:
                return excelGenerator.generate(template, reportData);
            case PDF:
                return pdfGenerator.generate(template, reportData);
            default:
                throw new IllegalArgumentException("Unsupported report format: " + format);
        }
    }
}

/**
 * Excel报表生成器
 */
@Component
public class ExcelReportGenerator {
    
    public byte[] generate(ReportTemplate template, ReportData reportData) {
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            XSSFSheet sheet = workbook.createSheet(template.getName());
            
            // 创建标题样式
            XSSFCellStyle titleStyle = createTitleStyle(workbook);
            XSSFCellStyle headerStyle = createHeaderStyle(workbook);
            XSSFCellStyle dataStyle = createDataStyle(workbook);
            
            int rowNum = 0;
            
            // 1. 生成报表标题
            rowNum = generateReportTitle(sheet, template, reportData, titleStyle, rowNum);
            
            // 2. 生成报表头部
            rowNum = generateReportHeaders(sheet, template, headerStyle, rowNum);
            
            // 3. 生成报表数据
            rowNum = generateReportData(sheet, template, reportData, dataStyle, rowNum);
            
            // 4. 设置列宽
            autoSizeColumns(sheet, template.getColumnCount());
            
            // 5. 生成图表（如果需要）
            generateCharts(workbook, sheet, template, reportData);
            
            // 转换为字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            return outputStream.toByteArray();
            
        } catch (IOException e) {
            throw new ReportGenerationException("Failed to generate Excel report", e);
        }
    }
    
    private int generateReportData(XSSFSheet sheet, ReportTemplate template, ReportData reportData, 
                                   XSSFCellStyle dataStyle, int startRow) {
        Map<String, FinancialData> dataMap = reportData.getFinancialData().stream()
                .collect(Collectors.toMap(
                        fd -> fd.getAccountCode() + "_" + fd.getOrganizationId(),
                        fd -> fd,
                        (existing, replacement) -> existing
                ));
        
        int rowNum = startRow;
        
        for (ReportRow templateRow : template.getRows()) {
            XSSFRow row = sheet.createRow(rowNum++);
            
            int colNum = 0;
            
            // 科目名称
            XSSFCell nameCell = row.createCell(colNum++);
            nameCell.setCellValue(templateRow.getAccountName());
            nameCell.setCellStyle(dataStyle);
            
            // 各列数据
            for (ReportColumn column : template.getColumns()) {
                XSSFCell dataCell = row.createCell(colNum++);
                
                if (column.getType() == ColumnType.ACCOUNT_AMOUNT) {
                    String key = templateRow.getAccountCode() + "_" + column.getOrganizationId();
                    FinancialData data = dataMap.get(key);
                    BigDecimal amount = data != null ? data.getAmount() : BigDecimal.ZERO;
                    dataCell.setCellValue(amount.doubleValue());
                } else if (column.getType() == ColumnType.CALCULATED) {
                    // 计算列逻辑
                    BigDecimal calculatedValue = calculateColumnValue(column, templateRow, dataMap);
                    dataCell.setCellValue(calculatedValue.doubleValue());
                }
                
                dataCell.setCellStyle(dataStyle);
            }
        }
        
        return rowNum;
    }
}
```

## 🗄️ 数据层设计

### 数据库架构

#### 分库分表策略
```yaml
# 数据库分片配置
databases:
  financial_data_db:
    sharding_strategy: "horizontal"
    sharding_key: "organization_id"
    partitions: 8
    tables:
      - financial_data
      - financial_data_history
      - consolidation_results
  
  master_data_db:
    sharding_strategy: "none"
    tables:
      - organizations
      - users
      - roles
      - report_templates
  
  analytics_db:
    type: "clickhouse"
    purpose: "OLAP queries and analytics"
    data_retention: "5 years"
```

#### 索引优化策略
```sql
-- 财务数据表索引设计
CREATE INDEX idx_financial_data_org_period ON financial_data(organization_id, period_year, period_month);
CREATE INDEX idx_financial_data_account ON financial_data(account_code);
CREATE INDEX idx_financial_data_composite ON financial_data(organization_id, account_code, period_year, period_month);
CREATE INDEX idx_financial_data_amount ON financial_data(amount) WHERE amount <> 0;

-- 组织架构表索引
CREATE INDEX idx_organizations_parent ON organizations(parent_id);
CREATE INDEX idx_organizations_type ON organizations(org_type);
CREATE INDEX idx_organizations_path ON organizations(org_path); -- 物化路径

-- 合并规则表索引
CREATE INDEX idx_consolidation_rules_parent ON consolidation_rules(parent_org_id, effective_date);
CREATE INDEX idx_consolidation_rules_child ON consolidation_rules(child_org_id, effective_date);
```

### 缓存策略

#### Redis缓存设计
```yaml
# Redis缓存配置
cache_strategies:
  user_session:
    key_pattern: "session:user:{user_id}"
    ttl: 3600 # 1小时
    type: "hash"
  
  organization_hierarchy:
    key_pattern: "org:hierarchy:{org_id}"
    ttl: 86400 # 24小时
    type: "string"
  
  consolidation_rules:
    key_pattern: "consolidation:rules:{parent_org_id}:{year}:{month}"
    ttl: 3600 # 1小时
    type: "list"
  
  report_templates:
    key_pattern: "report:template:{template_id}"
    ttl: 43200 # 12小时
    type: "hash"
  
  financial_data_summary:
    key_pattern: "financial:summary:{org_id}:{year}:{month}"
    ttl: 1800 # 30分钟
    type: "hash"
```

#### 缓存更新策略
```java
@Component
public class CacheManager {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    /**
     * 更新组织架构缓存
     */
    @CacheEvict(value = "organizationHierarchy", allEntries = true)
    public void evictOrganizationCache() {
        // 清除所有组织架构缓存
    }
    
    /**
     * 更新财务数据缓存
     */
    public void updateFinancialDataCache(Long orgId, Integer year, Integer month) {
        String key = String.format("financial:summary:%d:%d:%d", orgId, year, month);
        redisTemplate.delete(key);
        
        // 清除相关的合并计算缓存
        String consolidationKey = String.format("consolidation:rules:%d:%d:%d", orgId, year, month);
        redisTemplate.delete(consolidationKey);
    }
    
    /**
     * 预热缓存
     */
    @EventListener(ApplicationReadyEvent.class)
    public void warmUpCache() {
        // 预加载常用的组织架构数据
        organizationService.getAllOrganizations().forEach(org -> {
            String key = "org:hierarchy:" + org.getId();
            redisTemplate.opsForValue().set(key, org, Duration.ofHours(24));
        });
        
        // 预加载活跃的报表模板
        reportTemplateService.getActiveTemplates().forEach(template -> {
            String key = "report:template:" + template.getId();
            redisTemplate.opsForHash().putAll(key, convertToMap(template));
            redisTemplate.expire(key, Duration.ofHours(12));
        });
    }
}
```

## 🚀 部署架构

### Kubernetes部署配置

#### 服务部署清单
```yaml
# auth-service 部署配置
apiVersion: apps/v1
kind: Deployment
metadata:
  name: auth-service
  labels:
    app: auth-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: auth-service
  template:
    metadata:
      labels:
        app: auth-service
    spec:
      containers:
      - name: auth-service
        image: lucanet/auth-service:latest
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "production"
        - name: MYSQL_HOST
          valueFrom:
            secretKeyRef:
              name: mysql-secret
              key: host
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: redis-config
              key: host
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /actuator/health/readiness
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5

---
# Service配置
apiVersion: v1
kind: Service
metadata:
  name: auth-service
spec:
  selector:
    app: auth-service
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: ClusterIP
```

#### 网关配置
```yaml
# Spring Cloud Gateway配置
spring:
  cloud:
    gateway:
      routes:
        - id: auth-service
          uri: lb://auth-service
          predicates:
            - Path=/api/auth/**
          filters:
            - StripPrefix=2
            
        - id: financial-data-service
          uri: lb://financial-data-service
          predicates:
            - Path=/api/financial-data/**
          filters:
            - StripPrefix=2
            - name: AuthFilter
              
        - id: consolidation-service
          uri: lb://consolidation-service
          predicates:
            - Path=/api/consolidation/**
          filters:
            - StripPrefix=2
            - name: AuthFilter
            
        - id: report-service
          uri: lb://report-service
          predicates:
            - Path=/api/reports/**
          filters:
            - StripPrefix=2
            - name: AuthFilter
      
      globalcors:
        cors-configurations:
          '[/**]':
            allowedOrigins: 
              - "https://lucanet.company.com"
              - "https://lucanet-test.company.com"
            allowedMethods:
              - GET
              - POST
              - PUT
              - DELETE
              - OPTIONS
            allowedHeaders: "*"
            allowCredentials: true
```

### 监控和日志

#### Prometheus监控配置
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'lucanet-services'
    kubernetes_sd_configs:
      - role: pod
    relabel_configs:
      - source_labels: [__meta_kubernetes_pod_label_app]
        action: keep
        regex: (auth-service|financial-data-service|consolidation-service|report-service)
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
        action: keep
        regex: true
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
        action: replace
        target_label: __metrics_path__
        regex: (.+)

rule_files:
  - "/etc/prometheus/rules/*.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

#### Grafana仪表盘
```json
{
  "dashboard": {
    "title": "LucaNet Web Services",
    "panels": [
      {
        "title": "Service Health",
        "type": "stat",
        "targets": [
          {
            "expr": "up{job=\"lucanet-services\"}",
            "legendFormat": "{{instance}}"
          }
        ]
      },
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total{job=\"lucanet-services\"}[5m])",
            "legendFormat": "{{service}} - {{method}} {{status}}"
          }
        ]
      },
      {
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job=\"lucanet-services\"}[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      },
      {
        "title": "Database Connections",
        "type": "graph",
        "targets": [
          {
            "expr": "hikaricp_connections_active{job=\"lucanet-services\"}",
            "legendFormat": "{{service}} - Active"
          }
        ]
      }
    ]
  }
}
```

## 🔒 安全架构

### 安全设计原则
1. **深度防御**: 多层安全控制
2. **最小权限**: 最小必要权限原则
3. **零信任**: 不信任任何网络位置
4. **数据保护**: 端到端数据加密

### 安全控制矩阵
```yaml
security_controls:
  network_level:
    - firewall_rules
    - vpc_isolation
    - load_balancer_ssl_termination
    
  application_level:
    - jwt_authentication
    - role_based_access_control
    - api_rate_limiting
    - input_validation
    
  data_level:
    - database_encryption_at_rest
    - ssl_tls_in_transit
    - sensitive_data_masking
    - audit_logging
    
  infrastructure_level:
    - container_security_scanning
    - secrets_management
    - regular_security_updates
    - vulnerability_assessments
```

### JWT安全实现
```java
@Component
public class JwtTokenProvider {
    
    private static final String JWT_SECRET = "${jwt.secret}";
    private static final int JWT_EXPIRATION = 3600; // 1小时
    private static final int REFRESH_TOKEN_EXPIRATION = 86400; // 24小时
    
    @Value("${jwt.secret}")
    private String jwtSecret;
    
    public String generateToken(UserDetails userDetails) {
        Date expiryDate = new Date(System.currentTimeMillis() + JWT_EXPIRATION * 1000);
        
        return Jwts.builder()
                .setSubject(userDetails.getUsername())
                .setIssuedAt(new Date())
                .setExpiration(expiryDate)
                .claim("authorities", userDetails.getAuthorities().stream()
                        .map(GrantedAuthority::getAuthority)
                        .collect(Collectors.toList()))
                .signWith(SignatureAlgorithm.HS512, jwtSecret)
                .compact();
    }
    
    public String generateRefreshToken(String username) {
        Date expiryDate = new Date(System.currentTimeMillis() + REFRESH_TOKEN_EXPIRATION * 1000);
        
        return Jwts.builder()
                .setSubject(username)
                .setIssuedAt(new Date())
                .setExpiration(expiryDate)
                .claim("type", "refresh")
                .signWith(SignatureAlgorithm.HS512, jwtSecret)
                .compact();
    }
    
    public boolean validateToken(String token) {
        try {
            Jwts.parser().setSigningKey(jwtSecret).parseClaimsJws(token);
            return true;
        } catch (SignatureException ex) {
            logger.error("Invalid JWT signature");
        } catch (MalformedJwtException ex) {
            logger.error("Invalid JWT token");
        } catch (ExpiredJwtException ex) {
            logger.error("Expired JWT token");
        } catch (UnsupportedJwtException ex) {
            logger.error("Unsupported JWT token");
        } catch (IllegalArgumentException ex) {
            logger.error("JWT claims string is empty");
        }
        return false;
    }
}
```

## 📊 性能优化策略

### 数据库性能优化
```sql
-- 分区表设计
CREATE TABLE financial_data_2024 (
    LIKE financial_data INCLUDING ALL
) PARTITION BY RANGE (period_month);

CREATE TABLE financial_data_2024_q1 PARTITION OF financial_data_2024
    FOR VALUES FROM (1) TO (4);
    
CREATE TABLE financial_data_2024_q2 PARTITION OF financial_data_2024
    FOR VALUES FROM (4) TO (7);

-- 物化视图优化
CREATE MATERIALIZED VIEW mv_organization_summary AS
SELECT 
    o.id,
    o.name,
    o.org_type,
    COUNT(fd.id) as data_count,
    SUM(fd.amount) as total_amount,
    MAX(fd.updated_at) as last_updated
FROM organizations o
LEFT JOIN financial_data fd ON o.id = fd.organization_id
WHERE fd.period_year = EXTRACT(YEAR FROM CURRENT_DATE)
GROUP BY o.id, o.name, o.org_type;

-- 定期刷新物化视图
SELECT cron.schedule('refresh-org-summary', '0 1 * * *', 'REFRESH MATERIALIZED VIEW CONCURRENTLY mv_organization_summary;');
```

### 应用性能优化
```java
// 异步任务处理
@Service
public class AsyncReportService {
    
    @Async("reportTaskExecutor")
    @Retryable(value = {Exception.class}, maxAttempts = 3)
    public CompletableFuture<ReportResult> generateReportAsync(ReportGenerationRequest request) {
        try {
            ReportResult result = reportGenerationService.generateReport(request);
            
            // 发送完成通知
            notificationService.sendReportCompletionNotification(request.getUserId(), result);
            
            return CompletableFuture.completedFuture(result);
        } catch (Exception e) {
            logger.error("报表生成失败", e);
            throw e;
        }
    }
}

// 批量数据处理优化
@Service
public class BatchDataProcessor {
    
    private static final int BATCH_SIZE = 1000;
    
    @Transactional
    public void processBatchData(List<FinancialData> dataList) {
        // 分批处理，避免大事务
        for (int i = 0; i < dataList.size(); i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, dataList.size());
            List<FinancialData> batch = dataList.subList(i, endIndex);
            
            // 批量插入
            financialDataRepository.saveAll(batch);
            
            // 清理一级缓存
            entityManager.flush();
            entityManager.clear();
        }
    }
}
```

---

**文档状态**: 已完成  
**最后更新**: 2025-08-18  
**审核状态**: 待审核
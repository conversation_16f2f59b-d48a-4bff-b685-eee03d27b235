# 🔒 代码保护声明

## ⚠️ 严禁随意修改的关键代码区域

本文件记录项目中的关键业务逻辑代码，这些代码直接影响财务数据的准确性和系统的稳定性。

### 🚨 风险等级：🔴 极高风险

#### 1. Excel导出核心SQL逻辑
**文件位置**: `shared/data-processing/src/main/java/com/laoshu198838/export/ExcelExportOverdueDebt.java`

**保护区域**:
- **方法**: `buildQueryAndParams()` (第382行开始)
- **重点**: 表9-新增逾期债权明细表SQL (第708-792行)

**关键代码段**:
```java
// 表9的JOIN条件 - 第782-787行
LEFT JOIN disposal_data dd ON
    nd.债权人 = dd.债权人 AND
    nd.债务人 = dd.债务人 AND
    nd.是否涉诉 = dd.是否涉诉 AND
    nd.期间 = dd.期间  -- ⚠️ 此字段关联不可删除！

// 表9的WHERE条件 - 第789-790行  
WHERE
    nd.新增总额 > 0  -- ⚠️ 此条件已验证，不可随意修改！
```

**修改历史**:
- `dd210c0`: 基础版本
- `5af238c6`: 修复JOIN条件和WHERE逻辑，解决数据差异问题
- 当前版本: 已验证的稳定版本

**保护原因**:
- 直接影响逾期债权导出报表的数据准确性
- 经过多轮调试和数据验证
- 错误修改会导致财务数据差异

### 📋 修改审批流程

如需修改以上代码，必须遵循以下流程：

1. **📝 申请审批**
   - 详细说明修改原因
   - 提供预期效果分析
   - 获得业务负责人书面确认

2. **🧪 测试验证**
   - 创建代码备份快照
   - 在测试环境完整验证
   - 进行数据对比测试
   - 记录测试结果

3. **📊 数据验证**
   - 导出修改前后的报表数据
   - 逐行对比关键字段差异
   - 确认数据准确性

4. **✅ 部署确认**
   - 生产环境部署前再次确认
   - 实时监控数据准确性
   - 准备回滚方案

### 🚨 紧急情况处理

如发现代码被误修改导致数据异常：

1. **立即停止导出功能**
2. **恢复到最近的稳定版本**
3. **通知相关业务人员**
4. **进行数据影响评估**

### 📞 联系方式

- **业务负责人**: [待填写]
- **技术负责人**: [待填写]
- **紧急联系人**: [待填写]

---

⚠️ **重要提醒**: 本保护声明是项目的重要安全措施，任何人不得随意删除或修改此文件。

🔐 **最后更新**: 2025-08-18
🔐 **创建人**: Claude Code Assistant
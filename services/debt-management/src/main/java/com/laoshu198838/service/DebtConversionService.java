package com.laoshu198838.service;

import com.laoshu198838.dto.debt.DebtConversionRequestDTO;
import com.laoshu198838.dto.debt.DebtConversionResponseDTO;
import com.laoshu198838.dto.debt.DebtSearchResultDTO;
import com.laoshu198838.entity.overdue_debt.LitigationClaim;
import com.laoshu198838.entity.overdue_debt.NonLitigationClaim;
import com.laoshu198838.entity.overdue_debt.ImpairmentReserve;
import com.laoshu198838.entity.overdue_debt.LitigationClaim.LitigationCompositeKey;
import com.laoshu198838.entity.overdue_debt.NonLitigationClaim.NonLitigationCompositeKey;
import com.laoshu198838.entity.overdue_debt.ImpairmentReserve.ImpairmentReserveKey;
import com.laoshu198838.repository.overdue_debt.LitigationClaimRepository;
import com.laoshu198838.repository.overdue_debt.NonLitigationClaimRepository;
import com.laoshu198838.repository.overdue_debt.ImpairmentReserveRepository;
import com.laoshu198838.util.debt.FiveTableUpdateHelper;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Optional;

/**
 * 债权转换服务类
 * 实现诉讼与非诉讼债权相互转换的核心业务逻辑
 * 
 * <AUTHOR>
 */
@Service
public class DebtConversionService {
    
    private static final Logger logger = LoggerFactory.getLogger(DebtConversionService.class);
    
    @Autowired
    private LitigationClaimRepository litigationClaimRepository;
    
    @Autowired
    private NonLitigationClaimRepository nonLitigationClaimRepository;
    
    @Autowired
    private ImpairmentReserveRepository impairmentReserveRepository;
    
    /**
     * 搜索可转换的债权记录
     * 
     * @param creditor 债权人（可选）
     * @param debtor 债务人（可选）
     * @return 匹配的债权记录列表
     */
    public List<DebtSearchResultDTO> searchConvertibleDebts(String creditor, String debtor) {
        logger.info("开始搜索可转换债权记录，债权人：{}，债务人：{}", creditor, debtor);
        
        List<DebtSearchResultDTO> results = new ArrayList<>();
        
        try {
            // 搜索诉讼债权
            List<LitigationClaim> litigationClaims = findLitigationClaims(creditor, debtor);
            for (LitigationClaim claim : litigationClaims) {
                DebtSearchResultDTO dto = convertLitigationToSearchResult(claim);
                results.add(dto);
            }
            
            // 搜索非诉讼债权
            List<NonLitigationClaim> nonLitigationClaims = findNonLitigationClaims(creditor, debtor);
            for (NonLitigationClaim claim : nonLitigationClaims) {
                DebtSearchResultDTO dto = convertNonLitigationToSearchResult(claim);
                results.add(dto);
            }
            
            logger.info("搜索完成，找到 {} 条可转换记录", results.size());
            
        } catch (Exception e) {
            logger.error("搜索可转换债权记录时发生错误", e);
            throw new RuntimeException("搜索债权记录失败：" + e.getMessage(), e);
        }
        
        return results;
    }
    
    /**
     * 诉讼转非诉讼
     * 
     * @param request 转换请求数据
     * @return 转换结果
     */
    @Transactional
    public DebtConversionResponseDTO convertLitigationToNonLitigation(DebtConversionRequestDTO request) {
        logger.info("开始执行诉讼转非诉讼，债权人：{}，债务人：{}", request.getCreditor(), request.getDebtor());
        
        try {
            // 1. 查找诉讼表记录
            LitigationCompositeKey litigationKey = new LitigationCompositeKey();
            litigationKey.setCreditor(request.getCreditor());
            litigationKey.setDebtor(request.getDebtor());
            litigationKey.setPeriod(request.getPeriod());
            litigationKey.setYear(request.getYear());
            litigationKey.setMonth(request.getMonth());
            
            LitigationClaim litigationClaim = litigationClaimRepository.findById(litigationKey)
                .orElseThrow(() -> new RuntimeException("未找到对应的诉讼债权记录"));
            
            // 2. 保存原始数据用于转换（保存转换前的本月末债权余额）
            BigDecimal originalCurrentMonthBalance = litigationClaim.getCurrentMonthDebtBalance();
            
            // 3. 更新诉讼表（清零相关字段，记录互转减少金额）
            litigationClaim.setLitigationPrincipal(BigDecimal.ZERO);
            litigationClaim.setLitigationInterest(BigDecimal.ZERO);
            litigationClaim.setCurrentMonthDebtBalance(BigDecimal.ZERO);
            // 记录互转减少金额（等于转换前的本月末债权余额）
            litigationClaim.setTransferDecreaseAmount(originalCurrentMonthBalance != null ? originalCurrentMonthBalance : BigDecimal.ZERO);
            
            // 优化remark处理逻辑
            String conversionText = String.format("【转换】%d年%d月诉讼转非诉讼", 
                request.getConversionYear(), request.getConversionMonth());
            String userRemark = request.getRemark() != null ? request.getRemark().trim() : "";
            String originalRemark = litigationClaim.getRemark() != null ? litigationClaim.getRemark().trim() : "";
            
            String finalRemark;
            if (userRemark.isEmpty()) {
                // 用户没写内容，直接写转换说明
                finalRemark = conversionText;
            } else if (!userRemark.contains("转换")) {
                // 用户写了内容但没有转换表述，在最前面加上
                finalRemark = conversionText + "。" + userRemark;
            } else {
                // 已有转换表述，保持用户原内容
                finalRemark = userRemark;
            }
            
            // 如果原记录有备注，追加到后面
            if (!originalRemark.isEmpty()) {
                finalRemark = originalRemark + "; " + finalRemark;
            }
            
            litigationClaim.setRemark(finalRemark);
            
            litigationClaimRepository.save(litigationClaim);
            
            // 4. 创建非诉讼表记录
            createNonLitigationRecord(request, litigationClaim, originalCurrentMonthBalance);
            
            // 5. 更新减值准备表
            updateImpairmentReserveForConversion(request.getCreditor(), request.getDebtor(), 
                request.getConversionYear(), request.getConversionMonth(), request.getPeriod(), 
                "是", "否");
            
            // 6. 数据验证：检查转换后的数据一致性
            validateConversionResult(request, originalCurrentMonthBalance, "litigation_to_non_litigation");
            
            // 7. 更新后续月份数据（如果是历史转换）
            logger.info("🚀 准备调用后续月份更新方法...");
            try {
                updateSubsequentMonthsAfterConversion(request, "litigation_to_non_litigation");
                logger.info("✅ 后续月份更新方法调用完成");
            } catch (Exception e) {
                logger.error("❌ 后续月份更新方法调用失败", e);
                throw e; // 重新抛出异常
            }
            
            logger.info("诉讼转非诉讼执行成功");
            
            return DebtConversionResponseDTO.success("诉讼债权已成功转换为非诉讼债权", 
                "litigation_to_non_litigation", 1);
                
        } catch (Exception e) {
            logger.error("诉讼转非诉讼执行失败", e);
            throw new RuntimeException("诉讼转非诉讼失败：" + e.getMessage(), e);
        }
    }
    
    /**
     * 非诉讼转诉讼
     * 
     * @param request 转换请求数据
     * @return 转换结果
     */
    @Transactional
    public DebtConversionResponseDTO convertNonLitigationToLitigation(DebtConversionRequestDTO request) {
        logger.info("开始执行非诉讼转诉讼，债权人：{}，债务人：{}", request.getCreditor(), request.getDebtor());
        
        try {
            // 1. 查找非诉讼表记录
            NonLitigationCompositeKey nonLitigationKey = new NonLitigationCompositeKey();
            nonLitigationKey.setCreditor(request.getCreditor());
            nonLitigationKey.setDebtor(request.getDebtor());
            nonLitigationKey.setPeriod(request.getPeriod());
            nonLitigationKey.setYear(request.getYear());
            nonLitigationKey.setMonth(request.getMonth());
            
            NonLitigationClaim nonLitigationClaim = nonLitigationClaimRepository.findById(nonLitigationKey)
                .orElseThrow(() -> new RuntimeException("未找到对应的非诉讼债权记录"));
            
            // 2. 保存原始数据用于转换（使用上月末金额）
            BigDecimal lastMonthPrincipal = nonLitigationClaim.getLastMonthPrincipal();
            BigDecimal lastMonthInterest = nonLitigationClaim.getLastMonthInterest();
            BigDecimal lastMonthPenalty = nonLitigationClaim.getLastMonthPenalty();
            BigDecimal totalBalance = (lastMonthPrincipal != null ? lastMonthPrincipal : BigDecimal.ZERO)
                .add(lastMonthInterest != null ? lastMonthInterest : BigDecimal.ZERO)
                .add(lastMonthPenalty != null ? lastMonthPenalty : BigDecimal.ZERO);
            
            // 3. 更新非诉讼表（增加本月增减字段，清零本月末字段，记录互转减少金额）
            // 本月增减等于上月对应金额的负数
            nonLitigationClaim.setCurrentMonthPrincipalIncreaseDecrease(
                lastMonthPrincipal != null ? lastMonthPrincipal.negate() : BigDecimal.ZERO);
            nonLitigationClaim.setCurrentMonthInterestIncreaseDecrease(
                lastMonthInterest != null ? lastMonthInterest.negate() : BigDecimal.ZERO);
            nonLitigationClaim.setCurrentMonthPenaltyIncreaseDecrease(
                lastMonthPenalty != null ? lastMonthPenalty.negate() : BigDecimal.ZERO);
            // 清零本月末字段
            nonLitigationClaim.setCurrentMonthPrincipal(BigDecimal.ZERO);
            nonLitigationClaim.setCurrentMonthInterest(BigDecimal.ZERO);
            nonLitigationClaim.setCurrentMonthPenalty(BigDecimal.ZERO);
            // 记录互转减少金额
            nonLitigationClaim.setTransferDecreaseAmount(totalBalance);
            
            // 优化remark处理逻辑
            String conversionText = String.format("【转换】%d年%d月非诉讼转诉讼", 
                request.getConversionYear(), request.getConversionMonth());
            String userRemark = request.getRemark() != null ? request.getRemark().trim() : "";
            String originalRemark = nonLitigationClaim.getRemark() != null ? nonLitigationClaim.getRemark().trim() : "";
            
            String finalRemark;
            if (userRemark.isEmpty()) {
                // 用户没写内容，直接写转换说明
                finalRemark = conversionText;
            } else if (!userRemark.contains("转换")) {
                // 用户写了内容但没有转换表述，在最前面加上
                finalRemark = conversionText + "。" + userRemark;
            } else {
                // 已有转换表述，保持用户原内容
                finalRemark = userRemark;
            }
            
            // 如果原记录有备注，追加到后面
            if (!originalRemark.isEmpty()) {
                finalRemark = originalRemark + "; " + finalRemark;
            }
            
            nonLitigationClaim.setRemark(finalRemark);
            
            nonLitigationClaimRepository.save(nonLitigationClaim);
            
            // 4. 创建诉讼表记录
            createLitigationRecord(request, nonLitigationClaim, lastMonthPrincipal, lastMonthInterest, lastMonthPenalty, totalBalance);
            
            // 5. 更新减值准备表
            updateImpairmentReserveForConversion(request.getCreditor(), request.getDebtor(), 
                request.getConversionYear(), request.getConversionMonth(), request.getPeriod(), 
                "否", "是");
            
            // 6. 数据验证：检查转换后的数据一致性
            validateConversionResult(request, totalBalance, "non_litigation_to_litigation");
            
            // 7. 更新后续月份数据（如果是历史转换）
            logger.info("🚀 准备调用后续月份更新方法...");
            try {
                updateSubsequentMonthsAfterConversion(request, "non_litigation_to_litigation");
                logger.info("✅ 后续月份更新方法调用完成");
            } catch (Exception e) {
                logger.error("❌ 后续月份更新方法调用失败", e);
                throw e; // 重新抛出异常
            }
            
            logger.info("非诉讼转诉讼执行成功");
            
            return DebtConversionResponseDTO.success("非诉讼债权已成功转换为诉讼债权", 
                "non_litigation_to_litigation", 1);
                
        } catch (Exception e) {
            logger.error("非诉讼转诉讼执行失败", e);
            throw new RuntimeException("非诉讼转诉讼失败：" + e.getMessage(), e);
        }
    }
    
    // === 私有辅助方法 ===
    
    private List<LitigationClaim> findLitigationClaims(String creditor, String debtor) {
        if (creditor != null && debtor != null) {
            return litigationClaimRepository.findByCreditorAndDebtor(creditor, debtor);
        } else if (creditor != null) {
            return litigationClaimRepository.findByCreditor(creditor);
        } else if (debtor != null) {
            return litigationClaimRepository.findByDebtor(debtor);
        } else {
            return litigationClaimRepository.findAll();
        }
    }
    
    private List<NonLitigationClaim> findNonLitigationClaims(String creditor, String debtor) {
        if (creditor != null && debtor != null) {
            return nonLitigationClaimRepository.findByCreditorAndDebtor(creditor, debtor);
        } else if (creditor != null) {
            return nonLitigationClaimRepository.findByCreditor(creditor);
        } else if (debtor != null) {
            return nonLitigationClaimRepository.findByDebtor(debtor);
        } else {
            return nonLitigationClaimRepository.findAll();
        }
    }
    
    private DebtSearchResultDTO convertLitigationToSearchResult(LitigationClaim claim) {
        return DebtSearchResultDTO.builder()
            .creditor(claim.getId().getCreditor())
            .debtor(claim.getId().getDebtor())
            .period(claim.getId().getPeriod())
            .debtBalance(claim.getCurrentMonthDebtBalance())
            .currentStatus("诉讼")
            .isLitigation("是")
            .managementCompany(claim.getManagementCompany())
            .subjectName(claim.getSubjectName())
            .year(claim.getId().getYear())
            .month(claim.getId().getMonth())
            .sourceTable("litigation")
            .litigationCase(claim.getLitigationCase())
            .build();
    }
    
    private DebtSearchResultDTO convertNonLitigationToSearchResult(NonLitigationClaim claim) {
        BigDecimal totalBalance = (claim.getCurrentMonthPrincipal() != null ? claim.getCurrentMonthPrincipal() : BigDecimal.ZERO)
            .add(claim.getCurrentMonthInterest() != null ? claim.getCurrentMonthInterest() : BigDecimal.ZERO)
            .add(claim.getCurrentMonthPenalty() != null ? claim.getCurrentMonthPenalty() : BigDecimal.ZERO);
            
        return DebtSearchResultDTO.builder()
            .creditor(claim.getId().getCreditor())
            .debtor(claim.getId().getDebtor())
            .period(claim.getId().getPeriod())
            .debtBalance(totalBalance)
            .currentStatus("非诉讼")
            .isLitigation("否")
            .managementCompany(claim.getManagementCompany())
            .subjectName(claim.getSubjectName())
            .year(claim.getId().getYear())
            .month(claim.getId().getMonth())
            .sourceTable("non_litigation")
            .principal(claim.getCurrentMonthPrincipal())
            .interest(claim.getCurrentMonthInterest())
            .penalty(claim.getCurrentMonthPenalty())
            .build();
    }
    
    private void createNonLitigationRecord(DebtConversionRequestDTO request, LitigationClaim sourceClaim, BigDecimal originalCurrentMonthBalance) {
        NonLitigationClaim newClaim = new NonLitigationClaim();
        
        // 设置复合主键
        NonLitigationCompositeKey key = new NonLitigationCompositeKey();
        key.setCreditor(request.getCreditor());
        key.setDebtor(request.getDebtor());
        key.setPeriod(request.getPeriod());
        key.setYear(request.getConversionYear());
        key.setMonth(request.getConversionMonth());
        newClaim.setId(key);
        
        // 设置基本信息
        // 序号字段由数据库自动生成，无需手动设置
        newClaim.setSubjectName(sourceClaim.getSubjectName());
        newClaim.setManagementCompany(sourceClaim.getManagementCompany());
        newClaim.setResponsiblePerson(sourceClaim.getResponsiblePerson());
        
        // 设置金额信息
        newClaim.setLastMonthPrincipal(BigDecimal.ZERO);
        newClaim.setLastMonthInterest(BigDecimal.ZERO);
        newClaim.setLastMonthPenalty(BigDecimal.ZERO);
        
        // 💡 修正逻辑：诉讼转非诉讼时，非诉讼表的本月本金增减 = 诉讼表转换前的本月末债权余额
        BigDecimal transferAmount = originalCurrentMonthBalance != null ? originalCurrentMonthBalance : BigDecimal.ZERO;
        newClaim.setCurrentMonthPrincipalIncreaseDecrease(transferAmount);
        newClaim.setCurrentMonthInterestIncreaseDecrease(BigDecimal.ZERO);
        newClaim.setCurrentMonthPenaltyIncreaseDecrease(BigDecimal.ZERO);
        newClaim.setCurrentMonthPrincipal(transferAmount);
        newClaim.setCurrentMonthInterest(BigDecimal.ZERO);
        newClaim.setCurrentMonthPenalty(BigDecimal.ZERO);
        // 记录互转增加金额（等于诉讼表转换前的本月末债权余额）
        newClaim.setTransferIncreaseAmount(transferAmount);
        
        // 设置其他信息
        newClaim.setAnnualRecoveryTarget(sourceClaim.getAnnualRecoveryTarget() != null ? 
            new BigDecimal(sourceClaim.getAnnualRecoveryTarget()) : BigDecimal.ZERO);
        newClaim.setAnnualCumulativeRecovery(sourceClaim.getAnnualCumulativeRecovery());
        newClaim.setArrangement(sourceClaim.getArrangement());
        
        // remark处理：默认填写转换说明
        String conversionText = String.format("【转换】%d年%d月诉讼转非诉讼", 
            request.getConversionYear(), request.getConversionMonth());
        String userRemark = request.getRemark() != null ? request.getRemark().trim() : "";
        
        String finalRemark;
        if (userRemark.isEmpty()) {
            finalRemark = conversionText;
        } else {
            finalRemark = conversionText + "。" + userRemark;
        }
        
        newClaim.setRemark(finalRemark);
        
        nonLitigationClaimRepository.save(newClaim);
    }
    
    private void createLitigationRecord(DebtConversionRequestDTO request, NonLitigationClaim sourceClaim, 
            BigDecimal originalPrincipal, BigDecimal originalInterest, BigDecimal originalPenalty, BigDecimal totalBalance) {
        LitigationClaim newClaim = new LitigationClaim();
        
        // 设置复合主键
        LitigationCompositeKey key = new LitigationCompositeKey();
        key.setCreditor(request.getCreditor());
        key.setDebtor(request.getDebtor());
        key.setPeriod(request.getPeriod());
        key.setYear(request.getConversionYear());
        key.setMonth(request.getConversionMonth());
        newClaim.setId(key);
        
        // 设置基本信息
        // 序号字段由数据库自动生成，无需手动设置
        newClaim.setLitigationCase(request.getLitigationCase());
        newClaim.setSubjectName(sourceClaim.getSubjectName());
        newClaim.setManagementCompany(sourceClaim.getManagementCompany());
        newClaim.setResponsiblePerson(sourceClaim.getResponsiblePerson());
        
        // 设置债权金额（上月末债权余额设为0）
        newClaim.setLastMonthDebtBalance(BigDecimal.ZERO);
        newClaim.setLitigationPrincipal(originalPrincipal != null ? originalPrincipal : BigDecimal.ZERO);
        newClaim.setLitigationInterest((originalInterest != null ? originalInterest : BigDecimal.ZERO)
            .add(originalPenalty != null ? originalPenalty : BigDecimal.ZERO));
        newClaim.setCurrentMonthDebtBalance(totalBalance);
        // 记录互转增加金额
        newClaim.setTransferIncreaseAmount(totalBalance);
        
        // 设置诉讼特有字段
        newClaim.setLitigationOccurredPrincipal(request.getLitigationOccurredPrincipal());
        newClaim.setLitigationInterestFee(request.getLitigationInterestFee());
        newClaim.setLitigationFee(request.getLitigationFee());
        newClaim.setIntermediaryFee(request.getIntermediaryFee());
        
        // 设置其他信息
        newClaim.setAnnualRecoveryTarget(sourceClaim.getAnnualRecoveryTarget() != null ? 
            sourceClaim.getAnnualRecoveryTarget().toString() : "0");
        newClaim.setAnnualCumulativeRecovery(sourceClaim.getAnnualCumulativeRecovery());
        newClaim.setArrangement(sourceClaim.getArrangement());
        
        // remark处理：默认填写转换说明
        String conversionText = String.format("【转换】%d年%d月非诉讼转诉讼", 
            request.getConversionYear(), request.getConversionMonth());
        String userRemark = request.getRemark() != null ? request.getRemark().trim() : "";
        
        String finalRemark;
        if (userRemark.isEmpty()) {
            finalRemark = conversionText;
        } else {
            finalRemark = conversionText + "。" + userRemark;
        }
        
        newClaim.setRemark(finalRemark);
        
        litigationClaimRepository.save(newClaim);
    }
    
    private void updateImpairmentReserveForConversion(String creditor, String debtor, Integer year, Integer month, 
            String period, String fromLitigation, String toLitigation) {
        try {
            // 查找原减值准备记录
            ImpairmentReserveKey oldKey = new ImpairmentReserveKey();
            oldKey.setCreditor(creditor);
            oldKey.setDebtor(debtor);
            oldKey.setYear(year);
            oldKey.setMonth(month);
            oldKey.setPeriod(period);
            oldKey.setIsLitigation(fromLitigation);
            
            Optional<ImpairmentReserve> reserveOpt = impairmentReserveRepository.findById(oldKey);
            if (reserveOpt.isPresent()) {
                ImpairmentReserve oldReserve = reserveOpt.get();
                
                // 创建新的减值准备记录（新主键）
                ImpairmentReserve newReserve = new ImpairmentReserve();
                ImpairmentReserveKey newKey = new ImpairmentReserveKey();
                newKey.setCreditor(creditor);
                newKey.setDebtor(debtor);
                newKey.setYear(year);
                newKey.setMonth(month);
                newKey.setPeriod(period);
                newKey.setIsLitigation(toLitigation);
                newReserve.setId(newKey);
                
                // 复制所有数据
                newReserve.setSubjectName(oldReserve.getSubjectName());
                newReserve.setManagementCompany(oldReserve.getManagementCompany());
                newReserve.setCaseName(oldReserve.getCaseName());
                newReserve.setLastMonthBalance(oldReserve.getLastMonthBalance());
                newReserve.setCurrentMonthBalance(oldReserve.getCurrentMonthBalance());
                newReserve.setImpairmentAmount(oldReserve.getImpairmentAmount());
                newReserve.setInitialImpairmentDate(oldReserve.getInitialImpairmentDate());
                newReserve.setPreviousMonthBalance(oldReserve.getPreviousMonthBalance());
                newReserve.setCurrentMonthIncreaseDecrease(oldReserve.getCurrentMonthIncreaseDecrease());
                newReserve.setCurrentMonthAmount(oldReserve.getCurrentMonthAmount());
                newReserve.setAnnualRecoveryTarget(oldReserve.getAnnualRecoveryTarget());
                newReserve.setAnnualCumulativeRecovery(oldReserve.getAnnualCumulativeRecovery());
                newReserve.setRemark(oldReserve.getRemark());
                newReserve.setIsAllImpaired(oldReserve.getIsAllImpaired());
                newReserve.setCurrentMonthNewDebt(oldReserve.getCurrentMonthNewDebt());
                newReserve.setCurrentMonthDisposeDebt(oldReserve.getCurrentMonthDisposeDebt());
                
                // 先保存新记录，再删除旧记录（避免主键冲突）
                impairmentReserveRepository.save(newReserve);
                impairmentReserveRepository.delete(oldReserve);
                
                logger.info("减值准备表更新成功，是否涉诉从 {} 更新为 {}", fromLitigation, toLitigation);
            } else {
                logger.warn("未找到对应的减值准备记录，无法更新是否涉诉状态");
            }
        } catch (Exception e) {
            logger.error("更新减值准备表时发生错误", e);
            // 不抛出异帰，避免影响主要转换流程
        }
    }
    
    /**
     * 验证转换结果的数据一致性
     * 公式：修改后的诉讼表本月末债权余额 + 非诉讼表本月末本金 = 减值准备表本月末债权余额
     */
    private void validateConversionResult(DebtConversionRequestDTO request, BigDecimal convertedAmount, String conversionType) {
        try {
            logger.info("开始验证转换结果的数据一致性，转换类型：{}，转换金额：{}", conversionType, convertedAmount);
            
            // 1. 查询诉讼表的本月末债权余额
            LitigationCompositeKey litigationKey = new LitigationCompositeKey();
            litigationKey.setCreditor(request.getCreditor());
            litigationKey.setDebtor(request.getDebtor());
            litigationKey.setPeriod(request.getPeriod());
            litigationKey.setYear(request.getConversionYear());
            litigationKey.setMonth(request.getConversionMonth());
            
            BigDecimal litigationBalance = BigDecimal.ZERO;
            Optional<LitigationClaim> litigationOpt = litigationClaimRepository.findById(litigationKey);
            if (litigationOpt.isPresent()) {
                litigationBalance = litigationOpt.get().getCurrentMonthDebtBalance();
                litigationBalance = litigationBalance != null ? litigationBalance : BigDecimal.ZERO;
            }
            
            // 2. 查询非诉讼表的本月末本金
            NonLitigationCompositeKey nonLitigationKey = new NonLitigationCompositeKey();
            nonLitigationKey.setCreditor(request.getCreditor());
            nonLitigationKey.setDebtor(request.getDebtor());
            nonLitigationKey.setPeriod(request.getPeriod());
            nonLitigationKey.setYear(request.getConversionYear());
            nonLitigationKey.setMonth(request.getConversionMonth());
            
            BigDecimal nonLitigationPrincipal = BigDecimal.ZERO;
            Optional<NonLitigationClaim> nonLitigationOpt = nonLitigationClaimRepository.findById(nonLitigationKey);
            if (nonLitigationOpt.isPresent()) {
                nonLitigationPrincipal = nonLitigationOpt.get().getCurrentMonthPrincipal();
                nonLitigationPrincipal = nonLitigationPrincipal != null ? nonLitigationPrincipal : BigDecimal.ZERO;
            }
            
            // 3. 查询减值准备表的本月末债权余额（查询所有涉诉状态并求和）
            logger.info("查询减值准备表：债权人={}, 债务人={}, 年份={}, 月份={}, 期间={}", 
                request.getCreditor(), request.getDebtor(), request.getConversionYear(), 
                request.getConversionMonth(), request.getPeriod());
            
            BigDecimal impairmentBalance = BigDecimal.ZERO;
            
            // 查询涉诉状态为"是"的记录
            ImpairmentReserveKey litigationImpairmentKey = new ImpairmentReserveKey();
            litigationImpairmentKey.setCreditor(request.getCreditor());
            litigationImpairmentKey.setDebtor(request.getDebtor());
            litigationImpairmentKey.setPeriod(request.getPeriod());
            litigationImpairmentKey.setYear(request.getConversionYear());
            litigationImpairmentKey.setMonth(request.getConversionMonth());
            litigationImpairmentKey.setIsLitigation("是");
            
            Optional<ImpairmentReserve> litigationReserveOpt = impairmentReserveRepository.findById(litigationImpairmentKey);
            BigDecimal litigationImpairment = BigDecimal.ZERO;
            if (litigationReserveOpt.isPresent()) {
                litigationImpairment = litigationReserveOpt.get().getCurrentMonthBalance();
                litigationImpairment = litigationImpairment != null ? litigationImpairment : BigDecimal.ZERO;
                logger.info("找到涉诉减值准备记录，本月末余额：{}", litigationImpairment);
            }
            
            // 查询涉诉状态为"否"的记录
            ImpairmentReserveKey nonLitigationImpairmentKey = new ImpairmentReserveKey();
            nonLitigationImpairmentKey.setCreditor(request.getCreditor());
            nonLitigationImpairmentKey.setDebtor(request.getDebtor());
            nonLitigationImpairmentKey.setPeriod(request.getPeriod());
            nonLitigationImpairmentKey.setYear(request.getConversionYear());
            nonLitigationImpairmentKey.setMonth(request.getConversionMonth());
            nonLitigationImpairmentKey.setIsLitigation("否");
            
            Optional<ImpairmentReserve> nonLitigationReserveOpt = impairmentReserveRepository.findById(nonLitigationImpairmentKey);
            BigDecimal nonLitigationImpairment = BigDecimal.ZERO;
            if (nonLitigationReserveOpt.isPresent()) {
                nonLitigationImpairment = nonLitigationReserveOpt.get().getCurrentMonthBalance();
                nonLitigationImpairment = nonLitigationImpairment != null ? nonLitigationImpairment : BigDecimal.ZERO;
                logger.info("找到非涉诉减值准备记录，本月末余额：{}", nonLitigationImpairment);
            }
            
            // 求和所有减值准备金额
            impairmentBalance = litigationImpairment.add(nonLitigationImpairment);
            logger.info("减值准备表总金额：{} (涉诉:{} + 非涉诉:{})", 
                impairmentBalance, litigationImpairment, nonLitigationImpairment);
            
            // 4. 验证公式：诉讼表本月末债权余额 + 非诉讼表本月末本金 = 减值准备表本月末债权余额
            BigDecimal leftSide = litigationBalance.add(nonLitigationPrincipal);
            BigDecimal rightSide = impairmentBalance;
            
            logger.info("数据验证结果：");
            logger.info("诉讼表本月末债权余额：{}", litigationBalance);
            logger.info("非诉讼表本月末本金：{}", nonLitigationPrincipal);
            logger.info("减值准备表本月末债权余额：{}", impairmentBalance);
            logger.info("公式验证：{} + {} = {} (左侧={}, 右侧={})", 
                litigationBalance, nonLitigationPrincipal, impairmentBalance, leftSide, rightSide);
            
            // 允许小数点误差（0.01元）
            BigDecimal tolerance = new BigDecimal("0.01");
            BigDecimal difference = leftSide.subtract(rightSide).abs();
            
            if (difference.compareTo(tolerance) <= 0) {
                logger.info("✅ 数据验证通过：转换后的数据一致性正常，误差：{}", difference);
            } else {
                String warnMsg = String.format("数据验证警告：转换后的数据不一致，误差：%s元（超出容差范围%s元）", 
                    difference, tolerance);
                logger.warn("⚠️ {}", warnMsg);
                logger.warn("⚠️ 可能原因：");
                logger.warn("1. 减值准备表中没有对应的记录");
                logger.warn("2. 减值准备表的数据需要单独维护");
                logger.warn("3. 互转操作不影响减值准备表的本月末余额");
                logger.warn("⚠️ 转换操作已成功完成，但请注意检查减值准备表数据");
                // 暂时不抛出异常，只警告
                // throw new RuntimeException(warnMsg);
            }
            
        } catch (Exception e) {
            if (e instanceof RuntimeException) {
                throw e;
            }
            logger.error("数据验证过程中发生错误", e);
            throw new RuntimeException("数据验证失败：" + e.getMessage(), e);
        }
    }
    
    /**
     * 更新后续月份数据（诉讼非诉讼转换后）
     * 当在非当前月份进行转换时，需要更新后续所有月份的数据
     */
    @Transactional
    public void updateSubsequentMonthsAfterConversion(DebtConversionRequestDTO request, String conversionType) {
        int conversionYear = request.getConversionYear();
        int conversionMonth = request.getConversionMonth();
        
        // 获取当前年月
        int currentYear = LocalDate.now().getYear();
        int currentMonth = LocalDate.now().getMonthValue();
        
        // 检查是否需要更新后续月份
        logger.info("🔍 后续月份更新检查 - 转换年月: {}-{}, 当前年月: {}-{}", 
            conversionYear, conversionMonth, currentYear, currentMonth);
        
        if (conversionYear < currentYear || (conversionYear == currentYear && conversionMonth < currentMonth)) {
            logger.info("✅ 检测到在历史月份({}-{})进行债权转换，将更新后续月份数据，转换类型：{}", 
                conversionYear, conversionMonth, conversionType);
            
            // 创建更新上下文
            FiveTableUpdateHelper.UpdateContext context = new FiveTableUpdateHelper.UpdateContext();
            context.setCreditor(request.getCreditor());
            context.setDebtor(request.getDebtor());
            context.setPeriod(request.getPeriod());
            context.setYear(conversionYear);
            context.setMonth(conversionMonth);
            context.setOperationType("CONVERSION"); // 标识为转换操作
            context.setRemark("债权转换后续月份更新");
            
            // 获取需要更新的后续月份
            Calendar[] subsequentMonths = FiveTableUpdateHelper.getSubsequentMonths(context);
            logger.info("📅 计算出需要更新的后续月份数量: {}", subsequentMonths.length);
            
            for (Calendar monthCal : subsequentMonths) {
                int updateYear = monthCal.get(Calendar.YEAR);
                int updateMonth = monthCal.get(Calendar.MONTH) + 1;
                
                logger.info("更新后续月份 {}-{} 的数据", updateYear, updateMonth);
                
                // 根据转换类型更新相应的表
                if ("litigation_to_non_litigation".equals(conversionType)) {
                    updateSubsequentMonthAfterLitigationToNonLitigation(request, updateYear, updateMonth);
                } else if ("non_litigation_to_litigation".equals(conversionType)) {
                    updateSubsequentMonthAfterNonLitigationToLitigation(request, updateYear, updateMonth);
                }
            }
            
            logger.info("✅ 历史转换后续月份更新完成");
        } else {
            logger.info("ℹ️ 转换月份为当前月份或未来月份，无需更新后续月份");
        }
    }
    
    /**
     * 更新诉讼转非诉讼后的后续月份数据
     */
    private void updateSubsequentMonthAfterLitigationToNonLitigation(DebtConversionRequestDTO request, int year, int month) {
        try {
            // 1. 更新减值准备表（涉诉状态从"是"变为"否"）
            updateSubsequentImpairmentReserveAfterConversion(request, year, month, "否");
            
            // 2. 更新诉讼表（余额应该为0）
            updateSubsequentLitigationAfterConversion(request, year, month);
            
            // 3. 更新非诉讼表（继承转换的债权余额）
            updateSubsequentNonLitigationAfterConversion(request, year, month);
            
        } catch (Exception e) {
            logger.error("更新诉讼转非诉讼后续月份({}-{})数据失败", year, month, e);
            throw new RuntimeException("更新后续月份数据失败：" + e.getMessage(), e);
        }
    }
    
    /**
     * 更新非诉讼转诉讼后的后续月份数据
     */
    private void updateSubsequentMonthAfterNonLitigationToLitigation(DebtConversionRequestDTO request, int year, int month) {
        try {
            // 1. 更新减值准备表（涉诉状态从"否"变为"是"）
            updateSubsequentImpairmentReserveAfterConversion(request, year, month, "是");
            
            // 2. 更新非诉讼表（余额应该为0）
            updateSubsequentNonLitigationAfterConversion(request, year, month);
            
            // 3. 更新诉讼表（继承转换的债权余额）
            updateSubsequentLitigationAfterConversion(request, year, month);
            
        } catch (Exception e) {
            logger.error("更新非诉讼转诉讼后续月份({}-{})数据失败", year, month, e);
            throw new RuntimeException("更新后续月份数据失败：" + e.getMessage(), e);
        }
    }
    
    /**
     * 更新后续月份的减值准备表
     */
    private void updateSubsequentImpairmentReserveAfterConversion(DebtConversionRequestDTO request, int year, int month, String newLitigationStatus) {
        // 查找相应涉诉状态的减值准备记录
        ImpairmentReserveKey key = new ImpairmentReserveKey();
        key.setCreditor(request.getCreditor());
        key.setDebtor(request.getDebtor());
        key.setPeriod(request.getPeriod());
        key.setYear(year);
        key.setMonth(month);
        key.setIsLitigation(newLitigationStatus);
        
        Optional<ImpairmentReserve> existingRecord = impairmentReserveRepository.findById(key);
        
        if (existingRecord.isPresent()) {
            ImpairmentReserve entity = existingRecord.get();
            
            // 获取上月数据来更新本月初余额
            ImpairmentReserveKey prevKey = new ImpairmentReserveKey();
            prevKey.setCreditor(request.getCreditor());
            prevKey.setDebtor(request.getDebtor());
            prevKey.setPeriod(request.getPeriod());
            prevKey.setYear(month == 1 ? year - 1 : year);
            prevKey.setMonth(month == 1 ? 12 : month - 1);
            prevKey.setIsLitigation(newLitigationStatus);
            
            Optional<ImpairmentReserve> prevRecord = impairmentReserveRepository.findById(prevKey);
            
            if (prevRecord.isPresent()) {
                ImpairmentReserve prev = prevRecord.get();
                
                // 更新本月初余额
                entity.setLastMonthBalance(prev.getCurrentMonthBalance());
                entity.setPreviousMonthBalance(prev.getCurrentMonthAmount());
                
                // 重新计算本月末余额
                BigDecimal newDebt = entity.getCurrentMonthNewDebt() != null ? 
                    entity.getCurrentMonthNewDebt() : BigDecimal.ZERO;
                BigDecimal disposeDebt = entity.getCurrentMonthDisposeDebt() != null ? 
                    entity.getCurrentMonthDisposeDebt() : BigDecimal.ZERO;
                
                BigDecimal currentMonthBalance = prev.getCurrentMonthBalance()
                    .add(newDebt)
                    .subtract(disposeDebt);
                entity.setCurrentMonthBalance(currentMonthBalance);
                
                // 重新计算减值准备余额
                BigDecimal increaseDecrease = entity.getCurrentMonthIncreaseDecrease() != null ?
                    entity.getCurrentMonthIncreaseDecrease() : BigDecimal.ZERO;
                BigDecimal currentMonthAmount = prev.getCurrentMonthAmount().add(increaseDecrease);
                entity.setCurrentMonthAmount(currentMonthAmount);
                entity.setImpairmentAmount(currentMonthAmount);
                
                impairmentReserveRepository.save(entity);
                
                logger.info("更新后续月份({}-{})减值准备表(涉诉状态:{})完成", year, month, newLitigationStatus);
            }
        }
    }
    
    /**
     * 更新后续月份的诉讼表
     */
    private void updateSubsequentLitigationAfterConversion(DebtConversionRequestDTO request, int year, int month) {
        logger.info("🔍 开始更新后续月份({}-{})诉讼表数据", year, month);
        Optional<LitigationClaim> existingRecord = litigationClaimRepository
            .findByCreditorAndDebtorAndYearAndMonthAndPeriod(
                request.getCreditor(), request.getDebtor(), year, month, request.getPeriod());
        
        if (existingRecord.isPresent()) {
            logger.info("✅ 找到后续月份({}-{})诉讼表记录，开始更新", year, month);
            LitigationClaim entity = existingRecord.get();
            
            // 获取上月数据
            Optional<LitigationClaim> prevRecord = litigationClaimRepository
                .findByCreditorAndDebtorAndYearAndMonthAndPeriod(
                    request.getCreditor(), request.getDebtor(),
                    month == 1 ? year - 1 : year,
                    month == 1 ? 12 : month - 1,
                    request.getPeriod());
            
            if (prevRecord.isPresent()) {
                LitigationClaim prev = prevRecord.get();
                
                // 更新上月末债权余额
                entity.setLastMonthDebtBalance(prev.getCurrentMonthDebtBalance());
                
                // 重新计算本月末债权余额
                BigDecimal newDebt = entity.getCurrentMonthNewDebt() != null ?
                    entity.getCurrentMonthNewDebt() : BigDecimal.ZERO;
                BigDecimal disposeDebt = entity.getCurrentMonthDisposalDebt() != null ?
                    entity.getCurrentMonthDisposalDebt() : BigDecimal.ZERO;
                
                BigDecimal currentMonthBalance = prev.getCurrentMonthDebtBalance()
                    .add(newDebt)
                    .subtract(disposeDebt);
                entity.setCurrentMonthDebtBalance(currentMonthBalance);
                
                litigationClaimRepository.save(entity);
                
                logger.info("✅ 更新后续月份({}-{})诉讼表完成", year, month);
            } else {
                logger.warn("❌ 找不到上月({}-{})诉讼表记录，无法更新", 
                    month == 1 ? year - 1 : year, month == 1 ? 12 : month - 1);
            }
        } else {
            logger.warn("❌ 找不到后续月份({}-{})诉讼表记录，无法更新", year, month);
        }
    }
    
    /**
     * 更新后续月份的非诉讼表
     */
    private void updateSubsequentNonLitigationAfterConversion(DebtConversionRequestDTO request, int year, int month) {
        Optional<NonLitigationClaim> existingRecord = nonLitigationClaimRepository
            .findByCreditorAndDebtorAndYearAndMonthAndPeriod(
                request.getCreditor(), request.getDebtor(), year, month, request.getPeriod());
        
        if (existingRecord.isPresent()) {
            NonLitigationClaim entity = existingRecord.get();
            
            // 获取上月数据
            Optional<NonLitigationClaim> prevRecord = nonLitigationClaimRepository
                .findByCreditorAndDebtorAndYearAndMonthAndPeriod(
                    request.getCreditor(), request.getDebtor(),
                    month == 1 ? year - 1 : year,
                    month == 1 ? 12 : month - 1,
                    request.getPeriod());
            
            if (prevRecord.isPresent()) {
                NonLitigationClaim prev = prevRecord.get();
                
                // 更新上月末数据
                entity.setLastMonthPrincipal(prev.getCurrentMonthPrincipal());
                entity.setLastMonthInterest(prev.getCurrentMonthInterest());
                entity.setLastMonthPenalty(prev.getCurrentMonthPenalty());
                
                // 重新计算本月末数据
                BigDecimal principalIncrease = entity.getCurrentMonthPrincipalIncreaseDecrease() != null ?
                    entity.getCurrentMonthPrincipalIncreaseDecrease() : BigDecimal.ZERO;
                BigDecimal interestIncrease = entity.getCurrentMonthInterestIncreaseDecrease() != null ?
                    entity.getCurrentMonthInterestIncreaseDecrease() : BigDecimal.ZERO;
                BigDecimal penaltyIncrease = entity.getCurrentMonthPenaltyIncreaseDecrease() != null ?
                    entity.getCurrentMonthPenaltyIncreaseDecrease() : BigDecimal.ZERO;
                
                entity.setCurrentMonthPrincipal(prev.getCurrentMonthPrincipal().add(principalIncrease));
                entity.setCurrentMonthInterest(prev.getCurrentMonthInterest().add(interestIncrease));
                entity.setCurrentMonthPenalty(prev.getCurrentMonthPenalty().add(penaltyIncrease));
                
                nonLitigationClaimRepository.save(entity);
                
                logger.info("更新后续月份({}-{})非诉讼表完成", year, month);
            }
        }
    }
}
#!/bin/bash
# MCP 服务器配置验证脚本

echo "🔧 Claude Code MCP 配置验证"
echo "================================"

echo ""
echo "📋 已安装的MCP服务器："
claude mcp list

echo ""
echo "🎯 关键MCP服务器测试："

echo "测试 Context7..."
npx -y @upstash/context7-mcp --help > /dev/null 2>&1 && echo "✅ Context7 可用" || echo "❌ Context7 错误"

echo "测试 Database MCP..."
npx -y database-mcp --help > /dev/null 2>&1 && echo "✅ Database MCP 可用" || echo "❌ Database MCP 错误"

echo "测试 Docker MCP..."
npx -y @edjl/docker-mcp --help > /dev/null 2>&1 && echo "✅ Docker MCP 可用" || echo "❌ Docker MCP 错误"

echo "测试 Magic UI..."
npx -y @21st-dev/magic --help > /dev/null 2>&1 && echo "✅ Magic UI 可用" || echo "❌ Magic UI 错误"

echo ""
echo "📄 配置文件检查："
[ -f ".claude/mcp-auto-rules.yml" ] && echo "✅ 自动调用规则已配置" || echo "❌ 缺少自动调用规则"
[ -f "CLAUDE.md" ] && echo "✅ CLAUDE.md 已更新" || echo "❌ CLAUDE.md 缺失"

echo ""
echo "🚀 MCP系统配置完成！"
echo ""
echo "现在Claude Code会自动在以下场景使用MCP服务器："
echo "• Docker/容器操作 → docker-mcp"
echo "• 数据库查询 → database-multi" 
echo "• 官方文档查询 → context7"
echo "• UI组件生成 → magic-ui"
echo "• 复杂分析 → sequential-thinking"
echo "• 多智能体协作 → agent-collaboration"
echo ""
echo "详细规则请查看: .claude/mcp-auto-rules.yml"
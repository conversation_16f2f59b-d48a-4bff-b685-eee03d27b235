# Claude Code MCP 自动调用规则配置
# 让Claude知道什么时候自动调用哪些MCP服务器

## 🤖 智能触发规则

### Docker 操作 (docker-mcp, docker-advanced)
Docker_Triggers:
  - keywords: ["docker", "容器", "container", "镜像", "image", "dockerfile", "docker-compose"]
  - scenarios:
    - "构建Docker镜像" → docker-mcp
    - "管理容器生命周期" → docker-mcp  
    - "复杂Docker编排" → docker-advanced
    - "Docker网络和卷管理" → docker-advanced
  - auto_use: "当用户提到容器相关任务时"

### 数据库操作 (database-multi, sqlite-mcp)
Database_Triggers:
  - keywords: ["数据库", "database", "sql", "mysql", "postgresql", "sqlite", "查询", "表结构"]
  - scenarios:
    - "多数据库查询分析" → database-multi
    - "SQLite本地数据库" → sqlite-mcp
    - "数据库结构分析" → database-multi
    - "SQL优化建议" → database-multi
  - auto_use: "当用户询问数据库相关操作时"

### 文档研究 (context7)
Context7_Triggers:
  - keywords: ["文档", "documentation", "API参考", "官方文档", "库文档", "框架文档"]
  - scenarios:
    - "查找外部库文档" → context7
    - "API集成指南" → context7
    - "框架最佳实践" → context7
    - "版本兼容性检查" → context7
  - auto_use: "当用户询问外部库或框架使用方法时"

### 复杂分析 (sequential-thinking)
Sequential_Triggers:
  - keywords: ["分析", "设计", "架构", "复杂问题", "系统设计", "优化", "故障排除"]
  - scenarios:
    - "系统架构设计" → sequential-thinking
    - "性能优化分析" → sequential-thinking
    - "复杂bug调试" → sequential-thinking
    - "技术决策分析" → sequential-thinking
  - auto_use: "当遇到需要深度分析的多步骤问题时"

### UI开发 (magic-ui)
Magic_UI_Triggers:
  - keywords: ["UI", "组件", "component", "界面", "前端", "React", "Vue", "设计系统"]
  - scenarios:
    - "React组件生成" → magic-ui
    - "UI组件库集成" → magic-ui
    - "设计系统实现" → magic-ui
    - "快速原型开发" → magic-ui
  - auto_use: "当用户需要创建或修改UI组件时"

### 多智能体协作 (agent-collaboration, multi-agent-subagents, claude-flow)
Multi_Agent_Triggers:
  - keywords: ["复杂任务", "多步骤", "协作", "并行处理", "工作流", "自动化"]
  - scenarios:
    - "复杂项目分解" → agent-collaboration
    - "并行任务处理" → multi-agent-subagents
    - "企业级工作流" → claude-flow
    - "智能体间协调" → agent-collaboration
  - auto_use: "当任务复杂度超过单一智能体能力时"

### LangChain集成 (langchain-adapters, langchain-tools)
LangChain_Triggers:
  - keywords: ["langchain", "工作流", "链式调用", "AI流水线", "工具链"]
  - scenarios:
    - "AI工作流编排" → langchain-adapters
    - "工具链整合" → langchain-tools
    - "LLM应用开发" → langchain-adapters
  - auto_use: "当用户构建AI应用工作流时"

### 浏览器自动化 (puppeteer)
Puppeteer_Triggers:
  - keywords: ["测试", "E2E", "浏览器", "自动化", "性能监控", "截图"]
  - scenarios:
    - "端到端测试" → puppeteer
    - "性能监控" → puppeteer
    - "UI自动化测试" → puppeteer
    - "网页截图生成" → puppeteer
  - auto_use: "当需要浏览器自动化操作时"

## 🎯 智能路由策略

### 任务复杂度评估
Simple_Tasks:
  - trigger: "单一操作，明确需求"
  - strategy: "使用原生工具优先"
  - mcp_threshold: "仅在专业领域使用"

Medium_Tasks:
  - trigger: "多步骤操作，需要专业知识"
  - strategy: "选择最相关的单一MCP服务器"
  - examples: ["Docker部署", "数据库查询", "UI组件创建"]

Complex_Tasks:
  - trigger: "跨领域，需要协调多个系统"
  - strategy: "协调多个MCP服务器并行工作"
  - examples: ["全栈应用开发", "系统重构", "性能优化"]

### 成本优化规则
Token_Economy:
  - light_usage: "优先原生工具 → 必要时单一MCP"
  - moderate_usage: "战略性使用多个MCP"
  - heavy_usage: "全面MCP协作，最大化能力"

### 优先级排序
Priority_Order:
  1. context7: "外部文档查询（准确性关键）"
  2. sequential-thinking: "复杂分析（质量关键）"
  3. database-multi: "数据库操作（功能关键）"
  4. docker-mcp: "容器操作（开发关键）"
  5. magic-ui: "UI开发（效率关键）"
  6. agent-collaboration: "多任务协调（规模关键）"

## 🚦 自动决策逻辑

### 触发条件匹配
Auto_Trigger_Logic: |
  IF (user_message contains database_keywords) 
    THEN activate database-multi
  ELSE IF (user_message contains docker_keywords)
    THEN activate docker-mcp
  ELSE IF (user_message contains complex_analysis_pattern)
    THEN activate sequential-thinking
  ELSE IF (user_message contains external_library_query)
    THEN activate context7
  ELSE IF (user_message contains ui_component_request)
    THEN activate magic-ui
  ELSE IF (task_complexity > threshold)
    THEN activate multi-agent coordination

### 失败回滚策略
Fallback_Strategy:
  - mcp_server_error: "切换到原生工具继续"
  - partial_results: "结合MCP输出与原生分析"
  - timeout_issues: "缓存部分结果，继续处理"

## 📊 使用监控

Performance_Metrics:
  - success_rate: "追踪MCP服务器成功率"
  - response_time: "监控响应时间"
  - token_usage: "优化token消耗"
  - user_satisfaction: "基于任务完成质量"

Quality_Gates:
  - accuracy_threshold: "结果准确性要求"
  - performance_baseline: "响应时间标准"
  - cost_efficiency: "token使用效率"

---

*配置说明：此文件让Claude Code知道何时自动调用特定的MCP服务器，实现智能化的工具选择和任务路由。*
# Agent 颜色配置指南

## 📋 概述

本指南说明了财务管理系统中各Agent的颜色配置标准，基于Material Design 3.0和用户体验最佳实践设计。

## 🎨 配色原则

### 色彩心理学
- **🔵 蓝色系** (`#1976D2`): 专业、可信、稳定 → 核心功能agent
- **🟢 绿色系** (`#388E3C`): 安全、成功、创造 → 正面操作agent  
- **🟠 橙色系** (`#F57C00`): 注意、活跃、修复 → 需要关注的agent
- **🔴 红色系** (`#D32F2F`): 重要、警告、安全 → 关键安全agent
- **🟣 紫色系** (`#7B1FA2`): 智慧、创新、洞察 → 分析智能agent
- **⚫ 灰色系** (`#616161`): 中性、稳定、工具 → 辅助工具agent

### 可访问性标准
- 对比度符合 **WCAG AA标准** (4.5:1)
- **色盲友好**设计
- **深色/浅色主题**兼容

## 📊 Agent颜色分配表

| Agent类型 | 颜色代码 | 颜色 | 功能分类 | 使用原因 |
|----------|---------|------|---------|----------|
| **search-agent** | `#1976D2` | 🔵 Material Blue 700 | 核心服务 | 核心搜索功能，高频使用 |
| **fix-agent** | `#F57C00` | 🟠 Material Orange 700 | 修复维护 | 通用修复，需要引起注意 |
| **backend-fix-agent** | `#FF9800` | 🟠 Material Orange 500 | 修复维护 | 后端修复，橙色系 |
| **frontend-fix-agent** | `#FF8F00` | 🟠 Material Amber 700 | 修复维护 | 前端修复，橙色系变种 |
| **syntax-fix-agent** | `#FF6F00` | 🟠 Material Orange 800 | 修复维护 | 语法修复，深橙色 |
| **logic-fix-agent** | `#E65100` | 🟠 Material Orange 900 | 修复维护 | 逻辑修复，最深橙色 |
| **integration-fix-agent** | `#FB8C00` | 🟠 Material Orange 600 | 修复维护 | 集成修复，中等橙色 |
| **safety-agent** | `#D32F2F` | 🔴 Material Red 700 | 安全关键 | 安全守护，警示红色 |
| **deploy-agent** | `#388E3C` | 🟢 Material Green 700 | 开发工具 | 部署成功，绿色 |
| **test-agent** | `#4CAF50` | 🟢 Material Green 500 | 开发工具 | 测试验证，验证通过 |
| **review-agent** | `#7B1FA2` | 🟣 Material Purple 700 | 分析智能 | 代码审查，智慧紫色 |
| **document-agent** | `#9C27B0` | 🟣 Material Purple 500 | 分析智能 | 文档生成，紫色系 |
| **learning-agent** | `#512DA8` | 🟣 Material Deep Purple 700 | 分析智能 | 学习指导，深紫色代表知识 |
| **assistant-agent** | `#303F9F` | 🟣 Material Indigo 700 | 分析智能 | 智能助手，靛蓝色代表智能 |
| **todo-agent** | `#616161` | ⚫ Material Grey 700 | 工具支持 | 任务管理，中性工具 |
| **snapshot-agent** | `#757575` | ⚫ Material Grey 600 | 工具支持 | 快照管理，辅助工具 |
| **requirements-analyst** | `#1976D2` | 🔵 Material Blue 700 | 核心服务 | 需求分析，核心业务功能 |
| **technical-design-agent** | `#1565C0` | 🔵 Material Blue 800 | 核心服务 | 技术设计，深蓝色 |
| **implementation-planner** | `#0D47A1` | 🔵 Material Blue 900 | 核心服务 | 实施规划，最深蓝色 |

## 🌙 深色主题适配

深色主题下使用较亮的颜色变种，保持良好的对比度：

| 浅色主题 | 深色主题 | 调整规则 |
|---------|---------|----------|
| 700 | 400 | 深色 → 中亮色 |
| 800 | 500 | 更深 → 标准色 |
| 900 | 600 | 最深 → 较亮色 |

## 📐 配色系统层次

### 重要性分级
1. **最高优先级**：安全类agent（红色系）
2. **高优先级**：修复类agent（橙色系）
3. **中等优先级**：核心功能agent（蓝色系）
4. **一般优先级**：开发工具agent（绿色系）
5. **辅助级别**：分析智能agent（紫色系）
6. **基础级别**：工具支持agent（灰色系）

### 饱和度规则
- **核心agent**：使用高饱和度颜色（700-900）
- **辅助agent**：使用中等饱和度（500-600）
- **工具agent**：使用低饱和度（400以下）

## 🎯 使用指南

### 新增Agent时
1. 确定Agent的功能分类
2. 根据重要性选择合适的色系
3. 参考同类Agent的颜色深浅
4. 确保颜色不重复或过于相似

### 配置格式
```yaml
---
name: agent-name
description: Agent描述
model: opus/sonnet
color: "#HEX_COLOR"  # Material Color_Name Number - 中文说明
tools: [tool1, tool2]
---
```

### 验证清单
- [ ] 所有agent都有明确颜色定义
- [ ] 同类agent颜色保持一致性
- [ ] 对比度符合无障碍标准
- [ ] 深色主题适配完整
- [ ] 颜色使用符合心理学原则

## 🔧 维护说明

### 定期检查
- 每季度检查颜色使用一致性
- 根据用户反馈调整颜色
- 新增功能时及时更新配色

### 配置文件位置
- **主配置**: `.claude/agents/core/agent-colors.yml`
- **Agent配置**: `.claude/agents/**/*.md`
- **文档**: `.claude/agents/docs/agent-color-guide.md`

## 🎨 设计效果预览

```
核心服务: 🔵🔵🔵 (蓝色系 - 专业稳定)
修复维护: 🟠🟠🟠 (橙色系 - 警示修复)  
安全关键: 🔴🔴🔴 (红色系 - 安全警告)
分析智能: 🟣🟣🟣 (紫色系 - 智慧分析)
开发工具: 🟢🟢🟢 (绿色系 - 成功创造)
工具支持: ⚫⚫⚫ (灰色系 - 中性工具)
```

## 📚 参考资料

- [Material Design 3.0 Color System](https://m3.material.io/styles/color/overview)
- [WCAG 2.1 Accessibility Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)
- [Color Psychology in UI Design](https://www.interaction-design.org/literature/article/the-psychology-of-color-in-ux-design)

---

*财务管理系统Agent颜色标准化配置 v1.0.0*
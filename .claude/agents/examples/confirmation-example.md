# Agent确认机制使用示例

## 📋 实际使用流程示例

### 示例1：修复后端问题

**用户请求**：
```
请帮我修复登录API的性能问题，响应时间太慢了
```

**系统响应**：
```
🤖 **backend-fix-agent** 需求确认：

📋 **我的理解**：
• 核心目标：优化登录API性能，减少响应时间
• 涉及模块：用户认证服务、数据库查询、JWT生成
• 变更范围：AuthController, UserService, 数据库查询优化
• 预期结果：登录响应时间从当前2-3秒降低到500ms以内

🎯 **执行计划**：
1. 分析当前登录流程的性能瓶颈
2. 检查数据库查询执行计划和索引使用
3. 优化用户信息查询和权限加载逻辑
4. 缓存常用权限数据
5. 压测验证性能改进效果
✓ 验证：执行登录压测，确认响应时间目标

⚠️ **注意事项**：
• 数据库操作风险：查询优化可能影响其他功能
• API接口变更影响：确保前端兼容性
• 性能影响：缓存策略需要内存使用评估

✅ 确认执行？ (回复"确认"或"修改")
```

**用户确认**：
```
确认，但是请注意不要修改现有的API接口格式
```

**Agent执行**：
```
✅ 收到确认，开始执行后端性能优化...
注意：将保持现有API接口格式不变

[开始执行详细的修复流程]
```

### 示例2：前端组件修改

**用户请求**：
```
前端债权数据表格显示有问题，需要调整样式和排序功能
```

**系统响应**：
```
🤖 **frontend-fix-agent** 需求确认：

📋 **我的理解**：
• 核心目标：修复债权数据表格的显示和排序问题
• 涉及模块：DebtTable组件、样式文件、排序逻辑
• 变更范围：表格样式调整、排序功能增强
• 预期结果：表格正常显示且支持多列排序

🎯 **执行计划**：
1. 检查当前DebtTable组件的实现
2. 分析样式问题和排序功能缺陷
3. 修复表格显示样式
4. 实现或修复排序功能
5. 测试各种数据场景下的表格表现
✓ 验证：手动测试表格显示和排序功能

⚠️ **注意事项**：
• 组件修改范围：确保不影响其他使用该组件的页面
• 用户体验影响：保持现有的交互习惯
• 状态管理变更：排序状态的持久化处理

✅ 确认执行？ (回复"确认"或"修改")
```

### 示例3：自动触发无需确认

**场景**：用户在代码中遇到React错误

**系统行为**：
```
🔍 检测到前端React错误，自动启用frontend-fix-agent
[直接开始分析和修复，无需确认]
```

## 🎛️ 确认关键词处理

### 确认执行
- `确认` → 立即开始执行
- `同意` → 立即开始执行  
- `执行` → 立即开始执行
- `OK` → 立即开始执行

### 请求修改
- `修改` → 重新生成执行计划
- `调整` → 重新生成执行计划
- `不对` → 重新理解需求
- `取消` → 取消本次agent调用

### 请求澄清
- `解释` → 提供更详细的说明
- `为什么` → 解释技术选择原因
- `怎么` → 详细说明实现方法

## 🚫 确认超时处理

如果5分钟内用户未响应：
```
⏰ Agent调用超时，已自动取消
如需继续，请重新发起请求
```

## 🤔 主动询问机制示例

### 示例4：需求模糊时的主动询问

**用户请求**：
```
帮我优化一下系统性能
```

**Agent主动询问**：
```
🤔 **需求澄清**：
您的需求"优化系统性能"可能有几种理解方式：

🎯 **可能的理解**：
1. 优化数据库查询性能 (减少SQL执行时间)
2. 优化前端页面加载速度 (减少首屏时间)  
3. 优化API响应时间 (提升接口性能)
4. 优化服务器资源使用 (CPU/内存优化)

❓ **请明确**：您希望我执行哪种操作？或者您遇到了具体的性能问题？
```

### 示例5：信息缺失时的询问

**用户请求**：
```
请部署最新版本
```

**Agent主动询问**：
```
⚠️ **信息不足**：
执行"部署最新版本"需要以下关键信息：

📋 **缺少信息**：
• 部署目标环境 (开发/测试/生产)
• 部署范围 (前端/后端/全栈)
• 数据迁移需求 (是否有数据库变更)

❓ **请提供**：
1. 您要部署到哪个环境？
2. 需要部署哪些组件？
3. 是否需要数据库迁移？
```

### 示例6：多方案选择时的询问

**用户请求**：
```
前端表格性能太慢，需要优化
```

**Agent主动询问**：
```
🛠️ **方案选择**：
针对"前端表格性能优化"有多种可行方案：

📊 **方案对比**：
| 方案 | 优点 | 缺点 | 实施难度 |
|------|------|------|----------|
| 虚拟滚动 | 大数据量友好 | 复杂度高 | 中等 |
| 分页加载 | 简单易用 | 用户体验略差 | 低 |
| 数据预处理 | 加载速度快 | 内存占用高 | 低 |
| 懒加载 | 按需加载 | 首次仍慢 | 中等 |

❓ **您的偏好**：
1. 您更重视用户体验还是实施简单？
2. 表格通常显示多少行数据？
3. 用户是否需要快速浏览所有数据？
```

### 示例7：潜在副作用警告

**用户请求**：
```
修改用户认证逻辑，提升安全性
```

**Agent主动询问**：
```
⚠️ **潜在影响评估**：
执行"修改用户认证逻辑"可能产生以下影响：

🎯 **直接影响**：认证流程变更，安全性提升

⚠️ **潜在副作用**：
• 现有用户可能需要重新登录 - 影响程度：中等
• 第三方集成API可能需要调整 - 影响程度：高
• 移动端App可能需要更新 - 影响程度：中等

❓ **风险接受度**：
1. 您是否接受现有用户重新登录的影响？
2. 是否有第三方系统正在使用认证API？
3. 需要我制定渐进式升级方案吗？
```

## 💡 最佳实践

1. **清晰表达需求**：提供足够的上下文信息
2. **仔细阅读确认内容**：确保理解执行计划
3. **及时响应**：避免确认超时
4. **主动补充信息**：如有遗漏可在确认时补充
5. **欢迎Agent询问**：Agent询问说明它在认真思考，避免错误决策

## ⚙️ 配置自定义

可以在 `agent-orchestration.yml` 中调整：
- 确认超时时间
- 风险等级阈值  
- 特定agent的确认规则
- 确认模板格式
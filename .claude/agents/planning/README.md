# FinancialSystem规划类智能体协作指南

本目录包含用于项目规划和设计的智能体配置。经过优化重构，现在提供更清晰的职责划分和高效的协作流程。

## 📋 智能体列表

### 1. requirements-analyst.md (需求分析专家)
- **核心职责**: 需求收集、用户故事创建、验收标准定义、数据库结构查询
- **专长领域**: 业务需求分析、用户体验设计、数据模型分析
- **输出**: requirements.md (结构化需求文档)
- **触发条件**: 用户提出新功能需求、现有功能修改、性能优化需求

### 2. technical-design-agent.md (技术设计专家)  
- **核心职责**: 架构设计、API定义、技术选型、安全设计
- **专长领域**: 系统架构、接口设计、技术选型、性能优化
- **输出**: design.md (技术设计文档)
- **触发条件**: 需求文档完成、技术方案选择、架构重构

### 3. implementation-planner.md (实施规划专家)
- **核心职责**: 任务分解、工时估算、TDD指导、资源规划、风险管理
- **专长领域**: 项目管理、任务规划、时间估算、质量控制
- **输出**: tasks.md (可执行任务计划)
- **触发条件**: 技术设计完成、项目实施启动、任务规划需求

## 🔄 协作工作流程

### 标准协作链路
```mermaid
graph LR
    A[用户需求] --> B[requirements-analyst]
    B --> C[technical-design-agent]
    C --> D[implementation-planner]
    D --> E[开发执行]
    
    B -.-> F[requirements.md]
    C -.-> G[design.md]
    D -.-> H[tasks.md]
    
    I[review-agent] -.-> B
    I -.-> C
    I -.-> D
    
    style I fill:#e1f5fe
    style I stroke:#0277bd
```

### 集成review-agent的质量保障
- **requirements-analyst**: 条件性调用，分析现有功能实现
- **technical-design-agent**: 自动调用，评估现有架构问题
- **implementation-planner**: 自动调用，分析代码结构和质量

### 协作时序
1. **需求阶段**: requirements-analyst 分析用户需求，生成requirements.md
2. **设计阶段**: technical-design-agent 基于需求文档，创建design.md  
3. **规划阶段**: implementation-planner 基于设计文档，制定tasks.md
4. **执行阶段**: 开发团队按照任务计划实施

## 🔧 智能体触发指南

### 何时使用 requirements-analyst
```
✅ 用户说："我需要添加XX功能"
✅ 用户说："系统速度太慢，需要优化"
✅ 用户说："我们要修改XX模块"
❌ 已有明确技术设计文档
```

### 何时使用 technical-design-agent
```
✅ 已有requirements.md文档
✅ 用户询问："如何设计XX架构？"
✅ 需要API设计和技术选型
❌ 需求不明确，缺少需求文档
```

### 何时使用 implementation-planner
```
✅ 已有design.md文档
✅ 用户问："这个功能需要多长时间？"
✅ 需要任务分解和时间估算
❌ 技术设计不完整
```

## 📁 文档管理规范

### 输出文档位置
```
docs/engineering/
├── requirements/          # 需求文档
│   └── [feature-name]-requirements.md
├── design/               # 技术设计文档  
│   └── [feature-name]-design.md
└── planning/             # 实施计划文档
    └── [feature-name]-tasks.md
```

### 文档命名规范
- **需求文档**: `[功能名称]-requirements.md`
- **设计文档**: `[功能名称]-design.md`  
- **任务文档**: `[功能名称]-tasks.md`

## 🎯 最佳实践

### 智能体协作原则
1. **单一职责**: 每个智能体专注自己的核心职责
2. **顺序执行**: 按需求→设计→规划的顺序执行
3. **文档驱动**: 上游输出作为下游输入
4. **质量优先**: 每个阶段都有明确的质量标准
5. **代码质量保障**: 涉及现有代码时自动集成review-agent

### review-agent集成使用指南

#### 自动集成场景
```yaml
implementation-planner:
  触发: "涉及修改现有[模块]代码，先分析代码质量"
  价值: 确保任务分解考虑现有代码结构和质量问题
  
technical-design-agent:  
  触发: "设计需要集成现有[系统]，评估架构问题"
  价值: 基于现有代码问题制定更好的设计方案
  
requirements-analyst:
  触发: "修改现有[功能]，了解当前实现"
  价值: 基于现有实现质量评估需求可行性
```

#### 集成效果
- **提升代码质量**: 基于现有问题制定改进计划
- **降低技术债务**: 在规划阶段就考虑代码重构
- **更准确估算**: 基于代码复杂度提供更准确的工时估算

### 常见协作模式

#### 新功能开发
```
用户需求 → requirements-analyst → technical-design-agent → implementation-planner
```

#### 性能优化  
```
性能问题 → requirements-analyst → technical-design-agent → implementation-planner
```

#### 架构重构
```
重构需求 → requirements-analyst → technical-design-agent → implementation-planner
```

## 🔄 重构历史

### v2.0 优化内容 (最新)
- ✅ **合并冗余**: 将plan-agent.md合并到implementation-planner.md
- ✅ **职责清晰**: 重新定义每个智能体的核心职责
- ✅ **减少重复**: 消除了70%+的重复内容
- ✅ **流程优化**: 建立清晰的协作时序和触发条件

### 优化效果
- **文件减少**: 从4个优化为3个核心智能体
- **内容精简**: 代码行数减少约40%
- **职责明确**: 消除功能重叠和边界模糊
- **协作高效**: 建立标准化的协作流程

---

*💡 提示: 使用智能体前，请先确认前置条件是否满足，遵循标准协作流程以获得最佳效果。*
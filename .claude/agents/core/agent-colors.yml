# Agent颜色配置规范
# 基于Material Design 3.0和UX最佳实践的智能体颜色分配

agent_color_scheme:
  name: "FinancialSystem Agent Colors"
  version: "1.0.0"
  description: "财务管理系统Agent颜色标准化配置"
  
  # 🎨 核心颜色原则
  design_principles:
    color_psychology:
      - "蓝色系：专业、可信、稳定 (核心功能agent)"
      - "绿色系：安全、成功、创造 (正面操作agent)"
      - "橙色系：注意、活跃、修复 (需要关注的agent)"
      - "红色系：重要、警告、安全 (关键安全agent)"
      - "紫色系：智慧、创新、洞察 (分析智能agent)"
      - "灰色系：中性、稳定、工具 (辅助工具agent)"
    
    accessibility:
      - "对比度符合WCAG AA标准 (4.5:1)"
      - "色盲友好设计"
      - "深色/浅色主题兼容"
    
    visual_hierarchy:
      - "高频使用agent使用识别度高的颜色"
      - "危险操作agent使用警示色"
      - "工具类agent使用低饱和度颜色"

  # 🏷️ 功能分类配色
  category_colors:
    core_services:
      description: "核心服务类agent"
      color_family: "蓝色系"
      primary: "#1976D2"    # Material Blue 700
      secondary: "#2196F3"  # Material Blue 500
      usage: "核心功能、主要业务逻辑"
    
    development_tools:
      description: "开发工具类agent"
      color_family: "绿色系"
      primary: "#388E3C"    # Material Green 700
      secondary: "#4CAF50"  # Material Green 500
      usage: "代码开发、构建、测试"
    
    fix_repair:
      description: "修复维护类agent"
      color_family: "橙色系"
      primary: "#F57C00"    # Material Orange 700
      secondary: "#FF9800"  # Material Orange 500
      usage: "问题修复、Bug解决"
    
    security_critical:
      description: "安全关键类agent"
      color_family: "红色系"
      primary: "#D32F2F"    # Material Red 700
      secondary: "#F44336"  # Material Red 500
      usage: "安全防护、风险控制"
    
    analysis_intelligence:
      description: "分析智能类agent"
      color_family: "紫色系"
      primary: "#7B1FA2"    # Material Purple 700
      secondary: "#9C27B0"  # Material Purple 500
      usage: "代码分析、智能决策"
    
    utility_support:
      description: "工具支持类agent"
      color_family: "灰色系"
      primary: "#616161"    # Material Grey 700
      secondary: "#757575"  # Material Grey 600
      usage: "辅助工具、支持功能"

  # 📋 具体Agent颜色分配
  agent_assignments:
    # 🔍 搜索和查找类
    search-agent:
      color: "#1976D2"      # Material Blue 700
      category: "core_services"
      reason: "核心搜索功能，高频使用，需要高识别度"
      rgb: "25, 118, 210"
      hex_dark_theme: "#42A5F5"  # Material Blue 400
    
    # 🔧 修复类agent
    fix-agent:
      color: "#F57C00"      # Material Orange 700
      category: "fix_repair"
      reason: "通用修复agent，需要引起注意"
      rgb: "245, 124, 0"
      hex_dark_theme: "#FFA726"  # Material Orange 400
    
    backend-fix-agent:
      color: "#FF9800"      # Material Orange 500
      category: "fix_repair"
      reason: "后端修复，与fix-agent同色系但略浅"
      rgb: "255, 152, 0"
      hex_dark_theme: "#FFB74D"  # Material Orange 300
    
    frontend-fix-agent:
      color: "#FF8F00"      # Material Amber 700
      category: "fix_repair"
      reason: "前端修复，橙色系变种"
      rgb: "255, 143, 0"
      hex_dark_theme: "#FFCA28"  # Material Amber 400
    
    syntax-fix-agent:
      color: "#FF6F00"      # Material Orange 800
      category: "fix_repair"
      reason: "语法修复，橙色系深色"
      rgb: "255, 111, 0"
      hex_dark_theme: "#FF8A65"  # Material Deep Orange 300
    
    logic-fix-agent:
      color: "#E65100"      # Material Orange 900
      category: "fix_repair"
      reason: "逻辑修复，最深橙色"
      rgb: "230, 81, 0"
      hex_dark_theme: "#FF7043"  # Material Deep Orange 400
    
    integration-fix-agent:
      color: "#FB8C00"      # Material Orange 600
      category: "fix_repair"
      reason: "集成修复，中等橙色"
      rgb: "251, 140, 0"
      hex_dark_theme: "#FFB300"  # Material Amber 600
    
    # 🛡️ 安全防护类
    safety-agent:
      color: "#D32F2F"      # Material Red 700
      category: "security_critical"
      reason: "安全守护，使用警示红色"
      rgb: "211, 47, 47"
      hex_dark_theme: "#EF5350"  # Material Red 400
    
    # 🚀 部署运维类
    deploy-agent:
      color: "#388E3C"      # Material Green 700
      category: "development_tools"
      reason: "部署成功，使用绿色"
      rgb: "56, 142, 60"
      hex_dark_theme: "#66BB6A"  # Material Green 400
    
    # 📊 分析审查类
    review-agent:
      color: "#7B1FA2"      # Material Purple 700
      category: "analysis_intelligence"
      reason: "代码审查，智慧紫色"
      rgb: "123, 31, 162"
      hex_dark_theme: "#AB47BC"  # Material Purple 400
    
    document-agent:
      color: "#9C27B0"      # Material Purple 500
      category: "analysis_intelligence"
      reason: "文档生成，紫色系"
      rgb: "156, 39, 176"
      hex_dark_theme: "#BA68C8"  # Material Purple 300
    
    # 🧪 测试验证类
    test-agent:
      color: "#4CAF50"      # Material Green 500
      category: "development_tools"
      reason: "测试验证，绿色表示验证通过"
      rgb: "76, 175, 80"
      hex_dark_theme: "#81C784"  # Material Green 300
    
    # 📝 任务管理类
    todo-agent:
      color: "#616161"      # Material Grey 700
      category: "utility_support"
      reason: "任务管理，中性工具"
      rgb: "97, 97, 97"
      hex_dark_theme: "#BDBDBD"  # Material Grey 400
    
    snapshot-agent:
      color: "#757575"      # Material Grey 600
      category: "utility_support"
      reason: "快照管理，辅助工具"
      rgb: "117, 117, 117"
      hex_dark_theme: "#E0E0E0"  # Material Grey 300
    
    # 🎓 学习辅助类
    learning-agent:
      color: "#512DA8"      # Material Deep Purple 700
      category: "analysis_intelligence"
      reason: "学习指导，深紫色代表知识"
      rgb: "81, 45, 168"
      hex_dark_theme: "#7986CB"  # Material Indigo 300
    
    assistant-agent:
      color: "#303F9F"      # Material Indigo 700
      category: "analysis_intelligence"
      reason: "智能助手，靛蓝色代表智能"
      rgb: "48, 63, 159"
      hex_dark_theme: "#5C6BC0"  # Material Indigo 400
    
    # 📋 规划设计类
    requirements-analyst:
      color: "#1976D2"      # Material Blue 700
      category: "core_services"
      reason: "需求分析，核心业务功能"
      rgb: "25, 118, 210"
      hex_dark_theme: "#42A5F5"  # Material Blue 400
    
    technical-design-agent:
      color: "#1565C0"      # Material Blue 800
      category: "core_services"
      reason: "技术设计，深蓝色"
      rgb: "21, 101, 192"
      hex_dark_theme: "#2196F3"  # Material Blue 500
    
    implementation-planner:
      color: "#0D47A1"      # Material Blue 900
      category: "core_services"
      reason: "实施规划，最深蓝色"
      rgb: "13, 71, 161"
      hex_dark_theme: "#1E88E5"  # Material Blue 600

  # 🎯 特殊用途颜色
  special_purpose:
    urgent_attention:
      color: "#FF1744"      # Material Red A400
      description: "紧急关注color"
      usage: "系统故障、严重错误"
    
    success_completion:
      color: "#00C853"      # Material Green A700
      description: "成功完成color"
      usage: "任务完成、测试通过"
    
    warning_caution:
      color: "#FFC107"      # Material Amber 500
      description: "警告提醒color"
      usage: "需要注意、潜在问题"
    
    information_neutral:
      color: "#2196F3"      # Material Blue 500
      description: "信息展示color"
      usage: "常规信息、状态显示"

  # 📐 配色系统规范
  color_system_rules:
    consistency:
      - "同类功能agent使用同色系"
      - "重要程度通过饱和度区分"
      - "避免使用过于相似的颜色"
    
    hierarchy:
      - "核心agent使用高饱和度颜色"
      - "辅助agent使用中等饱和度"
      - "工具agent使用低饱和度"
    
    context_sensitivity:
      - "错误状态：红色系"
      - "成功状态：绿色系"
      - "警告状态：橙色/黄色系"
      - "信息状态：蓝色系"

  # 🌙 深色主题适配
  dark_theme_adaptations:
    principle: "深色主题下使用较亮的颜色变种"
    brightness_adjustment: "+200-300 (Material Color数值)"
    contrast_requirement: "保持4.5:1对比度"
    
    auto_mapping:
      "700": "400"    # 深色 -> 中亮色
      "800": "500"    # 更深 -> 标准色
      "900": "600"    # 最深 -> 较亮色

  # 🔧 实施指南
  implementation_guide:
    file_update_order:
      1. "更新核心agent配置文件"
      2. "更新专业化agent配置"
      3. "更新工具类agent配置"
      4. "验证颜色一致性"
    
    validation_checklist:
      - "所有agent都有明确颜色定义"
      - "同类agent颜色保持一致性"
      - "对比度符合无障碍标准"
      - "深色主题适配完整"
    
    maintenance:
      - "新增agent时参考此配色规范"
      - "定期检查颜色使用一致性"
      - "根据用户反馈调整颜色"

# 配色效果预览
color_preview:
  core_agents: "🔵🔵🔵 (蓝色系 - 专业稳定)"
  fix_agents: "🟠🟠🟠 (橙色系 - 警示修复)"
  security_agents: "🔴🔴🔴 (红色系 - 安全警告)"
  analysis_agents: "🟣🟣🟣 (紫色系 - 智慧分析)"
  utility_agents: "⚫⚫⚫ (灰色系 - 中性工具)"
  development_agents: "🟢🟢🟢 (绿色系 - 成功创造)"
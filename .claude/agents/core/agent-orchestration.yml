# Agent协作编排配置
# 财务管理系统专业化Agent协作机制

orchestration:
  name: "FinancialSystemAgentOrchestration"
  version: "1.1.0"
  description: "财务管理系统Agent智能协作编排"

# 🤝 Agent确认机制配置
confirmation_system:
  enabled: true
  description: "Agent调用前的需求确认机制"
  
  # 确认规则
  confirmation_rules:
    manual_invocation:
      description: "手动调用的agent需要确认"
      enabled: true
      triggers:
        - "用户直接@某个agent"
        - "用户明确指定使用某个agent"
        - "用户使用/agent命令"
        - "复杂任务需要agent协作"
      
      confirmation_template: |
        🤖 **{agent_name}** 需求确认：
        
        📋 **我的理解**：{understanding}
        
        🎯 **执行计划**：
        {execution_plan}
        
        ⚠️ **注意事项**：{warnings}
        
        ✅ 确认执行？ (回复"确认"或"修改")

    auto_invocation:
      description: "自动触发的agent无需确认"
      enabled: false
      triggers:
        - "基于关键词自动触发"
        - "基于文件模式自动触发"
        - "基于错误模式自动触发"
        - "系统内部协作调用"
      
      skip_confirmation: true
      log_action: true

  # Agent特定确认配置
  agent_specific_rules:
    syntax-fix-agent:
      confirmation_required: false
      risk_level: "low"
      auto_trigger: true
      confirmation_focus:
        - "语法错误类型"
        - "修复范围影响"
        - "编译验证结果"
      note: "语法错误修复通常安全，可自动执行"
    
    logic-fix-agent:
      confirmation_required: true
      risk_level: "high"
      confirmation_focus:
        - "业务逻辑变更影响"
        - "数据计算准确性"
        - "算法复杂度影响"
        - "业务规则一致性"
      note: "业务逻辑修复影响重大，需要确认"
    
    integration-fix-agent:
      confirmation_required: true
      risk_level: "medium"
      confirmation_focus:
        - "外部系统影响范围"
        - "数据同步安全性"
        - "网络配置变更"
        - "API调用频率"
      note: "集成修复可能影响外部系统，需要确认"

    backend-fix-agent:
      confirmation_required: true
      risk_level: "medium"
      confirmation_focus:
        - "数据库操作安全性"
        - "API接口变更影响"
        - "服务配置修改范围"
        - "业务逻辑变更点"
      path_validation:
        enabled: true
        allowed_paths:
          - "api-gateway/"
          - "services/"
          - "shared/"
          - "integrations/"
          - "src/main/java/"
          - "src/main/resources/"
        allowed_extensions:
          - ".java"
          - ".yml"
          - ".yaml"
          - ".xml"
          - ".sql"
          - ".properties"
        forbidden_paths:
          - "FinancialSystem-web/"
          - "*.js"
          - "*.jsx"
          - "*.tsx"
          - "*.css"
          - "package.json"
    
    frontend-fix-agent:
      confirmation_required: true
      risk_level: "low"
      confirmation_focus:
        - "组件修改范围"
        - "用户体验影响"
        - "状态管理变更"
        - "界面交互逻辑"
      path_validation:
        enabled: true
        allowed_paths:
          - "FinancialSystem-web/"
          - "src/"
          - "components/"
          - "pages/"
          - "hooks/"
          - "utils/"
        allowed_extensions:
          - ".js"
          - ".jsx"
          - ".ts"
          - ".tsx"
          - ".css"
          - ".scss"
          - ".json"
        forbidden_paths:
          - "api-gateway/"
          - "services/"
          - "*.java"
          - "*.yml"
          - "pom.xml"
    
    fix-agent:
      confirmation_required: true
      risk_level: "high"
      confirmation_focus:
        - "问题影响范围"
        - "修复策略选择"
        - "潜在副作用"
        - "回滚预案"
    
    deploy-agent:
      confirmation_required: true
      risk_level: "high"
      confirmation_focus:
        - "部署环境确认"
        - "影响范围评估"
        - "回滚策略"
        - "数据备份状态"
    
    safety-agent:
      confirmation_required: false
      risk_level: "protective"
      auto_activate: true
      note: "安全守护agent自动激活，无需确认"
    
    search-agent:
      confirmation_required: false
      risk_level: "minimal"
      auto_activate: true
      note: "搜索操作无副作用，无需确认"

  # 确认内容生成规则
  confirmation_content:
    understanding_generation:
      include:
        - "任务核心目标"
        - "涉及的系统模块"
        - "预期的变更范围"
        - "用户期望的结果"
      
      template: |
        • 核心目标：{core_objective}
        • 涉及模块：{affected_modules}
        • 变更范围：{change_scope}
        • 预期结果：{expected_outcome}

    execution_plan_generation:
      include:
        - "分步执行计划"
        - "关键检查点"
        - "质量验证步骤"
        - "完成标准"
      
      template: |
        1. {step1}
        2. {step2}
        3. {step3}
        ...
        ✓ 验证：{validation_steps}

    warnings_generation:
      check_for:
        - "数据库操作风险"
        - "生产环境影响"
        - "不可逆操作"
        - "第三方依赖影响"
        - "性能影响"
      
      template: |
        {high_risk_warnings}
        {medium_risk_warnings}
        {general_precautions}

# 确认流程控制
confirmation_workflow:
  timeout: 300  # 5分钟确认超时
  
  responses:
    confirmation_keywords:
      - "确认"
      - "同意"
      - "执行"
      - "开始"
      - "OK"
      - "yes"
      - "proceed"
    
    modification_keywords:
      - "修改"
      - "调整"
      - "不对"
      - "重新"
      - "取消"
      - "no"
      - "stop"
    
    clarification_keywords:
      - "解释"
      - "详细"
      - "为什么"
      - "怎么"
      - "说明"

  actions:
    on_confirmation:
      - "记录确认时间"
      - "启动agent执行"
      - "开始进度跟踪"
    
    on_modification:
      - "记录修改请求"
      - "重新生成确认内容"
      - "等待新的确认"
    
    on_timeout:
      - "记录超时事件"
      - "取消agent调用"
      - "通知用户超时"

# 🤔 主动询问机制 - 禁止猜测，必须确认
inquiry_system:
  enabled: true
  description: "Agent遇到不确定因素时主动向用户询问，严禁猜测"
  
  # 核心原则
  core_principles:
    - "疑则问，不猜测"
    - "宁可多问，不可错做"
    - "保持谦逊，承认不确定"
    - "提供选项，让用户决策"
  
  # 必须询问的情况
  mandatory_inquiry_triggers:
    ambiguous_requirements:
      description: "需求表述模糊或可能有多种理解"
      examples:
        - "修复这个问题"(未明确指出具体问题)
        - "优化性能"(未指定优化目标和范围)
        - "调整样式"(未说明具体调整方向)
        - "更新数据"(未指定更新内容和范围)
      
      inquiry_template: |
        🤔 **需求澄清**：
        您的需求"{user_request}"可能有几种理解方式：
        
        🎯 **可能的理解**：
        1. {interpretation_1}
        2. {interpretation_2}
        3. {interpretation_3}
        
        ❓ **请明确**：您希望我执行哪种操作？

    missing_critical_info:
      description: "缺少执行任务的关键信息"
      examples:
        - "部署环境未指定(开发/测试/生产)"
        - "操作范围未明确(单个文件/整个模块/系统级)"
        - "数据处理方式未说明(备份/直接修改)"
        - "权限级别不清楚"
      
      inquiry_template: |
        ⚠️ **信息不足**：
        执行"{task_name}"需要以下关键信息：
        
        📋 **缺少信息**：
        • {missing_info_1}
        • {missing_info_2}
        • {missing_info_3}
        
        ❓ **请提供**：{specific_questions}

    multiple_valid_approaches:
      description: "存在多种有效的实现方案"
      examples:
        - "数据库查询优化方案选择"
        - "UI组件实现方式选择"
        - "架构设计方案选择"
        - "部署策略选择"
      
      inquiry_template: |
        🛠️ **方案选择**：
        针对"{task_name}"有多种可行方案：
        
        📊 **方案对比**：
        {approach_comparison_table}
        
        ❓ **您的偏好**：请选择方案或指明优先考虑的因素

    potential_side_effects:
      description: "操作可能产生不可预见的副作用"
      examples:
        - "数据库结构修改可能影响其他功能"
        - "API变更可能影响前端或第三方集成"
        - "性能优化可能影响内存使用"
        - "安全策略调整可能影响用户体验"
      
      inquiry_template: |
        ⚠️ **潜在影响评估**：
        执行"{task_name}"可能产生以下影响：
        
        🎯 **直接影响**：{direct_effects}
        
        ⚠️ **潜在副作用**：
        • {side_effect_1} - 影响程度：{impact_level_1}
        • {side_effect_2} - 影响程度：{impact_level_2}
        
        ❓ **风险接受度**：您是否接受这些潜在影响？需要我采取额外防护措施吗？

    configuration_decisions:
      description: "需要配置参数或策略决策"
      examples:
        - "缓存过期时间设置"
        - "数据库连接池大小"
        - "日志级别选择"
        - "权限策略配置"
      
      inquiry_template: |
        ⚙️ **配置决策**：
        "{task_name}"需要以下配置决策：
        
        🎛️ **配置项**：
        {configuration_items}
        
        💡 **建议值**：{recommended_values}
        
        ❓ **您的选择**：请确认配置值或告知您的特殊需求

  # 询问响应处理
  inquiry_response_handling:
    clear_answer:
      action: "更新任务理解，继续确认流程"
      
    partial_answer:
      action: "针对未明确部分继续询问"
      
    request_recommendation:
      action: "提供专业建议，等待用户决策"
      
    defer_decision:
      action: "提供默认安全方案，等待后续调整"

  # 询问质量标准
  inquiry_quality_standards:
    clarity:
      - "问题表述清晰具体"
      - "避免技术术语过多"
      - "提供充分的上下文"
    
    completeness:
      - "一次性询问所有不确定点"
      - "避免反复询问同类问题"
      - "提供必要的背景信息"
    
    actionability:
      - "提供具体的选择选项"
      - "说明每个选项的利弊"
      - "给出明确的回答格式"

  # 特殊情况处理
  special_cases:
    urgent_tasks:
      description: "紧急任务的询问简化"
      strategy: "合并询问，提供快速决策选项"
      
    routine_operations:
      description: "常规操作的询问优化"
      strategy: "使用默认安全配置，事后确认"
      
    experimental_features:
      description: "实验性功能开发"
      strategy: "详细询问，充分告知风险"

# 询问优先级管理
inquiry_priority:
  critical:
    description: "关键决策，必须明确"
    examples:
      - "数据库修改操作"
      - "生产环境部署"
      - "安全配置变更"
    response_required: true
    
  important:
    description: "重要决策，建议明确"
    examples:
      - "性能优化策略"
      - "UI交互设计"
      - "API接口设计"
    fallback_allowed: true
    
  optional:
    description: "可选决策，可使用默认值"
    examples:
      - "代码格式偏好"
      - "注释详细程度"
      - "变量命名风格"
    auto_decide: true

# 智能触发规则配置
triggers:
  # 专业化Agent自动触发规则
  syntax_fix_triggers:
    description: "语法错误自动触发syntax-fix-agent"
    patterns:
      keywords:
        - "SyntaxError"
        - "TypeError"
        - "CompilationError"
        - "ParseError"
        - "语法错误"
        - "编译错误"
        - "类型错误"
        - "Unexpected token"
        - "Missing semicolon"
        - "Unclosed bracket"
        - "Invalid syntax"
        - "cannot find symbol"
        - "javac compilation failed"
        - "npm run build failed"
        - "ESLint errors"
        - "TypeScript compilation errors"
      
      error_patterns:
        - "expected ';' before"
        - "unexpected token"
        - "missing closing bracket"
        - "invalid character"
        - "undefined variable"
        
    agent: "syntax-fix-agent"
    priority: "high"
    auto_trigger: true

  logic_fix_triggers:
    description: "业务逻辑错误自动触发logic-fix-agent"
    patterns:
      keywords:
        - "业务逻辑错误"
        - "计算结果不对"
        - "数据不一致"
        - "逻辑错误"
        - "算法问题"
        - "流程异常"
        - "规则冲突"
        - "状态错误"
        - "金额计算错误"
        - "精度丢失"
        - "除零错误"
        - "数据不平衡"
        - "一致性检查失败"
        - "数据验证失败"
        
      business_patterns:
        - "期初.*期末.*不一致"
        - "债权.*计算.*错误"
        - "BigDecimal"
        - "MathContext"
        - "状态流转错误"
        - "权限检查失败"
        
    agent: "logic-fix-agent"
    priority: "high"
    auto_trigger: true

  integration_fix_triggers:
    description: "系统集成问题自动触发integration-fix-agent"
    patterns:
      keywords:
        - "API调用失败"
        - "接口超时"
        - "连接失败"
        - "集成问题"
        - "第三方服务"
        - "外部系统"
        - "数据同步"
        - "网络错误"
        - "财政系统"
        - "OA系统"
        - "金蝶"
        - "ERP"
        
      http_errors:
        - "ConnectTimeoutException"
        - "SocketTimeoutException"
        - "HttpClientException"
        - "RestClientException"
        - "ConnectionRefusedException"
        - "UnknownHostException"
        - "SSLHandshakeException"
        - "401 Unauthorized"
        - "403 Forbidden"
        - "404 Not Found"
        - "500 Internal Server Error"
        - "502 Bad Gateway"
        - "503 Service Unavailable"
        - "504 Gateway Timeout"
        
    agent: "integration-fix-agent"
    priority: "high"
    auto_trigger: true

  backend_triggers:
    description: "后端相关问题自动触发backend-fix-agent"
    patterns:
      keywords:
        - "后端"
        - "API"
        - "数据库" 
        - "Spring"
        - "Java"
        - "微服务"
        - "认证"
        - "JWT"
        - "服务"
        - "接口"
        - "SQL"
        - "JPA"
        - "Maven"
        - "配置"
      
      file_patterns:
        - "*.java"
        - "*.yml"
        - "*.yaml"
        - "*.sql"
        - "pom.xml"
        - "application.*"
        - "bootstrap.*"
      
      path_patterns:
        - "api-gateway/"
        - "services/"
        - "shared/"
        - "integrations/"
        - "src/main/java/"
        - "src/main/resources/"
      
      error_patterns:
        - "SQLException"
        - "JPA"
        - "ConnectionException"
        - "AuthenticationException"
        - "NullPointerException"
        - "IllegalArgumentException"
        - "DataAccessException"
        - "RestClientException"
    
    agent: "backend-fix-agent"
    priority: "high"

  frontend_triggers:
    description: "前端相关问题自动触发frontend-fix-agent"
    patterns:
      keywords:
        - "前端"
        - "React"
        - "组件"
        - "界面"
        - "UI"
        - "图表"
        - "Material-UI"
        - "页面"
        - "样式"
        - "CSS"
        - "JavaScript"
        - "TypeScript"
        - "Hook"
        - "状态"
        - "渲染"
      
      file_patterns:
        - "*.js"
        - "*.jsx"
        - "*.ts"
        - "*.tsx"
        - "*.css"
        - "*.scss"
        - "*.less"
        - "package.json"
        - "package-lock.json"
        - "yarn.lock"
      
      path_patterns:
        - "FinancialSystem-web/"
        - "src/"
        - "components/"
        - "pages/"
        - "hooks/"
        - "utils/"
        - "assets/"
        - "public/"
      
      error_patterns:
        - "TypeError"
        - "ReferenceError"
        - "React"
        - "Material-UI"
        - "Chart"
        - "useState"
        - "useEffect"
        - "Component"
    
    agent: "frontend-fix-agent"
    priority: "high"

# Agent协作工作流
workflows:
  simple_fix:
    description: "单一技术栈问题修复"
    steps:
      - name: "问题识别"
        action: "analyze_problem"
        agent: "fix-agent"
      
      - name: "路由到专业agent"
        action: "route_to_specialist"
        condition: "trigger_match"
        
      - name: "专业修复"
        action: "fix_issue"
        agent: "specialized_agent"
        
      - name: "验证修复"
        action: "validate_fix"
        agent: "specialized_agent"

  complex_fix:
    description: "跨技术栈问题修复"
    steps:
      - name: "问题分析"
        action: "deep_analysis"
        agent: "fix-agent"
        
      - name: "后端问题处理"
        action: "backend_fix"
        agent: "backend-fix-agent"
        condition: "has_backend_issue"
        
      - name: "前端问题处理"
        action: "frontend_fix"
        agent: "frontend-fix-agent"
        condition: "has_frontend_issue"
        
      - name: "集成测试"
        action: "integration_test"
        agent: "fix-agent"
        
      - name: "端到端验证"
        action: "e2e_validation"
        agent: "test-agent"

  data_flow_fix:
    description: "数据流问题修复"
    steps:
      - name: "数据流分析"
        action: "analyze_data_flow"
        agent: "fix-agent"
        
      - name: "后端数据验证"
        action: "validate_backend_data"
        agent: "backend-fix-agent"
        
      - name: "API接口验证"
        action: "validate_api"
        agent: "backend-fix-agent"
        
      - name: "前端数据处理验证"
        action: "validate_frontend_data"
        agent: "frontend-fix-agent"
        
      - name: "数据展示验证"
        action: "validate_display"
        agent: "frontend-fix-agent"

# Agent职责边界定义
responsibilities:
  backend_exclusive:
    description: "后端专属职责"
    areas:
      - "数据库设计和查询优化"
      - "微服务架构和配置"
      - "外部系统集成"
      - "业务逻辑算法"
      - "API安全和性能"
      - "数据一致性验证"
      - "服务监控和日志"
    
    technologies:
      - "Spring Boot"
      - "Spring Security"
      - "Spring Data JPA"
      - "MySQL"
      - "Maven"
      - "Java"

  frontend_exclusive:
    description: "前端专属职责"
    areas:
      - "组件设计和交互"
      - "状态管理和数据流"
      - "用户界面和体验"
      - "图表和可视化"
      - "响应式和兼容性"
      - "前端性能优化"
      - "用户交互逻辑"
    
    technologies:
      - "React"
      - "Material-UI"
      - "Chart.js"
      - "Recharts"
      - "JavaScript/TypeScript"
      - "CSS/SCSS"

  shared_responsibilities:
    description: "共享职责"
    areas:
      - "API接口设计"
      - "数据格式定义"
      - "错误处理机制"
      - "性能优化"
      - "端到端测试"
      - "用户体验流程"
      - "系统集成测试"

# 协作规则
collaboration_rules:
  escalation:
    description: "问题升级规则"
    conditions:
      - "专业agent无法独立解决"
      - "问题涉及多个技术栈"
      - "需要架构级别调整"
      - "影响系统核心功能"
    
    action: "escalate_to_fix_agent"

  handoff:
    description: "Agent交接规则"
    scenarios:
      backend_to_frontend:
        trigger: "API修复完成，需要前端配合调整"
        data: ["API变更详情", "新接口文档", "错误码更新"]
        
      frontend_to_backend:
        trigger: "前端发现后端数据问题"
        data: ["错误现象描述", "期望数据格式", "复现步骤"]
        
      both_to_integration:
        trigger: "前后端都修复完成，需要集成测试"
        data: ["修复内容总结", "测试要点", "验收标准"]

# 质量保证
quality_assurance:
  validation_checklist:
    - "功能完整性验证"
    - "数据一致性检查"
    - "性能影响评估"
    - "安全性审查"
    - "用户体验测试"
    - "回归测试"
    - "文档更新确认"

  success_criteria:
    - "问题根本原因解决"
    - "无新增问题引入"
    - "符合系统架构原则"
    - "代码质量标准达标"
    - "测试覆盖率满足要求"

# 监控和优化
monitoring:
  metrics:
    - "问题解决时间"
    - "修复成功率"
    - "问题复发率"
    - "Agent协作效率"
    - "用户满意度"

  optimization:
    - "定期评估触发规则准确性"
    - "优化Agent协作流程"
    - "完善问题分类机制"
    - "提升专业能力覆盖"
    - "增强预防性措施"
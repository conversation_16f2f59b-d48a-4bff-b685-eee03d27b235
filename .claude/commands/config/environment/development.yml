# SuperClaude 开发环境配置
# Development Environment Configuration
# Version: v2.0.1

environment:
  name: "development"
  description: "开发环境配置 - 宽松的安全策略，详细的调试信息"

# 开发环境特定设置
development:
  # 调试模式
  debug:
    enabled: true
    verbose_logging: true
    trace_commands: true
    show_internal_errors: true
    
  # 验证级别
  validation:
    level: "standard"  # relaxed, standard, strict
    skip_non_critical: true
    allow_experimental: true
    
  # 自动备份
  backup:
    enabled: false
    auto_snapshot: false
    
  # 性能优化
  performance:
    token_budget: 50000  # 开发环境较高的token预算
    cache_aggressively: true
    parallel_operations: true
    
# 开发者友好功能
developer_features:
  # 自动建议
  suggestions:
    enabled: true
    show_examples: true
    recommend_optimizations: true
    
  # 快速原型
  prototyping:
    allow_dirty_commits: true
    skip_tests_flag: true
    rapid_iteration: true
    
  # 学习模式
  learning:
    explain_decisions: true
    show_alternative_approaches: true
    educational_output: true

# 工具集成
integrations:
  # 开发工具
  dev_tools:
    hot_reload: true
    auto_format: true
    lint_on_save: false
    
  # 测试工具
  testing:
    run_unit_tests: true
    coverage_threshold: 70  # 开发环境较低的覆盖率要求
    allow_skipped_tests: true
    
  # 监控工具
  monitoring:
    detailed_metrics: true
    performance_profiling: false
    resource_tracking: false

# 安全设置
security:
  # 开发环境安全策略
  policy: "permissive"
  
  # 危险操作
  dangerous_operations:
    require_confirmation: false
    allow_force_flags: true
    
  # 审计
  audit:
    enabled: false
    log_level: "WARN"
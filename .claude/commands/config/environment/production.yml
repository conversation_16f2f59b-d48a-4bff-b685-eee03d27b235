# SuperClaude 生产环境配置  
# Production Environment Configuration
# Version: v2.0.1

environment:
  name: "production"
  description: "生产环境配置 - 最严格的安全策略，最小化风险"

# 生产环境特定设置
production:
  # 调试模式
  debug:
    enabled: false
    verbose_logging: false
    trace_commands: false
    show_internal_errors: false
    
  # 验证级别
  validation:
    level: "paranoid"  # standard, strict, paranoid
    skip_non_critical: false
    allow_experimental: false
    mandatory_review: true
    
  # 强制备份
  backup:
    enabled: true
    auto_snapshot: true
    pre_operation_backup: true
    retention_days: 30
    
  # 性能优化
  performance:
    token_budget: 20000  # 生产环境较低的token预算
    cache_aggressively: false
    parallel_operations: false
    
# 安全策略
security:
  # 生产环境安全策略
  policy: "zero_trust"
  
  # 必需的标志
  mandatory_flags:
    deploy: ["--validate", "--plan", "--rollback-plan", "--monitoring"]
    migrate: ["--backup", "--validate", "--dry-run", "--rollback"]
    cleanup: ["--dry-run", "--selective", "--backup"]
    
  # 禁止的操作
  forbidden_operations:
    - "--force"
    - "--skip-validation"  
    - "--no-backup"
    - "--experimental"
    
  # 危险操作
  dangerous_operations:
    require_confirmation: true
    require_approval: true
    approval_timeout: 300  # 5分钟
    allow_force_flags: false
    multi_person_approval: true
    
  # 审计
  audit:
    enabled: true
    log_level: "INFO"
    comprehensive_logging: true
    real_time_monitoring: true

# 监控和告警
monitoring:
  # 实时监控
  real_time:
    enabled: true
    metrics_collection: true
    performance_tracking: true
    error_tracking: true
    
  # 告警设置
  alerts:
    enabled: true
    email_notifications: true
    slack_notifications: true
    escalation_enabled: true
    
  # 健康检查
  health_checks:
    enabled: true
    interval: 60  # 秒
    timeout: 30   # 秒
    retry_count: 3

# 部署策略
deployment:
  # 部署方式
  strategy: "blue_green"  # canary, blue_green, rolling
  
  # 金丝雀部署设置
  canary:
    initial_percentage: 1
    increment_percentage: 5
    validation_time: 300  # 5分钟
    auto_promote: false
    
  # 回滚策略
  rollback:
    auto_rollback: true
    rollback_triggers:
      - error_rate_threshold: 1  # 1%
      - response_time_threshold: 5000  # 5秒
      - health_check_failures: 3
    rollback_timeout: 600  # 10分钟

# 合规性
compliance:
  # 数据保护
  data_protection:
    encryption_required: true
    backup_encryption: true
    audit_trail: true
    
  # 访问控制
  access_control:
    multi_factor_auth: true
    role_based_access: true
    session_timeout: 1800  # 30分钟
    
  # 审计要求
  audit_requirements:
    all_operations: true
    change_tracking: true
    approval_records: true
    compliance_reporting: true
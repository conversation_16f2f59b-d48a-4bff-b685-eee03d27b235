# 财务系统常量与配置
# Project-specific constants for FinancialSystem debt management application

## Finance_System_Core
```yaml
System_Identity:
  Name: "FinancialSystem"
  Type: "企业财务债权管理系统"
  Architecture: "Microservices"
  Technology_Stack: "Spring Boot 3.1.12 + React 18 + MySQL 8.0"
  
Business_Domains:
  Primary: "债权管理"  # Debt Management
  Secondary: "财务报表"  # Financial Reporting
  Data_Export: "数据导出中心"  # Data Export Center
  Monitoring: "数据监控"  # Data Monitoring
```

## Debt_Management_Constants
```yaml
Debt_Types:
  Litigation: "诉讼债权"
  Non_Litigation: "非诉讼债权"
  Impairment: "减值准备"
  
Debt_Status:
  Active: "正常"
  Overdue: "逾期"
  Disposed: "已处置"
  Recovered: "已收回"
  Written_Off: "已核销"
  
Debt_Operations:
  Add: "新增债权"
  Update: "更新债权"
  Delete: "删除债权"
  Convert: "诉讼转换"
  Export: "导出数据"
  Search: "查询债权"
  
Processing_Status:
  Pending: "待处理"
  In_Progress: "处理中"
  Completed: "已完成"
  Failed: "失败"
  Cancelled: "已取消"
```

## Finance_System_Paths
```yaml
Project_Structure:
  API_Gateway: "api-gateway/"
  Services: "services/"
  Shared: "shared/"
  Frontend: "FinancialSystem-web/"
  Documentation: "docs/"
  Scripts: "scripts/"
  
Service_Modules:
  Debt_Management: "services/debt-management/"
  Asset_Management: "services/asset-management/"
  Data_Maintenance: "services/data-maintenance/"
  
Shared_Components:
  Common: "shared/common/"
  Data_Access: "shared/data-access/"
  Data_Processing: "shared/data-processing/"
  
Database_Scripts:
  Init: "sql/init/"
  Migration: "scripts/database/"
  Backup: "scripts/database/backup/"
```

## Database_Configuration
```yaml
Database_Names:
  Overdue_Debt: "overdue_debt_db"
  User_System: "user_system"
  Kingdee: "kingdee"
  
Table_Prefixes:
  Litigation: "litigation_claim"
  Non_Litigation: "non_litigation_claim"
  Impairment: "impairment_reserve"
  Users: "users"
  Roles: "roles"
  
Data_Sources:
  Primary: "@DataSource(\"primary\")"
  Secondary: "@DataSource(\"secondary\")"
  Kingdee: "@DataSource(\"kingdee\")"
```

## Business_Priority_Levels
```yaml
Debt_Priority:
  Critical: "critical"    # 重大债权
  High: "high"           # 高优先级
  Medium: "medium"       # 中等优先级
  Low: "low"            # 低优先级
  
Amount_Thresholds:
  Large_Amount: "大额债权"     # > 1000万
  Medium_Amount: "中等金额"    # 100万-1000万
  Small_Amount: "小额债权"     # < 100万
  
Risk_Levels:
  High_Risk: "高风险"
  Medium_Risk: "中等风险"
  Low_Risk: "低风险"
  Minimal_Risk: "极低风险"
```

## Report_Templates
```yaml
Excel_Reports:
  Debt_Summary: "债权汇总表"
  Recovery_Statistics: "回收统计表"
  Aging_Analysis: "账龄分析表"
  Risk_Assessment: "风险评估表"
  
Report_Formats:
  Daily_Report: "日报-{YYYY-MM-DD}"
  Weekly_Report: "周报-{YYYY-WW}"
  Monthly_Report: "月报-{YYYY-MM}"
  Quarterly_Report: "季报-{YYYY-QQ}"
  Annual_Report: "年报-{YYYY}"
  
Export_File_Names:
  Overdue_Debt_Export: "逾期债权导出-{timestamp}.xlsx"
  Litigation_Claims: "诉讼债权-{date}.xlsx"
  Non_Litigation_Claims: "非诉讼债权-{date}.xlsx"
  Recovery_Report: "回收情况报告-{period}.xlsx"
```

## System_Environment
```yaml
Environment_Types:
  Local: "local"         # 本地开发环境
  Development: "dev"     # 开发环境
  Testing: "test"        # 测试环境
  Staging: "staging"     # 预生产环境
  Production: "prod"     # 生产环境
  
Deployment_Types:
  Local_Direct: "直接启动"    # 本地直接启动，不使用docker
  Docker_Compose: "容器化"   # 使用docker-compose启动
  
Server_Ports:
  API_Gateway: "8080"
  Frontend: "3000"
  Database: "3306"
  Redis: "6379"
```

## Finance_Data_Validation
```yaml
Required_Fields:
  Debtor_Name: "债务人名称"
  Creditor_Name: "债权人名称"
  Debt_Amount: "债权金额"
  Overdue_Date: "逾期日期"
  
Data_Formats:
  Currency_Format: "￥#,##0.00"
  Date_Format: "YYYY-MM-DD"
  Percentage_Format: "#0.00%"
  
Validation_Rules:
  Amount_Range: "金额必须大于0"
  Date_Range: "日期不能超过当前日期"
  Name_Length: "名称长度不能超过200字符"
```

## API_Endpoints
```yaml
Authentication:
  Login: "/api/auth/login"
  Logout: "/api/auth/logout"
  Password_Reset: "/api/auth/reset-password"
  
Debt_Management:
  Get_Debts: "/api/debt/query"
  Add_Debt: "/api/debt/add"
  Update_Debt: "/api/debt/update"
  Delete_Debt: "/api/debt/delete"
  Convert_Debt: "/api/debt/convert"
  
Data_Export:
  Export_Excel: "/api/export/excel"
  Export_Status: "/api/export/status"
  Download_File: "/api/export/download"
  
System_Monitoring:
  Health_Check: "/api/health"
  Data_Consistency: "/api/monitor/consistency"
  System_Status: "/api/monitor/status"
```

## Error_Messages
```yaml
Business_Errors:
  Debt_Not_Found: "债权记录不存在"
  Duplicate_Debt: "债权记录重复"
  Invalid_Amount: "债权金额无效"
  Conversion_Failed: "诉讼转换失败"
  Export_Failed: "数据导出失败"
  
System_Errors:
  Database_Connection: "数据库连接失败"
  Permission_Denied: "权限不足"
  File_Not_Found: "文件不存在"
  Service_Unavailable: "服务不可用"
  
Success_Messages:
  Debt_Added: "债权添加成功"
  Debt_Updated: "债权更新成功"
  Debt_Deleted: "债权删除成功"
  Export_Complete: "数据导出完成"
  Conversion_Success: "诉讼转换成功"
```

## Performance_Thresholds
```yaml
Query_Limits:
  Max_Records_Per_Page: 1000
  Default_Page_Size: 50
  Large_Dataset_Threshold: 10000
  
Export_Limits:
  Max_Export_Records: 50000
  Excel_Row_Limit: 1048576
  CSV_Size_Limit: "100MB"
  
Timeout_Settings:
  Query_Timeout: "30s"
  Export_Timeout: "300s"
  Report_Generation: "180s"
  Database_Connection: "10s"
```

## File_Extensions
```yaml
Supported_Formats:
  Excel: [".xlsx", ".xls"]
  Document: [".pdf", ".doc", ".docx"]
  Data: [".csv", ".json", ".xml"]
  Image: [".jpg", ".jpeg", ".png", ".gif"]
  
Configuration_Files:
  Spring_Config: [".yml", ".yaml", ".properties"]
  Database_Scripts: [".sql"]
  Documentation: [".md"]
```

---
*财务系统常量 v1.0 - 专为企业债权管理定制的配置常量*
---
# Documentation Patterns
# Unified docs standards, directory structure, format requirements

## Legend
@include universal-constants.yml#Universal_Legend

Directory_Standards:
  Documentation_Structure:
    Claude_Internal: ".claudedocs/"
    Project_Documentation: "docs/"

    Claude_Subdirectories:
      Reports: ".claudedocs/reports/"
      Metrics: ".claudedocs/metrics/"
      Summaries: ".claudedocs/summaries/"
      Checkpoints: ".claudedocs/checkpoints/"
      Validation: ".claudedocs/validation/"
      Audit: ".claudedocs/audit/"
      Incidents: ".claudedocs/incidents/"
      Tasks: ".claudedocs/tasks/"
      Task_States:
        - ".claudedocs/tasks/pending/"
        - ".claudedocs/tasks/in-progress/"
        - ".claudedocs/tasks/completed/"
        - ".claudedocs/tasks/cancelled/"

    Project_Subdirectories:
    API_Docs: "docs/api/"
    User_Docs: "docs/user/"
    Developer_Docs: "docs/dev/"
    Architecture: "docs/architecture/"
    Guides: "docs/guides/"
    References: "docs/references/"

  Auto_Creation_Rules:
    Create_On_Demand: "mkdir -p before writing files"
    Validate_Permissions: "Check write access before operations"
    Ignore_Patterns: "Add to .gitignore if not already present"
    Permissions: "755 for dirs, 644 for files"
```

## Naming Conventions

```yaml
File_Naming:
  Reports: "<command>-<type>-<timestamp>.md"
  Metrics: "<metric>-<date>.md|html|json"
  Audit: "audit-<YYYY-MM-DD>.log"
  Tasks: "{type}-{id}-{slug}.md"

Examples:
  Analysis_Report: "analysis-security-20240115-143022.md"
  Coverage_Report: "coverage-20240115.html"
  Audit_Log: "audit-2024-01-15.log"
  RCA_Report: "rca-api-timeout-20240115-143022.md"
  Task_File: "feature-001-auth-system.md"

Task_ID_Format: "YYYYMMDD-HHMMSS"
Task_Types: ["feature", "bugfix", "refactor", "docs", "test"]

Git_Branches:
  Task_Branch: "task/{id}-{slug}"
  Feature_Branch: "feature/{name}"
  Bugfix_Branch: "bugfix/{name}"
  Release_Branch: "release/{version}"
```

## Format Requirements

```yaml
Document_Standards:
  Markdown_Format:
    Headers: "Use ## for main sections, ### for subsections"
    Code_Blocks: "Always specify language for syntax highlighting"
    Tables: "Use pipe format w/ header separators"
    Links: "Use descriptive text, avoid raw URLs"

  Report_Templates:
    Analysis_Report:
      Structure: "Executive Summary → Findings → Recommendations → Appendix"
      Length: "2-5 pages for standard analysis"
      Format: "Markdown w/ embedded metrics"

    Performance_Report:
      Structure: "Metrics Overview → Trend Analysis → Issues → Actions"
      Charts: "ASCII charts for CLI compatibility"
      Data_Format: "JSON for machine processing, Markdown for human reading"

    Security_Report:
      Structure: "Risk Summary → Vulnerabilities → Mitigations → Verification"
      Risk_Levels: "CRITICAL|HIGH|MEDIUM|LOW w/ CVSS scores"
      Remediation: "Actionable steps w/ priority order"

  Required_Headers:
    Generated_By: "SuperClaude v2"
    Command: "/<command> [flags]"
    Timestamp: "ISO 8601 format"
    Duration: "Operation time"

  UltraCompressed_Mode:
    Token_Reduction: "Substantial reduction from standard format"
    Symbols_Required: "→ & @ ∀ ∃ ∴ ∵ based on legend"
    Structure: "YAML > Tables > Lists > Prose"
    Legend_Requirement: "Auto-generate symbol/abbreviation legend"
```

## Output Notifications

```yaml
Standard_Notifications:
  File_Operations:
    Created: "📝 Created: {file_path}"
    Updated: "✏ Updated: {file_path}"
    Deleted: "🗑 Deleted: {file_path}"
    Moved: "➡ Moved: {old_path} → {new_path}"

  Report_Generation:
    Analysis_Complete: "📄 Analysis report saved to: {path}"
    Metrics_Updated: "📊 Metrics updated: {path}"
    Summary_Generated: "📋 Summary saved to: {path}"
    Checkpoint_Created: "💾 Checkpoint created: {path}"
    Documentation_Created: "📚 Documentation created: {path}"
    Directory_Created: "📁 Created directory: {path}"

  Process_Status:
    Started: "▶ Starting {operation}"
    In_Progress: "🔄 Processing {operation} ({progress}%)"
    Completed: "✅ {operation} completed successfully"
    Failed: "❌ {operation} failed: {reason}"
    Warning: "⚠ {operation} completed w/ warnings"

  Performance_Alerts:
    Slow_Operation: "⚠ Operation taking longer than expected ({duration}s)"
    High_Memory: "⚠ High memory usage detected ({usage}MB)"
    Large_Output: "⚠ Large output generated ({size}MB)"

Quality_Standards:
  Consistency: "All notifications follow emoji + message format"
  Actionability: "Include file paths & next steps where applicable"
  Brevity: "Keep messages under 80 characters when possible"
  Context: "Include relevant details (size, duration, progress)"
```

## Command Integration

```yaml
Command_To_Directory_Mapping:
  analyze: ".claudedocs/reports/analysis-*.md"
  scan: ".claudedocs/reports/scan-*.md"
  test: ".claudedocs/metrics/coverage-*.html"
  improve: ".claudedocs/metrics/quality-*.md"
  troubleshoot: ".claudedocs/incidents/rca-*.md"
  estimate: ".claudedocs/summaries/estimate-*.md"
  document: "docs/[category]/*.md"
  git: ".claudedocs/audit/git-operations-*.log"
  deploy: ".claudedocs/audit/deployment-*.log"

Directory_Creation_Logic:
  Pre_Write_Check:
    - "Verify parent directory exists"
    - "Create if missing with proper permissions"
    - "Validate write access"
    - "Handle errors gracefully"

  Auto_Create_Paths:
    - ".claudedocs/ and all subdirectories"
    - "docs/ and project documentation structure"
    - "Respect existing .gitignore patterns"
```

## Documentation Standards

```yaml
Project_Documentation:
  Organization_Rules:
    - "README.md at each level"
    - "index.md for navigation"
    - "Versioned subdirectories when needed"
    - "Assets in dedicated folders"

  Content_Standards:
    - "Clear section headers"
    - "Table of contents for long docs"
    - "Code examples with syntax highlighting"
    - "Cross-references to related docs"

Operational_Reports:
  Structure_Requirements:
    - "Executive summary first"
    - "Detailed findings follow"
    - "Actionable recommendations"
    - "Severity/priority indicators"

  Format_Standards:
    - "Markdown for human-readable reports"
    - "JSON for machine-readable metrics"
    - "HTML for coverage reports"
    - "Plain text for logs"
```

## Standard_Notifications

```yaml
Standard_Notifications:
  Operation_Start: "▶ Starting {operation}"
  Operation_Complete: "✅ {operation} completed successfully"
  File_Created: "📝 Created: {file_path}"
  File_Updated: "✏ Updated: {file_path}"
  Report_Generated: "📄 Report saved to: {path}"
  Error_Occurred: "❌ {operation} failed: {reason}"
  Warning_Issued: "⚠ {warning_message}"
  Info_Message: "ℹ {information}"

Output_Notifications:
  Success_Format: "✅ {operation} completed in {duration}"
  Error_Format: "❌ {operation} failed: {error_details}"
  Warning_Format: "⚠ {warning}: {details}"
  Info_Format: "ℹ {message}"
  Progress_Format: "🔄 {operation}: {current}/{total} ({percentage}%)"
```

## Gitignore Recommendations

```yaml
# Recommended .gitignore patterns:
Exclude_From_Git:
  - ".claudedocs/audit/"        # Operational logs
  - ".claudedocs/metrics/"      # Performance data
  - ".claudedocs/incidents/"    # Sensitive RCAs
  - ".claudedocs/validation/"   # Validation reports

Include_In_Git:
  - ".claudedocs/reports/"      # Useful analysis reports
  - ".claudedocs/summaries/"    # Important summaries
  - "docs/"                     # All project documentation
  - ".claudedocs/tasks/"        # Task tracking files
```

## Task File Formatting

```yaml
Task_File_Structure:
  Always_Compressed: true
  No_Exceptions: "All task files use UltraCompressed format"

  Header_Template: |
    # Legend: {used_symbols_only}

    T: {title}
    ID: {id} | S: {status} | P: {priority}
    Branch: {branch}

  Phase_Format:
    Template: "- {symbol} {phase}: {brief_description}"
    Symbols:
      "□": "pending phase"
      "◐": "in-progress phase"
      "✓": "completed phase"
      "⚠": "blocked phase"

  Context_Format:
    Decisions: "Dec: {key_decisions}"
    Blockers: "Block: {active_blockers}"
    Files: "Files: {affected_files}"
    Next: "Next: {immediate_next_step}"

  Progress_Format:
    Todos: "Todos: {active}/{total}"
    Completion: "Done: {percentage}%"
    Time: "Est: {estimated_remaining}"
```

## Validation & Enforcement

```yaml
Directory_Validation:
  Check_On_Startup: "Verify all required directories exist"
  Auto_Repair: "Create missing directories with proper permissions"
  Permission_Check: "Validate write access before operations"

Content_Validation:
  Format_Compliance: "Check markdown syntax and structure"
  Reference_Integrity: "Validate all internal links"
  Required_Sections: "Ensure all required headers present"

Notification_Standards:
  Emoji_Consistency: "Use standard emoji mapping"
  Path_Accuracy: "Always include full paths in notifications"
  Status_Clarity: "Clear success/failure indication"
```

---
*Documentation Patterns v2 - Unified standards for docs, structure, 
format requirements, and notifications*

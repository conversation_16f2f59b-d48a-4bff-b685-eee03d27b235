# Quality Patterns
# Unified validation, severity response, error handling, and quality control framework

## Legend
@include universal-constants.yml#Universal_Legend

## Severity Framework

```yaml
Severity_Levels:
  CRITICAL:
    Definition: "Data loss, security breach, production down"
    Response: "Immediate stop, alert, rollback, incident response"
    Recovery: "Manual intervention required"
    Response_Time: "Immediate"
    Examples: ["Delete prod data", "Expose secrets", "Force push main"]
    
  HIGH:
    Definition: "Build failure, test failure, deployment issues"
    Response: "Stop workflow, notify user, suggest fixes"
    Recovery: "Automated retry w/ backoff"
    Response_Time: "Rapid"
    Examples: ["Syntax error", "Permission denied", "Test failure"]
    
  MEDIUM:
    Definition: "Warning conditions, performance issues, code quality"
    Response: "Continue w/ warning, log for review"
    Recovery: "Attempt optimization, monitor"
    Response_Time: "Standard"
    Examples: ["Slow operation", "Deprecated API", "Large file"]
    
  LOW:
    Definition: "Info messages, style violations, suggestions"
    Response: "Note in output, continue"
    Recovery: "Background fixes, cleanup"
    Response_Time: "Batch processing"
    Examples: ["Code style", "Optional update", "Performance tip"]
```

## Pre-Execution Validation

```yaml
Validation_Sequence:
  1_Ambiguity_Check:
    Detect: ["vague instructions", "missing targets", "unclear scope"]
    Actions: ["Request clarification", "Block if critical"]
    
  2_Security_Validation:
    Path: "No ../, absolute paths only"
    Secrets: "Scan for API keys, passwords, tokens"
    Permissions: "User has required access"
    
  3_Dependency_Check:
    Tools: "Required CLI tools installed"
    Libraries: "Package dependencies available"
    Services: "External services accessible"
    
  4_State_Validation:
    Git: "Clean working tree for git ops"
    Files: "Target files exist & accessible"
    Resources: "Disk space, memory adequate"

Risk_Assessment:
  Score_Factors:
    Data_Loss: "High impact | Irreversibility: Moderate impact"
    Scope: "Moderate impact | Security: High impact"
    Backup: "Reduces risk | Test_Coverage: Slightly reduces risk"
    Sandbox: "Reduces risk | Checkpoint: Slightly reduces risk"
  Thresholds:
    Low: "Proceed w/ info"
    Medium: "Warn & confirm"
    High: "Require approval"
    Critical: "Block completely"
```

## Error Handling Patterns

```yaml
Error_Categories:
  Transient:
    Types: ["Network timeout", "Resource busy", "Rate limit"]
    Strategy: "Exponential backoff retry"
    Retry: "Base short, Max moderate duration, multiple attempts, with jitter"
    
  Permanent:
    Types: ["Syntax error", "Permission denied", "Not found"]
    Strategy: "No retry, immediate fallback"
    Response: "Clear error message & guidance"
    
  Context:
    Types: ["Config error", "State conflict", "Version mismatch"]
    Strategy: "Validation & helpful guidance"
    
  Resource:
    Types: ["Memory", "Disk space", "API limits"]
    Strategy: "Monitor, cleanup, queue management"

Circuit_Breaker:
  Threshold: "Multiple consecutive failures"
  Recovery: "Brief pause before re-enable"
  States:
    Closed: "Normal operation"
    Open: "Blocking calls after threshold"
    Half_Open: "Testing recovery, limited calls"
```

## Recovery Strategies

```yaml
Automatic_Recovery:
  Retry_Patterns:
    Simple: "Multiple attempts, short delay"
    Exponential: "Progressive backoff w/ jitter"
    Circuit: "Stop after threshold"
  Fallback_Options:
    Alternative: "Use native if MCP fails"
    Degraded: "Skip non-essential features"
    Cached: "Use previous successful outputs"
  State_Management:
    Checkpoint: "Save before risky ops"
    Rollback: "Auto-revert to good state"
    Cleanup: "Remove partial results"

Manual_Recovery:
  User_Guidance:
    Error_Format: "What failed→Why→How to fix"
    Actions: "Specific steps user can take"
    Resources: "Relevant help documentation"
  Intervention:
    Confirm: "Ask before destructive ops"
    Override: "Allow skip validation warnings"
    Custom: "Accept user alternatives"
```

## Configuration Validation

```yaml
Required_Files:
  Global:
    - "~/.claude/CLAUDE.md"
    - "~/.claude/RULES.md"
    - "~/.claude/PERSONAS.md"
    - "~/.claude/MCP.md"
  Project:
    - ".claude/commands/*.md"
    - ".claude/commands/shared/*.yml"

Syntax_Validation:
  YAML:
    Rules: ["Valid syntax", "Proper indent", "No duplicate keys"]
    Error: "File:Line:Column - Description"
  Markdown:
    Check: ["Valid headers", "Code blocks", "References"]
  References:
    Include: "@include ([^#]+)#([^\\s]+)"
    See: "@see shared/([^\\s]+)"
    Validate: ["File exists", "Section exists", "No circular refs"]

Structure_Requirements:
  Commands:
    Required: ["Legend", "Description", "Flags", "Examples", "Deliverables"]
    Flags: ["Universal always available", "Document all unique"]
  Dependencies:
    MCP: ["Server configured", "Health check passes"]
    CLI: ["git ≥2.0", "node ≥16.0", "python ≥3.8"]
```

## Runtime Monitoring

```yaml
Execution_Monitoring:
  Pre:
    Check: ["Command valid", "Flags recognized", "User authorized"]
  During:
    Monitor: ["Progress normal", "Resources in limits", "Catch errors"]
  Post:
    Verify: ["Expected outcomes", "No side effects", "Cleanup done"]

Context_Preservation:
  Checkpoints:
    Create: "Before destructive ops"
    Include: ["File states", "Working dir", "Command context"]
    Location: ".claudedocs/checkpoints/checkpoint-{timestamp}"
  Chain_Recovery:
    Isolate: "Don't lose successful steps"
    Alternative: "Suggest different approaches"
    Partial: "Use completed work in recovery"

Health_Monitoring:
  Frequency: "Regular intervals during active use"
  Timeout: "Brief timeout per check"
  Degradation: "Slow response → switch alternatives"
  Recovery: "Re-enable after brief pause"
```

## Command-Specific Recovery

```yaml
Build_Failures:
  Clean_Retry: "Remove artifacts, clear cache, rebuild"
  Dependencies: "Update lockfiles, reinstall packages"
  Compilation: "Suggest fixes, alternative approaches"

Test_Failures:
  Flaky_Tests: "Retry failed, identify unstable"
  Environment: "Reset state, check prerequisites"
  Coverage: "Generate missing tests, update thresholds"

Deploy_Failures:
  Health_Check: "Rollback, investigate logs"
  Resources: "Scale up, optimize deployment"
  Config: "Validate settings, check secrets"

Analysis_Failures:
  Tool_Unavailable: "Fallback to alternatives"
  Large_Codebase: "Reduce scope, batch process"
  Permissions: "Guide user through access setup"
```

## Quality Reports

```yaml
Validation_Reports:
  Location: ".claudedocs/validation/"
  Filename: "validation-{type}-{timestamp}.md"
  Sections:
    Summary: ["Total checks", "✅ Pass | ⚠ Warn | ❌ Fail"]
    Details: ["File-by-file status", "Reference integrity", "Dependencies"]
    Recommendations: ["Critical fixes", "Improvements", "Optimizations"]

Auto_Repair:
  Fixable:
    Missing_Sections: "Generate from templates"
    Broken_References: "Update to valid targets"
    Outdated_Syntax: "Modernize to standards"
  Manual_Required:
    Syntax_Errors: "User must fix YAML/Markdown"
    Missing_Files: "User must create/restore"
    Permission_Issues: "User must grant access"

Error_Learning:
  Pattern_Recognition: ["Track common errors", "User patterns", "System issues"]
  Adaptive_Response: ["Personalized suggestions", "Proactive warnings", "Auto fixes"]
  Metrics: ["Error frequency", "Recovery success", "User satisfaction"]
```

## Integration Points

```yaml
Startup: "Full config validation | Block critical, warn high"
Command: "Command-specific checks | Validate args, flags, permissions"
Continuous: "Monitor changes | Daily full validation"
On_Demand: "/validate --full"

CLI_Commands:
  Full: "/validate --config --full"
  Quick: "/validate --quick"
  Fix: "/validate --fix"
  Report: "/validate --report"

Usage_Examples:
  Manual:
    Full_Report: "/validate --config --report"
    Quick_Check: "/validate --quick"
    Auto_Repair: "/validate --fix --verbose"
  Programmatic:
    Pre_Execution: "validate_pre_execution()"
    Background: "validate_config_changes()"
    Report: "create_validation_report()"
```

## Code Quality Metrics

```yaml
Code_Quality_Metrics:
  Complexity_Metrics:
    Cyclomatic_Complexity: "Target: <10 per function | Warning: >15 | Critical: >20"
    Cognitive_Complexity: "Target: <15 per function | Warning: >25 | Critical: >30"
    Nesting_Depth: "Target: <4 levels | Warning: >5 | Critical: >6"
    Function_Length: "Target: <50 lines | Warning: >100 | Critical: >150"
    
  Maintainability_Metrics:
    DRY_Score: "Target: >90% | Warning: <80% | Critical: <70%"
    Code_Duplication: "Target: <5% | Warning: >10% | Critical: >15%"
    Technical_Debt: "Target: <A rating | Warning: B rating | Critical: C+ rating"
    
  Quality_Gates:
    Coverage: "Minimum 80% line coverage | 75% branch coverage"
    Linting: "Zero errors | Max 5 warnings per file"
    Type_Safety: "100% type coverage for TypeScript"
    Security: "Zero high/critical vulnerabilities"
```

## Test Quality Standards

```yaml
Test_Quality_Standards:
  Coverage_Requirements:
    Statement_Coverage: "Minimum: 80% | Target: 90% | Excellent: 95%+"
    Branch_Coverage: "Minimum: 75% | Target: 85% | Excellent: 90%+"
    Function_Coverage: "Minimum: 90% | Target: 95% | Excellent: 100%"
    Line_Coverage: "Minimum: 80% | Target: 90% | Excellent: 95%+"
    
  Test_Organization:
    Structure: "AAA (Arrange-Act-Assert) | Given-When-Then (BDD)"
    Naming: "Descriptive test names | should_[expected_behavior]_when_[condition]"
    Isolation: "Independent tests | No shared state | Fresh setup/teardown"
    
  Test_Types:
    Unit_Tests: "Fast (<1ms) | Isolated | Mock dependencies | 70-80% of total"
    Integration_Tests: "Component interaction | Real dependencies | 15-20% of total"
    E2E_Tests: "Full workflows | Browser automation | 5-10% of total"
    
  Quality_Indicators:
    Test_Reliability: "Flaky test rate <2% | Consistent results"
    Execution_Speed: "Unit test suite <10s | Full suite <5min"
    Mutation_Testing: "Mutation score >75% for critical components"
```

## Root Cause Analysis

```yaml
Root_Cause_Analysis:
  Investigation_Framework:
    Five_Whys:
      Process: "Ask 'why' 5 times to drill down to root cause"
      Example: "Bug→Why?→Logic error→Why?→Missing validation→Why?→No requirements→Why?→Rushed timeline→Why?→Poor planning"
      
    Fishbone_Analysis:
      Categories: ["People", "Process", "Technology", "Environment", "Materials", "Measurement"]
      Application: "Systematic categorization of potential causes"
      
    Timeline_Analysis:
      Sequence: "When did issue start | What changed | Correlation analysis"
      Events: "Code changes | Deployments | Configuration updates | External factors"
      
  Evidence_Collection:
    System_State:
      Logs: "Error logs | Application logs | System logs | Network logs"
      Metrics: "Performance metrics | Resource usage | Error rates"
      Configuration: "Environment variables | Feature flags | Database state"
      
    Change_History:
      Code_Changes: "Git commits | PR history | Deployment history"
      Infrastructure: "Server changes | Database migrations | Third-party updates"
      Process_Changes: "Team changes | Process updates | Tool updates"
      
  Resolution_Framework:
    Immediate_Fix:
      Stabilize: "Stop the bleeding | Rollback if necessary"
      Workaround: "Temporary solution to restore service"
      Monitor: "Verify fix effectiveness | Watch for recurrence"
      
    Permanent_Solution:
      Root_Fix: "Address underlying cause | Not just symptoms"
      Prevention: "Add safeguards | Improve processes | Update documentation"
      Validation: "Test fix thoroughly | Verify no new issues introduced"
      
    Learning_Integration:
      Documentation: "RCA report | Lessons learned | Knowledge sharing"
      Process_Improvement: "Update procedures | Add checks | Training needs"
      Monitoring: "Add alerts | Improve observability | Early warning systems"
```

---
*Quality Patterns v2 - Unified validation, severity response, error handling, and quality control framework*

# SuperClaude 工作流序列指南 🔄

> **智能工作流** | 命令序列最佳实践 | SuperClaude v2.0.1

---

## 🎯 核心工作流模式

### 🔄 工作流设计原理
- **阶段化**: 将复杂任务分解为有序阶段
- **验证点**: 每个阶段都有质量验证
- **可恢复**: 支持中断后从任意阶段恢复
- **并行化**: 无依赖的步骤可以并行执行
- **智能化**: 根据结果自动调整后续步骤

---

## 🚀 标准开发工作流

### 📋 完整功能开发流程 (8步)
```bash
# 🔍 第1步: 需求分析与任务规划
/task:create "实现用户认证功能" --breakdown --estimation

# 🏗️ 第2步: 架构设计与技术选型  
/design --architecture --security --persona-architect --think-hard --plan

# 📊 第3步: 现有代码分析
/analyze --codebase --dependencies --impact-assessment --think

# 🔧 第4步: 功能实现
/build --feature "用户认证" --tdd --incremental --magic --validate

# 🔍 第5步: 代码质量审查
/review --code --security --standards --evidence --persona-security

# ✅ 第6步: 测试验证
/test --unit --integration --e2e --coverage --persona-qa

# 📝 第7步: 文档更新
/document --api --user-guide --architecture --auto

# 🚀 第8步: 部署验证
/deploy --staging --validate --rollback-plan --monitoring
```

### ⚡ 快速功能开发流程 (5步)
```bash
# 快速分析
/analyze --code --changes-only --think --uc

# 敏捷实现  
/build --feature "功能名" --tdd --incremental

# 质量检查
/review --files . --quality --quick

# 快速测试
/test --changed --smoke --coverage

# 智能提交
/git --commit --smart --message "实现功能"
```

---

## 🐛 问题排查工作流

### 🚨 生产问题紧急响应流程 (6步)
```bash
# 🔴 第1步: 快速问题识别
/troubleshoot --prod --symptoms --impact --persona-analyzer --think

# 🔍 第2步: 深度根因分析  
/troubleshoot --five-whys --seq --evidence --comprehensive --ultrathink

# 🛡️ 第3步: 安全影响评估
/scan --security --vulnerabilities --impact --persona-security

# 🔧 第4步: 修复方案设计
/improve --fix --optimization --rollback-plan --validate --plan

# ⚡ 第5步: 快速修复实施
/build --hotfix --critical --test --validate --persona-analyzer

# ✅ 第6步: 修复验证与监控
/test --regression --production --monitoring --evidence
```

### 🔍 开发问题调试流程 (4步)
```bash
# 问题复现与分析
/troubleshoot --debug --reproduce --logs --persona-analyzer

# 代码深度分析
/analyze --code --logic --dependencies --think-hard

# 修复实现
/build --fix "具体问题" --tdd --validate

# 测试验证
/test --specific --edge-cases --regression
```

---

## 🔐 安全审计工作流

### 🛡️ 完整安全审计流程 (7步)
```bash
# 🔍 第1步: 安全扫描
/scan --security --comprehensive --owasp --persona-security

# 📊 第2步: 漏洞分析
/analyze --vulnerabilities --attack-vectors --risk-assessment --ultrathink

# 🔧 第3步: 依赖安全检查
/scan --dependencies --licenses --vulnerabilities --supply-chain

# 🏗️第4步: 架构安全审查
/review --architecture --security --threat-modeling --persona-security

# 📝 第5步: 代码安全审查
/review --code --security --injection --authentication --evidence

# 🔒 第6步: 安全加固建议
/improve --security --hardening --best-practices --plan

# ✅ 第7步: 安全测试验证
/test --security --penetration --vulnerability --compliance
```

### ⚡ 快速安全检查流程 (3步)
```bash
# 基础安全扫描
/scan --security --quick --critical-only

# 代码安全审查
/review --code --security --common-vulnerabilities

# 安全配置检查  
/analyze --configuration --security --compliance
```

---

## ⚡ 性能优化工作流

### 📈 完整性能优化流程 (6步)
```bash
# 🔍 第1步: 性能基准测试
/test --performance --baseline --benchmarks --persona-performance

# 📊 第2步: 性能瓶颈分析
/analyze --performance --bottlenecks --profiling --ultrathink

# 🔧 第3步: 系统资源分析
/troubleshoot --performance --cpu --memory --io --comprehensive

# 🏗️ 第4步: 架构性能审查
/review --architecture --scalability --performance --persona-architect

# ⚡ 第5步: 优化实施
/improve --performance --optimization --caching --async --plan

# ✅ 第6步: 性能验证测试
/test --performance --load --stress --comparison --monitoring
```

### 🎯 针对性性能优化流程 (4步)
```bash
# 问题定位
/troubleshoot --performance --specific --profiling

# 代码分析
/analyze --code --performance --algorithms --complexity

# 优化实现
/improve --performance --targeted --validate

# 性能测试
/test --performance --before-after --benchmarks
```

---

## 🏗️ 架构设计工作流

### 🎨 新系统架构设计流程 (8步)
```bash
# 🔍 第1步: 需求分析
/analyze --requirements --constraints --non-functional --think-hard

# 🎯 第2步: 架构模式选择
/design --architecture --patterns --scalability --persona-architect

# 🗄️ 第3步: 数据模型设计
/design --database --schema --relationships --normalization

# 🔌 第4步: API接口设计
/design --api --restful --contracts --versioning --c7

# 🔐 第5步: 安全架构设计
/design --security --authentication --authorization --persona-security

# 📊 第6步: 性能架构设计
/design --performance --caching --load-balancing --persona-performance

# 📝 第7步: 架构文档生成
/document --architecture --diagrams --decisions --rationale

# ✅ 第8步: 架构验证
/review --architecture --feasibility --risks --trade-offs --evidence
```

### ⚡ 架构重构流程 (5步)
```bash
# 现有架构分析
/analyze --architecture --technical-debt --coupling --think-hard

# 重构方案设计
/design --refactoring --incremental --migration-strategy --plan

# 风险评估
/estimate --refactoring --risk --impact --timeline

# 分阶段实施
/build --refactor --incremental --backward-compatible --validate

# 重构验证
/test --architecture --integration --performance --regression
```

---

## 🚀 部署运维工作流

### 📦 完整部署流程 (10步)
```bash
# 🔍 第1步: 部署前检查
/scan --deployment --readiness --dependencies --persona-architect

# 🏗️ 第2步: 构建验证
/build --production --optimize --validate --comprehensive

# ✅ 第3步: 测试验证
/test --full-suite --e2e --performance --smoke --coverage

# 📊 第4步: 安全检查
/scan --security --pre-deployment --compliance --persona-security

# 🎯 第5步: 预发布部署
/deploy --staging --validate --smoke-test --monitoring

# 🔍 第6步: 预发布验证
/test --staging --integration --user-acceptance --performance

# 🚀 第7步: 生产部署
/deploy --production --blue-green --rollback-plan --monitoring

# 📈 第8步: 生产验证
/test --production --health-check --performance --monitoring

# 📝 第9步: 部署文档
/document --deployment --runbook --monitoring --troubleshooting

# 🔄 第10步: 后续监控
/monitor --production --alerts --performance --business-metrics
```

### ⚡ 快速部署流程 (5步)
```bash
# 快速构建测试
/build --quick --test --validate

# 预发布部署
/deploy --staging --validate --quick

# 生产部署
/deploy --production --validate --monitoring

# 健康检查
/test --health-check --smoke

# 监控确认
/monitor --basic --alerts
```

---

## 📚 知识管理工作流

### 📖 文档完善流程 (5步)
```bash
# 🔍 第1步: 文档现状分析
/analyze --documentation --coverage --quality --gaps

# 📝 第2步: 文档规划设计
/design --documentation --structure --audience --persona-mentor

# ✍️ 第3步: 内容创建生成
/document --comprehensive --examples --diagrams --c7 --magic

# ✅ 第4步: 文档质量审查
/review --documentation --accuracy --completeness --clarity --evidence

# 🔄 第5步: 文档维护机制
/setup --documentation --automation --versioning --sync
```

### 🎓 代码解释工作流 (4步)
```bash
# 代码结构分析
/analyze --code --architecture --patterns --think

# 逻辑解释说明
/explain --code --logic --flow --persona-mentor

# 示例演示
/explain --examples --use-cases --best-practices

# 互动答疑
/explain --interactive --q-and-a --troubleshooting
```

---

## 🔄 维护管理工作流

### 🧹 代码维护流程 (6步)
```bash
# 🔍 第1步: 代码健康检查
/analyze --codebase --technical-debt --quality --metrics

# 🧹 第2步: 代码清理
/cleanup --code --unused --deprecated --dry-run --validate

# 🔄 第3步: 依赖管理
/analyze --dependencies --outdated --vulnerabilities --licenses

# 📦 第4步: 依赖更新
/migrate --dependencies --incremental --test --validate

# ✅第5步: 回归测试
/test --regression --full-suite --performance --coverage

# 📝 第6步: 维护文档
/document --maintenance --changes --impact --procedures
```

### ⚡ 日常维护流程 (3步)
```bash
# 快速健康检查
/scan --health --dependencies --security --quick

# 代码清理
/cleanup --incremental --safe --automated

# 提交更新
/git --commit --maintenance --automated
```

---

## 🎯 项目里程碑工作流

### 🏁 版本发布流程 (12步)
```bash
# 📋 第1步: 发布准备
/task:create "版本v2.0发布" --milestone --comprehensive

# 🔍 第2步: 功能完整性检查
/analyze --features --completeness --requirements --coverage

# 🧪 第3步: 全面质量检查
/test --full-suite --performance --security --accessibility --coverage

# 🔐 第4步: 安全审计
/scan --security --comprehensive --compliance --audit

# 📊 第5步: 性能基准
/test --performance --benchmarks --regression --production-like

# 📝 第6步: 文档完善
/document --release --user-guide --api --changelog --migration

# 🏗️ 第7步: 构建发布包
/build --release --production --optimize --sign --validate

# 🎯 第8步: 预发布部署
/deploy --pre-release --validate --comprehensive --rollback-ready

# ✅第9步: 用户验收测试
/test --uat --user-scenarios --business-critical --sign-off

# 🚀 第10步: 生产发布
/deploy --production --phased --monitoring --rollback-plan

# 📈 第11步: 发布后验证
/test --post-release --monitoring --business-metrics --user-feedback

# 🔄 第12步: 发布总结
/document --release-retrospective --lessons-learned --improvements
```

---

## 🤖 智能体协同工作流

### 👥 多智能体并行工作流
```bash
# 🚀 启动专家团队
/spawn --agent architect-expert &
/spawn --agent security-expert &  
/spawn --agent performance-expert &
/spawn --agent qa-expert &

# 📋 分配并行任务
/task:assign architect-expert "系统设计分析" &
/task:assign security-expert "安全风险评估" &
/task:assign performance-expert "性能瓶颈分析" &
/task:assign qa-expert "测试策略制定" &

# 🔄 协调整合结果
/task:integrate --multi-agent --consensus --comprehensive

# ✅ 统一验证
/review --multi-perspective --evidence --comprehensive
```

### 🔄 智能体接力工作流
```bash
# 第1棒: 需求分析师
/spawn --agent requirements-analyst "分析用户需求"

# 第2棒: 架构设计师 (基于第1棒结果)
/spawn --agent system-architect "设计技术架构" --context previous-result

# 第3棒: 开发专家 (基于第2棒结果)
/spawn --agent senior-developer "实现核心功能" --context architectural-design

# 第4棒: 质量专家 (基于第3棒结果)
/spawn --agent qa-specialist "质量验证测试" --context implementation
```

---

## 📊 工作流效果评估

### 🎯 效率对比
| 工作流类型 | 时间节省 | 质量提升 | 风险降低 | 适用场景 |
|------------|----------|----------|----------|----------|
| 🚀 快速流程 | 70% | +20% | -10% | 原型/实验 |
| 📊 标准流程 | 40% | +60% | -50% | 常规开发 |
| 💎 完整流程 | 20% | +90% | -80% | 关键系统 |
| 🤖 智能体协同 | 60% | +80% | -70% | 复杂项目 |

### 📈 质量保证
- **验证点**: 每个阶段都有明确的验证标准
- **可回滚**: 任意阶段都可以安全回滚
- **可恢复**: 支持中断后从断点继续
- **可追溯**: 完整的操作历史记录

---

## ⚡ 工作流使用技巧

### 🎯 选择合适的工作流
1. **项目复杂度**: 复杂项目使用完整流程
2. **时间压力**: 紧急情况使用快速流程  
3. **质量要求**: 高质量要求使用标准以上流程
4. **团队经验**: 新手团队使用详细流程

### 🔄 工作流定制化
```bash
# 保存常用工作流为模板
/task:template "我的开发流程" --steps "analyze,design,build,test,deploy"

# 根据项目特点定制流程
/task:customize "前端项目流程" --add-steps "ui-review,accessibility-test"

# 团队共享工作流
/task:share "团队标准流程" --team-wide --enforce-quality-gates
```

### 📊 工作流优化
- **持续改进**: 根据项目经验不断优化流程
- **度量驱动**: 基于数据分析优化工作流效率
- **团队反馈**: 收集团队使用反馈持续改进
- **工具集成**: 与现有开发工具链深度集成

---

*🔄 **实战建议**: 从简单工作流开始，逐步增加复杂度，形成适合团队的标准流程！*

*🚀 SuperClaude v2.0.1 工作流指南 | 让开发更有序、更高效*
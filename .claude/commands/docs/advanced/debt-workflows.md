# 债权管理专用工作流

## 🏦 金融系统专业化命令组合

### 📊 债权数据分析工作流
```bash
# 完整债权数据分析
/analyze --code --architecture --persona-analyzer --seq --debt-validation

# 债权处置逻辑审查  
/review --files src/services/debt-disposal --persona-security --cross-table

# 数据一致性深度检查
/troubleshoot --investigate --five-whys --debt-validation --seq
```

### 🔒 金融安全审计工作流  
```bash
# 金融合规全面扫描
/scan --security --financial-audit --sensitive-data --persona-security

# 生产环境安全部署
/deploy --env prod --validate --backup-first --financial-audit

# 数据库迁移安全检查
/migrate --database --validate --backup-first --cross-table --debt-validation
```

### 🚀 债权系统开发工作流
```bash
# 债权功能开发 (使用超压缩模式提高效率)
/build --feature "债权转换" --tdd --magic --uc

# 债权报表生成优化
/improve --performance --iterate --threshold 95% --persona-performance

# Excel导出功能测试
/test --integration --e2e --pup --coverage
```

### 📈 债权报告生成工作流
```bash  
# 智能文档生成 (使用超压缩模式)
/document --api --examples --c7 --uc

# README和更新日志生成 (使用GitHub工具)
superclaude readme
superclaude changelog

# 智能提交 (使用GitHub工具)
superclaude commit "优化债权处置算法" --interactive
```

## 🎯 常用场景快捷命令

### 日常开发
```bash
# 快照管理 (你现有的中文命令)
快照：优化债权计算逻辑

# 代码提交
superclaude commit "修复债权余额计算错误" -i

# 启动测试
测试
```

### 生产维护
```bash
# 生产部署
/deploy --env prod --validate --backup-first --monitor

# 紧急回滚  
/deploy --rollback --ultrathink --financial-audit

# 数据完整性检查
/scan --debt-validation --cross-table --strict
```

### 性能优化
```bash
# 债权查询性能分析
/analyze --profile --deep --persona-performance --seq

# 数据库查询优化
/improve --performance --cache --iterate --pup

# 负载测试
/test --performance --load --coverage
```

## 🔧 MCP服务器在债权系统中的应用

- **`--seq`**: 复杂债权计算逻辑推理、多步骤数据验证
- **`--c7`**: Spring Boot、React、MySQL官方文档查询
- **`--pup`**: 前端自动化测试、Excel导出验证
- **`--magic`**: 债权管理UI组件快速生成

## ⚠️ 金融系统安全规则

1. **生产环境强制验证**: 所有 `--env prod` 自动启用 `--validate`
2. **数据库操作强制备份**: 所有 `--database` 自动启用 `--backup-first` 
3. **债权数据迁移验证**: 所有 `--data` 自动启用 `--debt-validation`
4. **超压缩模式限制**: 仅用于简单操作，复杂分析禁用 `--uc`

---
*债权管理专用工作流 | SuperClaude v2.0.1 + v3功能增强*
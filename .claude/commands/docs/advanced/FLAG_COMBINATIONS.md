# SuperClaude 标志组合指南 🏷️

> **智能标志组合** | 发挥命令最大潜力 | SuperClaude v2.0.1

---

## 🎯 标志组合原理

### 🔗 组合逻辑
- **通用标志**: 所有命令都可使用
- **专用标志**: 特定命令独有功能  
- **角色标志**: 专家视角模式
- **MCP标志**: 增强AI能力
- **优化标志**: 效率与资源控制

### ⚡ 组合效应
- **加法效应**: 多个标志能力叠加
- **协同效应**: 某些组合产生增强效果
- **冲突处理**: 系统智能处理冲突标志

---

## 🧠 思维深度标志组合

### 基础思维模式
```bash
# 轻量分析 (~1K tokens)
/analyze --code

# 标准分析 (~4K tokens)  
/analyze --code --think

# 深度分析 (~10K tokens)
/analyze --code --think-hard

# 极限分析 (~32K tokens)
/analyze --code --ultrathink
```

### 思维 + 专业角色
```bash
# 架构师深度思考
/analyze --architecture --think-hard --persona-architect

# 安全专家极限分析
/scan --security --ultrathink --persona-security

# 性能专家深度诊断  
/troubleshoot --performance --think-hard --persona-performance
```

### 思维 + MCP增强
```bash
# 深度思考 + 文档查询
/explain --architecture --think-hard --c7

# 极限分析 + 复杂推理
/troubleshoot --complex --ultrathink --seq

# 标准思考 + UI增强
/build --frontend --think --magic
```

---

## 🤖 MCP服务器组合

### 单一MCP服务
```bash
--c7      # Context7: 文档查找专家
--seq     # Sequential: 复杂推理专家  
--magic   # Magic: UI组件生成专家
--pup     # Puppeteer: 浏览器自动化专家
```

### 双重组合 (推荐)
```bash
# 文档 + 推理 (分析类任务)
/analyze --architecture --c7 --seq

# 推理 + UI (前端开发)
/build --frontend --seq --magic

# 自动化 + 推理 (测试任务)
/test --e2e --pup --seq

# 文档 + UI (文档生成)
/document --interactive --c7 --magic
```

### 三重组合 (高级)
```bash
# 分析专家组合
/analyze --comprehensive --c7 --seq --think-hard

# 前端开发组合  
/build --react --magic --seq --think

# 测试专家组合
/test --full-suite --pup --seq --think
```

### 全能组合 (终极)
```bash
# 最大能力模式 (慎用，高Token消耗)
/analyze --system --all-mcp --ultrathink --introspect

# 平衡全能模式
/task:create "复杂功能" --all-mcp --think-hard --plan
```

---

## 👤 专业角色组合

### 单一角色专精
```bash
--persona-architect     # 系统架构师 (设计/模式/扩展性)
--persona-frontend      # 前端专家 (UI/UX/无障碍)  
--persona-backend       # 后端专家 (API/数据/可靠性)
--persona-security      # 安全专家 (威胁/漏洞/加固)
--persona-performance   # 性能专家 (优化/瓶颈/监控)
--persona-analyzer      # 分析专家 (根因/证据/逻辑)
--persona-mentor        # 导师专家 (教学/指导/解释)
--persona-refactorer    # 重构专家 (代码质量/清理)
--persona-qa            # 质量专家 (测试/验证/边界)
```

### 角色 + 命令最佳匹配
```bash
# 架构设计
/design --system --persona-architect --think-hard

# 安全审计
/scan --security --persona-security --comprehensive

# 性能调优
/troubleshoot --performance --persona-performance --profiling

# 前端开发
/build --react --persona-frontend --magic --accessibility

# 后端API  
/build --api --persona-backend --scalability --reliability

# 代码审查
/review --quality --persona-refactorer --standards --evidence

# 问题分析
/troubleshoot --complex --persona-analyzer --five-whys --seq

# 测试策略
/test --comprehensive --persona-qa --edge-cases --coverage

# 学习解释
/explain --concepts --persona-mentor --step-by-step --examples
```

### 多角色协同 (通过spawn)
```bash
# 架构 + 安全双重审查
/spawn --agent architect-expert && /spawn --agent security-expert

# 前端 + 后端全栈开发
/build --fullstack --persona-frontend --persona-backend

# 性能 + 安全综合分析
/analyze --system --persona-performance --persona-security --ultrathink
```

---

## 📊 质量验证组合

### 基础验证
```bash
--validate      # 基础安全检查
--security      # 安全重点分析  
--quality       # 代码质量检查
--evidence      # 提供证据支持
```

### 验证 + 思维深度
```bash
# 标准验证分析
/review --code --validate --security --think

# 深度验证分析  
/deploy --production --validate --security --think-hard --evidence

# 极限验证分析
/migrate --database --validate --security --ultrathink --comprehensive
```

### 验证 + 专业角色
```bash
# 安全专家验证
/scan --comprehensive --validate --security --persona-security

# 架构师质量验证
/review --architecture --validate --quality --persona-architect

# QA专家全面验证
/test --full-suite --validate --coverage --persona-qa --evidence
```

---

## ⚡ 效率优化组合

### Token节省组合
```bash
# 超压缩模式
--uc / --ultracompressed

# 压缩 + 快速分析
/analyze --code --uc --quick

# 压缩 + 增量操作
/build --feature --uc --incremental --dry-run

# 压缩 + 预览模式
/cleanup --code --uc --plan --dry-run
```

### 智能预览组合
```bash
# 预览 + 验证
--plan --validate --dry-run

# 预览执行组合
/deploy --staging --plan --validate --dry-run

# 智能预览分析
/cleanup --aggressive --plan --dry-run --think --evidence
```

### 增量处理组合
```bash
# 增量 + Git感知
--incremental --git-aware --changed-only

# 增量开发
/build --feature "认证" --incremental --tdd --validate

# 增量部署
/deploy --production --incremental --validate --rollback-plan
```

---

## 🔥 场景化终极组合

### 🏗️ 新项目初始化终极组合
```bash
/build --init --fullstack --magic --all-mcp --persona-architect --think-hard --plan --validate
```
**解析**: 初始化全栈项目 + UI组件增强 + 全MCP能力 + 架构师视角 + 深度思考 + 预览计划 + 验证安全

### 🐛 生产问题排查终极组合
```bash
/troubleshoot --prod --five-whys --seq --persona-analyzer --ultrathink --evidence --comprehensive --introspect
```
**解析**: 生产问题 + 根因分析 + 复杂推理 + 分析专家 + 极限思考 + 证据收集 + 全面诊断 + 透明思维

### 🔐 安全审计终极组合
```bash
/scan --security --owasp --comprehensive --persona-security --ultrathink --evidence --validate --audit-trail
```
**解析**: 安全扫描 + OWASP标准 + 全面检查 + 安全专家 + 极限分析 + 证据支持 + 验证 + 审计轨迹

### ⚡ 性能优化终极组合  
```bash
/analyze --performance --bottlenecks --persona-performance --ultrathink --profiling --benchmarks --recommendations
```
**解析**: 性能分析 + 瓶颈识别 + 性能专家 + 极限思考 + 性能剖析 + 基准测试 + 优化建议

### 🚀 生产部署终极组合
```bash
/deploy --production --validate --rollback-plan --persona-architect --think-hard --evidence --monitoring --audit
```
**解析**: 生产部署 + 验证检查 + 回滚计划 + 架构师视角 + 深度思考 + 证据记录 + 监控 + 审计

---

## 📈 组合效果对比

### Token消耗对比 (相对值)
| 组合复杂度 | Token消耗 | 适用场景 | 示例 |
|------------|-----------|----------|------|
| 🥉 基础 | ~1K | 日常检查 | `/review --files .` |
| 🥈 标准 | ~4K | 常规开发 | `/analyze --code --think --validate` |
| 🥇 高级 | ~10K | 复杂任务 | `/troubleshoot --prod --think-hard --persona-analyzer` |
| 💎 极限 | ~32K | 关键系统 | `/analyze --system --ultrathink --all-mcp --comprehensive` |

### 能力提升对比
| 能力维度 | 无标志 | +角色标志 | +MCP标志 | +思维标志 | 全组合 |
|----------|--------|-----------|----------|-----------|---------|
| 专业深度 | ⭐ | ⭐⭐⭐ | ⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 分析广度 | ⭐ | ⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 准确性 | ⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 实用性 | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |

---

## 🎯 组合选择策略

### 🚦 按紧急程度选择
```bash
# 🔴 紧急 (快速响应)
基础命令 + --uc + --quick

# 🟡 重要 (标准流程)  
标准命令 + --think + --validate + 专业角色

# 🟢 规划 (深度分析)
完整命令 + --think-hard + --all-mcp + --comprehensive
```

### 📊 按项目阶段选择
```bash
# 🚀 项目初期 (探索阶段)
--think-hard + --persona-architect + --plan + --comprehensive

# 🏗️ 开发阶段 (实现阶段)
--think + 相关专业角色 + --tdd + --validate  

# 🔧 维护阶段 (优化阶段)
--incremental + --git-aware + --uc + --dry-run
```

### 👥 按团队经验选择
```bash
# 🌱 新手团队
--persona-mentor + --explain + --step-by-step + --interactive

# 🌿 中级团队
--think + 相关专业角色 + --validate + --evidence

# 🌳 专家团队  
--ultrathink + --all-mcp + --introspect + --comprehensive
```

---

## ⚠️ 组合使用注意事项

### 🚫 避免的组合
```bash
# ❌ 冲突组合
--no-mcp --all-mcp          # MCP控制冲突
--dry-run --force           # 执行模式冲突  
--quick --ultrathink        # 速度深度冲突

# ❌ 过度组合 (Token浪费)
--ultrathink --all-mcp --introspect --comprehensive --think-hard  # 过度复杂

# ❌ 不匹配组合
/cleanup --magic            # 清理任务不需要UI组件
/build --five-whys          # 构建不需要根因分析
```

### ⚡ 高效组合原则
1. **目标导向**: 根据具体目标选择标志
2. **成本效益**: 平衡功能需求与Token消耗  
3. **经验积累**: 记录有效组合供重复使用
4. **渐进增强**: 从简单组合开始逐步增加复杂度

---

*🎯 **实用建议**: 将常用组合保存为别名，提高使用效率！*

*🚀 SuperClaude v2.0.1 标志组合指南 | 让每个命令发挥最大威力*
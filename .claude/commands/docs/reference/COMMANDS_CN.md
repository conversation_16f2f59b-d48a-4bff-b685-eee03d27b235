# SuperClaude 中文命令参考

本文档提供所有SuperClaude命令的中文说明和使用示例。

## 📋 命令分类

### 🔧 开发命令 (4个)

#### `/analyze` - 分析
**目的**: 项目分析和代码审查
- **使用**: `/analyze --code --performance` - 代码性能分析
- **使用**: `/analyze --architecture --security` - 架构安全分析
- **使用**: `/analyze --dependencies --outdated` - 依赖分析

#### `/build` - 构建
**目的**: 项目构建和打包
- **使用**: `/build --clean --test` - 清理构建并测试
- **使用**: `/build --production --optimize` - 生产环境构建
- **使用**: `/build --docker --push` - Docker构建并推送

#### `/dev-setup` - 开发环境
**目的**: 开发环境配置和初始化
- **使用**: `/dev-setup --install --configure` - 安装配置开发环境
- **使用**: `/dev-setup --database --sample-data` - 数据库和示例数据
- **使用**: `/dev-setup --ide --plugins` - IDE和插件配置

#### `/test` - 测试
**目的**: 自动化测试执行
- **使用**: `/test --unit --integration` - 单元和集成测试
- **使用**: `/test --coverage --report` - 测试覆盖率报告
- **使用**: `/test --performance --load` - 性能和负载测试

### 🔍 分析改进命令 (4个)

#### `/review` - 审查
**目的**: 代码审查和质量检查
- **使用**: `/review --code --standards` - 代码标准审查
- **使用**: `/review --security --vulnerabilities` - 安全漏洞审查
- **使用**: `/review --performance --bottlenecks` - 性能瓶颈审查

#### `/improve` - 改进
**目的**: 代码优化和重构建议
- **使用**: `/improve --performance --memory` - 性能内存优化
- **使用**: `/improve --structure --patterns` - 结构设计模式优化
- **使用**: `/improve --security --hardening` - 安全加固优化

#### `/troubleshoot` - 排错
**目的**: 问题诊断和解决方案
- **使用**: `/troubleshoot --errors --logs` - 错误日志分析
- **使用**: `/troubleshoot --performance --profiling` - 性能问题分析
- **使用**: `/troubleshoot --database --queries` - 数据库查询问题

#### `/explain` - 解释
**目的**: 代码和架构解释说明
- **使用**: `/explain --code --logic` - 代码逻辑解释
- **使用**: `/explain --architecture --patterns` - 架构模式解释
- **使用**: `/explain --api --endpoints` - API端点解释

### 🚀 运维命令 (6个)

#### `/deploy` - 部署
**目的**: 应用部署和发布管理
- **使用**: `/deploy --staging --validate` - 预发布环境部署
- **使用**: `/deploy --production --rollback-plan` - 生产环境部署
- **使用**: `/deploy --docker --kubernetes` - 容器化部署

#### `/migrate` - 迁移
**目的**: 数据库和系统迁移
- **使用**: `/migrate --database --schema` - 数据库架构迁移
- **使用**: `/migrate --data --backup` - 数据迁移备份
- **使用**: `/migrate --system --configuration` - 系统配置迁移

#### `/scan` - 扫描
**目的**: 安全和漏洞扫描
- **使用**: `/scan --security --vulnerabilities` - 安全漏洞扫描
- **使用**: `/scan --dependencies --licenses` - 依赖许可证扫描
- **使用**: `/scan --code --quality` - 代码质量扫描

#### `/cleanup` - 清理
**目的**: 项目清理和维护
- **使用**: `/cleanup --code --unused` - 清理未使用代码
- **使用**: `/cleanup --files --temporary` - 清理临时文件
- **使用**: `/cleanup --dependencies --outdated` - 清理过时依赖

#### `/git` - Git操作
**目的**: Git版本控制操作
- **使用**: `/git --commit --message "描述"` - 智能提交
- **使用**: `/git --branch --create feature/新功能` - 创建分支
- **使用**: `/git --merge --strategy ours` - 合并策略

#### `/estimate` - 估算
**目的**: 工作量和成本估算
- **使用**: `/estimate --feature --complexity` - 功能复杂度估算
- **使用**: `/estimate --migration --risk` - 迁移风险估算
- **使用**: `/estimate --performance --impact` - 性能影响估算

### 📋 工作流命令 (5个)

#### `/design` - 设计
**目的**: 系统设计和架构规划
- **使用**: `/design --architecture --microservices` - 微服务架构设计
- **使用**: `/design --database --schema` - 数据库设计
- **使用**: `/design --api --restful` - RESTful API设计

#### `/document` - 文档
**目的**: 文档创建和维护
- **使用**: `/document --api --swagger` - API文档生成
- **使用**: `/document --architecture --diagrams` - 架构图表文档
- **使用**: `/document --user --guide` - 用户指南文档

#### `/load` - 加载
**目的**: 项目和配置加载
- **使用**: `/load --project --configuration` - 项目配置加载
- **使用**: `/load --data --samples` - 示例数据加载
- **使用**: `/load --environment --variables` - 环境变量加载

#### `/spawn` - 智能体
**目的**: 启动专门任务智能体
- **使用**: `/spawn --agent database-expert` - 数据库专家智能体
- **使用**: `/spawn --agent security-auditor` - 安全审计智能体
- **使用**: `/spawn --agent performance-optimizer` - 性能优化智能体

#### `/task` - 任务
**目的**: 任务管理和执行
- **使用**: `/task --create --priority high` - 创建高优先级任务
- **使用**: `/task --execute --parallel` - 并行执行任务
- **使用**: `/task --monitor --progress` - 监控任务进度

## 🔥 常用组合命令

### 完整开发流程
```bash
/analyze --code --architecture     # 分析现有代码
/design --improvements --plan      # 设计改进方案
/implement --features --tests      # 实现功能和测试
/review --code --security          # 代码安全审查
/test --all --coverage             # 完整测试覆盖
/deploy --staging --validate       # 预发布部署验证
```

### 问题排查流程
```bash
/troubleshoot --errors --analysis  # 问题分析
/scan --security --performance     # 安全性能扫描
/explain --issue --root-cause      # 根因分析解释
/improve --fix --optimization      # 修复优化建议
```

### 日常维护流程
```bash
/cleanup --code --files            # 清理代码文件
/migrate --database --incremental  # 增量数据库迁移
/document --updates --changes       # 更新变更文档
/estimate --maintenance --cost      # 维护成本估算
```

## 📚 使用最佳实践

### 1. 命令链式使用
- 多个命令可以组合使用获得更好效果
- 建议按照逻辑顺序执行相关命令

### 2. 参数优化
- 使用`--plan`参数预览执行计划
- 使用`--dry-run`参数模拟执行
- 使用`--verbose`参数获得详细输出

### 3. 专业化智能体
- 复杂任务优先使用`/spawn`启动专门智能体
- 可以同时运行多个智能体并行处理

### 4. 安全考虑
- 生产环境操作务必使用`--validate`参数
- 重要操作前建议使用`/estimate`评估风险
- 数据库操作前必须进行`/scan --security`检查

---

*SuperClaude v2.0.1 中文命令参考 | 更新时间: 2025-08-14*
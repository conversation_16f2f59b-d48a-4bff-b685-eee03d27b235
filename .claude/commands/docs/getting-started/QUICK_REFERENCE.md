# SuperClaude 命令快速参考卡 🚀

> **最新版本**: SuperClaude v2.0.1 | **更新时间**: 2025-08-14  
> **项目**: 财务管理系统 | **语言**: 中英文双语支持

---

## 🔥 最常用命令 (Top 10)

| 命令 | 用途 | 快速示例 |
|------|------|----------|
| `/analyze --code` | 代码质量分析 | `/analyze --code src/ --security` |
| `/review --files` | 代码审查 | `/review --files . --quality --evidence` |
| `/build --feature` | 功能构建 | `/build --feature "用户认证" --tdd` |
| `/test --coverage` | 测试覆盖 | `/test --unit --integration --coverage` |
| `/troubleshoot --errors` | 问题排查 | `/troubleshoot --errors --logs --five-whys` |
| `/deploy --staging` | 部署验证 | `/deploy --staging --validate --rollback-plan` |
| `/task:create` | 任务管理 | `/task:create "实现OAuth认证"` |
| `/cleanup --code` | 代码清理 | `/cleanup --code --unused --dependencies` |
| `/git --commit` | Git操作 | `/git --commit --message "功能完成"` |
| `/explain --code` | 代码解释 | `/explain --architecture --patterns` |

## 📊 命令分类速览

### 🔧 开发类 (4个)
- **`/analyze`** - 多维度分析 (代码/架构/性能/安全)
- **`/build`** - 项目构建 (功能/测试/部署包)
- **`/dev-setup`** - 环境配置 (依赖/CI/监控)
- **`/test`** - 测试执行 (单元/集成/性能)

### 🔍 改进类 (4个)  
- **`/review`** - 代码审查 (质量/安全/性能)
- **`/improve`** - 优化建议 (重构/性能/安全)
- **`/troubleshoot`** - 问题诊断 (错误/性能/根因)
- **`/explain`** - 解释说明 (逻辑/架构/API)

### 🚀 运维类 (6个)
- **`/deploy`** - 部署管理 (预发/生产/回滚)
- **`/migrate`** - 数据迁移 (数据库/配置/系统)
- **`/scan`** - 安全扫描 (漏洞/依赖/质量)
- **`/cleanup`** - 项目清理 (代码/文件/依赖)
- **`/git`** - 版本控制 (提交/分支/合并)
- **`/estimate`** - 工作估算 (复杂度/风险/成本)

### 📋 工作流类 (5个)
- **`/design`** - 系统设计 (架构/数据库/API)
- **`/document`** - 文档生成 (API/架构/用户)
- **`/load`** - 数据加载 (项目/配置/示例)
- **`/spawn`** - 智能体 (专家/并行/协同)
- **`/task`** - 任务管理 (创建/跟踪/恢复)

---

## 🏷️ 通用标志速查

### 🧠 思维深度
| 标志 | Token消耗 | 适用场景 |
|------|-----------|----------|
| `--think` | ~4K | 多文件分析 |
| `--think-hard` | ~10K | 架构级分析 |
| `--ultrathink` | ~32K | 系统重构 |

### 📦 效率优化
| 标志 | 效果 | 使用场景 |
|------|------|----------|
| `--uc / --ultracompressed` | 大幅减少token | 大型项目分析 |
| `--plan` | 显示执行计划 | 重要操作预览 |
| `--dry-run` | 预览不执行 | 安全验证 |

### 🔧 MCP 服务器
| 标志 | 功能 | 最适合 |
|------|------|--------|
| `--c7` | 文档查找 | API参考 |
| `--seq` | 复杂推理 | 问题分析 |
| `--magic` | UI组件 | 前端开发 |
| `--pup` | 浏览器自动化 | E2E测试 |
| `--all-mcp` | 全部能力 | 复杂任务 |

### 👤 专家角色
| 角色标志 | 专业领域 | 推荐命令 |
|----------|----------|----------|
| `--persona-architect` | 系统架构 | `/design`, `/analyze` |
| `--persona-frontend` | UI/UX | `/build`, `/review` |
| `--persona-backend` | 服务端 | `/migrate`, `/scan` |
| `--persona-security` | 安全审计 | `/scan`, `/review` |
| `--persona-performance` | 性能优化 | `/analyze`, `/troubleshoot` |

---

## ⚡ 黄金组合命令

### 🔥 完整开发流程
```bash
/analyze --code --architecture --persona-architect    # 1. 架构师分析现状
/design --improvements --plan --think-hard           # 2. 设计改进方案  
/build --feature "新功能" --tdd --magic              # 3. 构建功能+测试
/review --code --security --evidence                 # 4. 安全审查
/test --all --coverage --e2e                        # 5. 完整测试
/deploy --staging --validate --rollback-plan        # 6. 安全部署
```

### 🚨 紧急问题排查
```bash
/troubleshoot --prod --five-whys --persona-analyzer  # 1. 根因分析
/scan --security --performance --ultrathink         # 2. 深度扫描
/explain --issue --root-cause --evidence            # 3. 问题解释
/improve --fix --optimization --plan                # 4. 修复建议
/test --regression --validate                       # 5. 回归测试
```

### 🔄 日常维护流程
```bash
/cleanup --code --files --dependencies --dry-run    # 1. 预览清理
/migrate --database --incremental --validate        # 2. 增量迁移
/scan --vulnerabilities --licenses                  # 3. 安全扫描
/document --updates --api --auto                    # 4. 文档更新
/git --commit --smart --push                        # 5. 智能提交
```

### 🏗️ 新项目启动
```bash
/build --init --fullstack --magic --all-mcp         # 1. 初始化全栈
/dev-setup --install --ci --monitor --team          # 2. 开发环境
/design --architecture --database --api             # 3. 系统设计
/document --project --architecture --guide          # 4. 文档生成
/test --setup --unit --e2e                         # 5. 测试框架
```

---

## 🎯 场景化快速命令

### 🔍 代码质量检查
```bash
# 快速检查
/review --files . --quality

# 深度审查
/analyze --code --security --think-hard --persona-security

# 性能分析  
/analyze --profile --bottlenecks --persona-performance
```

### 🐛 Bug修复工作流
```bash
# 问题诊断
/troubleshoot --errors --logs --seq --persona-analyzer

# 代码分析
/analyze --code --logic --think --introspect

# 修复实施
/build --fix "具体问题" --tdd --validate

# 测试验证
/test --regression --edge-cases --coverage
```

### 🚀 功能开发工作流  
```bash
# 需求分析
/task:create "功能描述"

# 架构设计
/design --feature --patterns --persona-architect

# 开发实现
/build --feature --incremental --tdd --magic

# 质量保证
/review --code --test --security --evidence
```

### 📊 性能优化专项
```bash
# 性能分析
/analyze --profile --memory --cpu --persona-performance

# 瓶颈识别
/troubleshoot --performance --profiling --ultrathink

# 优化建议
/improve --performance --scalability --plan

# 验证测试
/test --performance --load --benchmarks
```

---

## 💡 高级用法技巧

### 🔗 命令链式组合
```bash
# 多命令并行 (用 && 连接)
/scan --security && /test --unit && /deploy --staging

# 条件执行
/build --feature "认证" --plan && /review --security
```

### 🎛️ 智能参数
```bash
# 自动检测项目类型
/build --auto-detect --optimize

# 基于Git历史智能分析
/analyze --git-aware --changes-only

# 上下文感知清理
/cleanup --smart --preserve-important
```

### 🤖 多智能体协同
```bash
# 并行专家协同
/spawn --agent security-expert && /spawn --agent performance-expert

# 任务分解协同
/task:create "复杂功能" --multi-agent --parallel
```

---

## ⚠️ 安全使用指南

### 🔒 生产环境规则
- ✅ **必须使用**: `--plan`, `--validate`, `--dry-run`
- ⚠️ **谨慎使用**: `--force`, `--no-validate`  
- 🚫 **禁止使用**: 未经`--plan`预览的破坏性操作

### 🛡️ 数据安全
```bash
# 数据库操作前必须验证
/migrate --database --validate --backup-first

# 敏感操作使用安全模式
/cleanup --sensitive-data --secure-delete --audit-log
```

### 📝 操作审计
```bash
# 关键操作启用审计
/deploy --prod --audit --evidence --rollback-plan

# 安全操作留痕
/scan --security --comprehensive --report --archive
```

---

## 📚 进阶学习资源

### 📖 详细文档
- **完整命令手册**: [`COMMANDS.md`](COMMANDS.md)
- **中文命令指南**: [`COMMANDS_CN.md`](COMMANDS_CN.md) 
- **共享配置**: [`shared/`](shared/) 目录

### 🎓 最佳实践
1. **总是先`--plan`**: 重要操作前预览计划
2. **合理选择思维深度**: 根据复杂度选择`--think`级别
3. **利用专家角色**: 使用`--persona-*`获得专业视角
4. **组合MCP服务**: 复杂任务启用多个MCP服务器
5. **保存任务上下文**: 复杂功能使用`/task:create`跟踪

---

*🚀 SuperClaude v2.0.1 快速参考卡 | 助力高效开发 | claude.ai/code*
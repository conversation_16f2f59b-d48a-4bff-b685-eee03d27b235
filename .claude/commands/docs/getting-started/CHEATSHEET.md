# SuperClaude 命令速查表 📋

> **一页掌握所有命令** | SuperClaude v2.0.1 | 财务管理系统专用

---

## 🎯 核心命令矩阵

| 类别 | 命令 | 核心功能 | 必备标志 | 典型用法 |
|------|------|----------|----------|----------|
| **🔧 开发** | `/analyze` | 代码分析 | `--code` `--security` | `/analyze --code src/ --security --think` |
| | `/build` | 项目构建 | `--feature` `--tdd` | `/build --feature "认证" --tdd --magic` |  
| | `/dev-setup` | 环境配置 | `--install` `--ci` | `/dev-setup --install --ci --monitor` |
| | `/test` | 测试执行 | `--coverage` `--e2e` | `/test --unit --coverage --e2e` |
| **🔍 改进** | `/review` | 代码审查 | `--quality` `--evidence` | `/review --files . --quality --evidence` |
| | `/improve` | 优化建议 | `--performance` `--security` | `/improve --performance --plan` |
| | `/troubleshoot` | 问题诊断 | `--errors` `--five-whys` | `/troubleshoot --prod --five-whys` |
| | `/explain` | 解释说明 | `--logic` `--architecture` | `/explain --architecture --patterns` |
| **🚀 运维** | `/deploy` | 部署管理 | `--validate` `--rollback-plan` | `/deploy --staging --validate` |
| | `/migrate` | 数据迁移 | `--database` `--backup` | `/migrate --database --validate` |
| | `/scan` | 安全扫描 | `--security` `--vulnerabilities` | `/scan --security --comprehensive` |
| | `/cleanup` | 项目清理 | `--code` `--unused` | `/cleanup --code --unused --dry-run` |
| | `/git` | 版本控制 | `--commit` `--smart` | `/git --commit --message "完成功能"` |
| | `/estimate` | 工作估算 | `--complexity` `--risk` | `/estimate --feature --complexity` |
| **📋 工作流** | `/design` | 系统设计 | `--architecture` `--database` | `/design --architecture --patterns` |
| | `/document` | 文档生成 | `--api` `--auto` | `/document --api --auto --swagger` |
| | `/load` | 数据加载 | `--project` `--samples` | `/load --project --configuration` |
| | `/spawn` | 智能体 | `--agent` `--parallel` | `/spawn --agent database-expert` |
| | `/task` | 任务管理 | `:create` `:status` | `/task:create "实现OAuth认证"` |

---

## ⚡ 超级标志组合

### 🧠 思维级别 (按复杂度选择)
```bash
轻度分析    # 无额外标志 (~1K tokens)
--think     # 多文件上下文 (~4K tokens) 
--think-hard # 架构级深度 (~10K tokens)
--ultrathink # 系统重构级 (~32K tokens)
```

### 🔧 MCP 超能力 (可组合)
```bash
--c7        # 文档查找专家
--seq       # 复杂推理专家  
--magic     # UI组件专家
--pup       # 浏览器自动化专家
--all-mcp   # 全部专家 (终极模式)
```

### 👤 专业角色 (选择适合的)
```bash
--persona-architect    # 系统架构师视角
--persona-frontend     # 前端开发者视角
--persona-backend      # 后端开发者视角
--persona-security     # 安全专家视角
--persona-performance  # 性能优化师视角
--persona-analyzer     # 深度分析师视角
```

---

## 🔥 黄金组合公式

### 📊 项目分析套餐
```bash
# 🥉 铜牌 - 快速检查
/review --files . --quality

# 🥈 银牌 - 深度审查  
/analyze --code --security --think --persona-security

# 🥇 金牌 - 全面分析
/analyze --code --architecture --security --ultrathink --all-mcp --introspect
```

### 🚀 功能开发套餐
```bash
# 🥉 铜牌 - 基础开发
/build --feature "功能名" --tdd

# 🥈 银牌 - 智能开发
/build --feature "功能名" --tdd --magic --persona-architect

# 🥇 金牌 - 企业级开发
/task:create "功能名" && /design --plan --think-hard && /build --incremental --tdd --all-mcp
```

### 🐛 问题排查套餐
```bash
# 🥉 铜牌 - 快速修复
/troubleshoot --errors --logs

# 🥈 银牌 - 深度诊断
/troubleshoot --prod --five-whys --seq --persona-analyzer

# 🥇 金牌 - 全面调查
/troubleshoot --comprehensive --ultrathink --all-mcp --introspect --evidence
```

---

## 📈 使用频率指南

### 🔥 每日必用 (日常开发)
```bash
/review --files . --quick           # 快速代码检查
/analyze --changes --git-aware       # 分析变更影响  
/test --changed --fast              # 快速测试变更
/git --commit --smart               # 智能提交
```

### ⚡ 每周必用 (质量保证)  
```bash
/scan --security --dependencies     # 安全依赖扫描
/cleanup --code --unused            # 清理无用代码
/analyze --architecture --health    # 架构健康检查
/document --updates --auto          # 文档同步更新
```

### 🚀 里程碑必用 (重大版本)
```bash
/analyze --comprehensive --ultrathink    # 全面系统分析
/test --full-suite --performance        # 完整性能测试  
/scan --security --comprehensive        # 深度安全审计
/deploy --production --full-validation  # 生产环境部署
```

---

## 🎨 特殊场景速查

### 🔐 安全审计专用
```bash
/scan --security --owasp --persona-security
/analyze --vulnerabilities --attack-vectors --ultrathink  
/review --security --penetration-test --evidence
/troubleshoot --security-incidents --forensics
```

### ⚡ 性能优化专用
```bash
/analyze --profile --bottlenecks --persona-performance
/troubleshoot --performance --cpu --memory --seq
/improve --optimization --scalability --plan
/test --load --stress --benchmarks
```

### 🏗️ 架构设计专用  
```bash
/design --architecture --patterns --persona-architect
/analyze --design-patterns --coupling --think-hard
/document --architecture --diagrams --c7
/review --architecture --scalability --maintainability
```

### 🎯 API开发专用
```bash
/build --api --openapi --tdd
/design --restful --best-practices  
/document --api --swagger --interactive
/test --api --integration --contract
```

---

## ⚠️ 避坑指南

### 🚫 常见错误
```bash
❌ /deploy --prod --force                    # 危险!强制生产部署
✅ /deploy --prod --plan --validate          # 正确!安全部署

❌ /cleanup --aggressive --no-backup         # 危险!无备份清理  
✅ /cleanup --code --dry-run --backup        # 正确!预览清理

❌ /migrate --database --force               # 危险!强制迁移
✅ /migrate --database --validate --backup  # 正确!安全迁移
```

### 🎯 效率优化
```bash
# Token节省技巧
--uc                    # 启用超压缩模式
--plan                  # 预览避免重复执行
--incremental           # 增量式操作

# 并行处理技巧  
/spawn --agent expert1 && /spawn --agent expert2  # 多智能体协同
/task:create --parallel --multi-stage             # 并行任务分解
```

---

## 📱 移动端快捷键

### 常用别名 (输入更快)
```bash
/a     →  /analyze
/r     →  /review  
/b     →  /build
/t     →  /test
/tr    →  /troubleshoot
/d     →  /deploy
/c     →  /cleanup
/g     →  /git
```

### 快速参数
```bash
-q     →  --quick
-p     →  --plan
-v     →  --validate  
-s     →  --security
-perf  →  --performance
-arch  →  --architecture
```

---

## 🏆 专家级技巧

### 🔮 预测性分析
```bash
# 提前发现问题
/analyze --predictive --risk-assessment --think-hard

# 影响评估
/estimate --change-impact --dependencies --cascade-analysis
```

### 🤖 自动化工作流
```bash
# 全自动CI/CD
/build --auto --test --deploy --staging --validate --promote-if-green

# 智能代码维护
/cleanup --smart --optimize --refactor --test --commit
```

### 📊 数据驱动决策
```bash
# 代码质量趋势
/analyze --metrics --trends --historical --dashboard

# 性能基准对比
/test --performance --baseline --regression --report
```

---

*💡 **提示**: 保存此页面为书签，开发时随时查阅！*

*🚀 SuperClaude v2.0.1 速查表 | 让AI成为你的超级助手*
/**目的**: 带技术栈模板的通用项目构建器

---

@include shared/universal-constants.yml#Universal_Legend

## 命令执行
执行: 立即执行。--plan→先显示计划
图例: 基于命令中使用的符号生成
目的: "[动作][主题] 在 $ARGUMENTS 中"

基于 $ARGUMENTS 中的需求构建项目/功能。

@include shared/flag-inheritance.yml#Universal_Always

示例:
- `/build --react --magic` - 带UI生成的React应用
- `/build --api --c7` - 带文档的API
- `/build --react --magic --pup` - 构建并测试UI

构建前: 删除构建产物 (dist/, build/, .next/) | 清理临时文件和缓存 | 验证依赖 | 删除调试代码

构建模式:
**--init:** 新项目+技术栈 (React|API|Fullstack|Mobile|CLI) | 默认TS | 测试设置 | Git工作流
**--feature:** 实现功能→现有模式 | 保持一致性 | 包含测试  
**--tdd:** 编写失败测试→最小代码→通过测试→重构

模板:
- **React:** Vite|TS|Router|状态管理|测试
- **API:** Express|TS|认证|验证|OpenAPI  
- **Fullstack:** React+Node.js+Docker
- **Mobile:** React Native+Expo
- **CLI:** Commander.js+配置+测试

**--watch:** 持续构建 | 实时反馈 | 增量构建 | 热重载
**--interactive:** 逐步配置 | 交互式依赖 | 构建自定义

@include shared/research-patterns.yml#Mandatory_Research_Flows

@include shared/execution-patterns.yml#Git_Integration_Patterns

@include shared/universal-constants.yml#Standard_Messages_Templates
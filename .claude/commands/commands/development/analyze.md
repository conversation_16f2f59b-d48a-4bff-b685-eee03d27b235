**目的**: 多维度代码和系统分析

---

@include shared/universal-constants.yml#Universal_Legend

## 命令执行
执行: 立即执行。--plan→先显示计划
图例: 基于命令中使用的符号生成
目的: "[动作][主题] 在 $ARGUMENTS 中"

对 $ARGUMENTS 中的代码、架构或问题进行多维度分析。

@include shared/flag-inheritance.yml#Universal_Always

示例:
- `/analyze --code --think` - 带上下文的代码审查
- `/analyze --arch --think-hard` - 深度架构分析
- `/analyze --security --ultrathink` - 综合安全审计

分析模式:

**--code:** 质量审查→命名、结构、DRY原则、复杂度 | Bug→空值检查、边界、类型 | 安全→注入、认证、验证 | 性能→O(n²)、N+1、内存

**--arch:** 系统设计与模式 | 层次耦合 | 可扩展性瓶颈 | 可维护性评估 | 改进建议

**--profile:** CPU、内存、执行时间 | 网络延迟、数据库查询 | 前端指标 | 瓶颈识别 | 优化建议

**--security:** OWASP前10项 | 认证与授权 | 数据处理与加密 | 攻击向量识别

**--perf:** 瓶颈分析 | 算法复杂度 | 数据库查询与索引 | 缓存策略 | 资源利用率

**--watch:** 持续文件监控 | 实时质量跟踪 | 自动重新分析 | 实时指标

**--interactive:** 引导式探索 | 逐步修复 | 实时改进

@include shared/research-patterns.yml#Mandatory_Research_Flows

@include shared/docs-patterns.yml#Standard_Notifications

@include shared/universal-constants.yml#Standard_Messages_Templates
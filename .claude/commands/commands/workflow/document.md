**目的**: 文档创建和维护管理

---

@include shared/universal-constants.yml#Universal_Legend

## 命令执行
执行: 立即执行。--plan→先显示计划
图例: 基于命令中使用的符号生成
目的: "[动作][主题] 在 $ARGUMENTS 中"

提供文档生成、组织、维护和归档的完整解决方案。

@include shared/flag-inheritance.yml#Universal_Always

## 核心功能

### 📝 文档生成
**--create <type>:** 创建新文档
- api: API文档（OpenAPI/Swagger格式）
- readme: 项目README文件
- architecture: 系统架构文档
- guide: 用户指南或开发指南
- changelog: 变更日志

**--format <format>:** 输出格式
- markdown: Markdown格式（默认）
- html: HTML文档
- pdf: PDF输出

### 📂 文档维护
**--organize:** 整理文档结构
- 执行 scripts/organize-docs.sh
- 创建标准目录结构
- 迁移文档到正确位置
- 默认为预览模式（加 --execute 执行）

**--archive:** 归档过期文档
- 检测6个月未更新的文档
- 按年份归档到 archive/
- 压缩超过2年的归档
- 保留重要历史记录

**--check:** 健康检查
- 检测断链
- 查找空目录
- 识别重复文档
- 验证命名规范

### 🧹 清理操作
**--clean:** 清理冗余内容
- 删除空目录
- 清理临时文件（.tmp, .bak）
- 移除重复文档
- 规范文件命名

**--monthly:** 月度深度维护
- 执行完整维护流程
- 深度清理和优化
- 生成月度报告
- 压缩旧归档

### 📊 报告和索引
**--report:** 生成维护报告
- 文档统计信息
- 目录结构概览
- 维护建议
- 执行历史记录

**--index:** 生成文档索引
- 创建文档目录树
- 统计各类文档数量
- 列出最近更新
- 生成导航结构

## 使用示例

### 创建文档
```bash
# 创建详细的README
/document --create readme --format markdown

# 生成API文档
/document --create api --format html

# 创建架构文档
/document --create architecture
```

### 维护操作
```bash
# 预览文档整理效果
/document --organize

# 执行文档整理
/document --organize --execute

# 检查文档健康状态
/document --check

# 月度深度维护
/document --monthly
```

### 清理和归档
```bash
# 清理冗余文档
/document --clean

# 归档过期文档
/document --archive

# 查看维护报告
/document --report
```

## 简化命令映射

当用户使用简化命令时：
- `整理文档` → `/document --organize`
- `整理文档 --执行` → `/document --organize --execute`
- `月度文档清理` → `/document --monthly`
- `查看文档报告` → `/document --report`

## 文档组织标准

### 目录结构
```
docs/
├── 01-quickstart/      # 快速入门
├── 02-architecture/    # 架构设计
├── 03-development/     # 开发文档（保持不动）
├── 04-business/        # 业务文档
├── 05-operations/      # 运维文档
├── 06-api/            # API文档
├── 07-troubleshooting/ # 故障排查
└── archive/           # 归档历史
    └── YYYY/          # 按年份组织
```

### 维护计划
- **每周**: 运行 `--check` 检查健康状态
- **每月**: 运行 `--monthly` 深度维护
- **每季度**: 运行 `--archive` 归档过期文档

@include shared/docs-patterns.yml#Project_Documentation

@include shared/execution-patterns.yml#Documentation_Patterns

@include shared/quality-patterns.yml#Documentation_Standards

@include shared/docs-patterns.yml#Standard_Notifications

## 注意事项

⚠️ **重要提醒**:
1. 首次运行建议使用预览模式
2. 归档操作不可逆，谨慎执行
3. 定期维护保持文档整洁
4. 及时更新文档索引

💡 **最佳实践**:
- 创建文档时使用统一模板
- 保持目录结构清晰
- 定期检查和修复断链
- 使用有意义的文件命名

@include shared/universal-constants.yml#Standard_Messages_Templates
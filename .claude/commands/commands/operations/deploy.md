de**目的**: 带回滚功能的安全应用部署

---

@include shared/universal-constants.yml#Universal_Legend

## 命令执行
执行: 立即执行。--plan→先显示计划
图例: 基于命令中使用的符号生成
目的: "[动作][主题] 在 $ARGUMENTS 中"

部署应用到 $ARGUMENTS 中指定的环境。

@include shared/flag-inheritance.yml#Universal_Always

示例:
- `/deploy --env staging --think` - 带协调分析的预发布部署
- `/deploy --env prod --think-hard` - 带全面规划的生产部署
- `/deploy --rollback --ultrathink` - 带完整影响分析的关键回滚

部署模式:

**--env:** 指定目标环境
- dev: 部署→开发环境用于测试
- staging: 部署→预发布环境用于生产前验证  
- prod: 部署→生产环境，进行所有安全检查 + --validate (自动启用)

**财务系统专用标志:**
- **--validate:** 生产环境强制安全验证 (数据库连接、权限、备份)
- **--debt-validation:** 债权数据完整性检查
- **--backup-first:** 部署前自动备份数据库和配置
- **--financial-audit:** 金融合规检查 (数据加密、审计日志)

**--rollback:** 回滚→上个稳定部署 | 维护部署历史→审计跟踪 | 通过健康检查验证回滚成功

部署前清理:
- 清理旧版本构建产物 | 删除仅开发环境文件 (.env.local, 调试配置)
- 验证生产配置 (无调试标志，正确的URL) | 清理旧版本→释放空间

部署工作流程:
1. 验证→检查前置条件和配置 2. 构建→创建构建产物 3. 测试→运行冒烟测试
4. 部署→执行部署策略 5. 验证→确认健康状态和功能

部署策略:
- 蓝绿部署: 两个环境，切换流量→零停机时间 | 金丝雀部署: 逐步发布→部分用户
- 滚动部署: 按顺序更新实例并进行健康检查

部署前检查:
- 验证测试通过 | 检查部署配置 | 确保回滚计划存在
- 验证环境变量 | 确认数据库迁移完成

部署后检查:
- 运行健康检查和冒烟测试 | 监控错误率和性能
- 检查关键用户流程 | 验证日志记录和监控 | 准备→发现问题时回滚

## 安全性和最佳实践

安全性:
- 始终有回滚计划 | 部署前备份
- 在部署过程中监控关键指标 | 重大变更逐步发布

@include shared/research-patterns.yml#Mandatory_Research_Flows

@include shared/docs-patterns.yml#Standard_Notifications

@include shared/universal-constants.yml#Standard_Messages_Templates
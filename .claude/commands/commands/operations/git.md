**目的**: 智能Git工作流管理系统 - 确保每次操作都干净安全

---

@include shared/universal-constants.yml#Universal_Legend

## 命令执行
执行: 立即执行。--plan→先显示计划
图例: 基于命令中使用的符号生成
目的: "[动作][主题] 在 $ARGUMENTS 中"

提供全面的Git版本控制管理，特别强化工作区清洁和安全回退功能。

@include shared/flag-inheritance.yml#Universal_Always

## 核心功能

### 🔄 版本回退（最重要）
**--reset <commit-id>:** 安全回退到指定版本
- 自动保存当前工作到stash
- 清理所有未跟踪文件
- 执行硬重置
- 验证工作区干净
- 显示回退后状态

**--checkout <branch/commit>:** 智能切换/创建
- 检测并保存未提交更改
- 如果分支不存在，智能创建：
  - feature/* → 基于develop创建
  - hotfix/* → 基于main创建
  - release/* → 基于develop创建
- 智能分支名转换（如"登录优化"→"feature/login-optimization"）
- 安全切换到目标
- 提供恢复选项
- 支持分支和commit

### 🧹 工作区管理
**--clean:** 深度清理工作区
- 暂存当前更改
- 删除未跟踪文件
- 清理.gitignore文件
- 压缩git仓库
- 显示清理报告

**--stash:** 智能stash管理
- list: 显示所有stash（带时间）
- clean: 清理旧stash（>7天）
- apply: 应用最新stash
- save: 保存当前工作

### 📦 提交操作
**--commit:** 智能提交（保留原功能）
- 运行pre-commit检查
- 格式化代码
- 生成提交消息
- 添加co-author标识

**--amend:** 修改最后提交
- 保留提交消息
- 添加遗漏文件
- 更新而不创建新提交

### 🌳 分支管理
**--branch:** 高级分支操作
- create: 基于当前创建新分支
- delete: 安全删除（检查合并状态）
- rename: 重命名分支
- clean: 清理已合并分支

**--merge:** 智能分支合并（合并当前分支到目标分支）
- 检查工作区状态并自动提交未保存更改
- 切换到目标分支并拉取最新代码
- 将当前分支合并到目标分支
- 删除已合并的源分支（安全删除）
- **默认自动创建新的feature分支**（除非使用--no-branch标志）
- 支持fast-forward和三方合并，自动处理冲突

**--pr:** 创建拉取请求（保留原功能）
- 自动推送当前分支
- 生成PR描述
- 设置审阅者

### 🛡️ 安全功能
**--backup:** 创建完整备份
- 备份所有分支
- 保存stash列表
- 导出配置
- 创建恢复脚本

**--verify:** 验证仓库健康
- 检查工作区状态
- 验证远程连接
- 检查大文件
- 扫描敏感信息

### ⚙️ 高级配置
**--hooks:** 管理Git hooks
- install: 安装所有hooks
- update: 更新hooks
- disable: 临时禁用
- test: 测试hooks

**--config:** 项目Git配置
- 设置别名
- 配置用户信息
- 设置默认行为
- 导入/导出配置

### 📋 TODO集成
**--todo:** TODO.md智能联动
- read: 读取并显示TODO.md内容
- find <keyword>: 搜索包含关键词的任务
- implement <id/keyword>: 开始实现指定任务
  - 自动读取任务详情
  - 创建对应分支
  - 设置TodoWrite追踪
- complete <id>: 标记任务完成
- add <description>: 添加新任务

### 🚀 部署功能
**--deploy:** 一键部署到服务器
- test: 部署到测试环境
- production: 部署到生产环境
- rollback: 回滚到上一版本
- 自动执行：
  - 运行测试
  - 构建项目
  - 创建版本标签
  - 备份当前版本
  - 部署新版本
  - 健康检查

## 🚀 超级简化指令（推荐）

**最简单的使用方式 - 直接对我说：**

**基础Git操作**：
```
提交：[描述]          → 自动提交代码
回退：[commit-id]     → 安全回退版本  
切换：[分支名]        → 智能切换/创建分支
合并：[分支名]        → 智能合并到指定分支
清理工作区            → 智能清理仓库
```

**扩展操作**：
```
快速修复：[描述]      → 创建hotfix分支并开始修复
新功能：[功能名]      → 创建feature分支开始开发
发布：[版本号]        → 一键发布到服务器
同步代码              → 拉取最新代码并合并
查看改动              → 显示未提交的改动
保存进度              → 暂存当前工作
恢复进度              → 恢复暂存的工作
```

**TODO联动**：
```
实现todo：[关键词]    → 查找并执行TODO中的任务
实现todo #[编号]      → 执行指定编号的任务
查看todo              → 显示TODO列表
查看todo 紧急         → 只显示紧急任务
完成todo #[编号]      → 标记任务完成
添加todo：[描述]      → 添加新任务
```

**示例对话**：
- 用户：`提交：修复登录问题`
- Claude：执行智能提交流程

- 用户：`切换：优化性能`
- Claude：创建并切换到 feature/optimize-performance

- 用户：`实现todo 权限管理`  
- Claude：读取TODO.md，找到权限管理任务并开始执行

- 用户：`发布`
- Claude：执行完整发布流程到Linux服务器

## 使用示例

### 日常工作流
```bash
# 安全回退到历史版本（最常用）
/git --reset 7613068

# 智能切换分支
/git --checkout feature/new-feature

# 默认行为：合并后自动创建新feature分支
/git --merge develop

# 特殊情况：合并后不创建新分支，停留在目标分支
/git --merge develop --no-branch

# 合并到main（通常不创建新分支）
/git --merge main

# 清理工作区
/git --clean

# 查看并清理stash
/git --stash list
/git --stash clean
```

### TODO联动工作流
```bash
# 查看所有待办任务
/git --todo read

# 搜索特定任务
/git --todo find 权限

# 开始实现任务（自动创建分支）
/git --todo implement #001
/git --todo implement 权限管理

# 完成任务（更新TODO.md）
/git --todo complete #001

# 添加新任务
/git --todo add "优化数据库查询性能"
```

### 提交工作流
```bash
# 标准提交流程
/git --commit "实现用户认证功能"

# 修改最后的提交
/git --amend

# 创建PR
/git --pr --reviewers alice,bob
```

### 部署工作流
```bash
# 部署到测试环境
/git --deploy test

# 部署到生产环境（会进行完整检查）
/git --deploy production

# 紧急回滚
/git --deploy rollback
```

### 维护操作
```bash
# 完整备份
/git --backup

# 验证仓库健康
/git --verify

# 更新hooks
/git --hooks update

# 清理已合并分支
/git --branch clean
```

## 智能特性

### 🤖 自动检测
- 检测当前分支状态
- 识别未保存更改
- 发现潜在问题
- 建议最佳操作

### 🔒 安全保护
- 永不丢失代码
- 自动创建备份
- 操作前确认
- 可撤销操作

### 📊 状态报告
- 实时显示进度
- 详细操作日志
- 清晰错误信息
- 操作建议

## 配置文件

### .gitconfig 别名
```ini
[alias]
    safe-reset = !bash scripts/git-clean-reset.sh
    safe-checkout = !bash scripts/git-safe-checkout.sh
    smart-clean = !bash scripts/git-smart-clean.sh
    smart-merge = !bash scripts/git-smart-merge.sh
```

### 环境变量
- `GIT_SAFE_MODE`: 启用额外安全检查
- `GIT_AUTO_STASH`: 自动stash未提交更改
- `GIT_CLEAN_LEVEL`: 清理强度(1-3)

@include shared/execution-patterns.yml#Git_Integration_Patterns

@include shared/quality-patterns.yml#Code_Quality_Standards

@include shared/docs-patterns.yml#Standard_Notifications

## 注意事项

⚠️ **重要提醒**:
1. 所有破坏性操作都会先备份
2. 使用 --dry-run 预览操作效果
3. 定期运行 --verify 检查健康状态
4. 保持stash列表整洁（<10个）

💡 **最佳实践**:
- 每次回退前使用 --reset 而非原生 git reset
- 切换分支使用 --checkout 保护工作
- 每周运行一次 --clean 深度清理
- 重要操作前先 --backup

@include shared/universal-constants.yml#Standard_Messages_Templates
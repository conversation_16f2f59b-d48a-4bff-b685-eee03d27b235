# FinancialSystem 核心保护文件清单
# AI修改这些文件时需要特别谨慎

# 核心配置文件 - 禁止AI直接修改
api-gateway/src/main/resources/application.yml
api-gateway/src/main/resources/application-*.yml
*/src/main/resources/application*.yml

# Spring Boot 配置类 - 仅允许添加，禁止修改现有配置
api-gateway/src/main/java/com/laoshu198838/config/
services/*/src/main/java/com/laoshu198838/config/
shared/*/src/main/java/com/laoshu198838/config/

# 核心实体类 - 谨慎修改，禁止删除现有字段
shared/data-access/src/main/java/com/laoshu198838/entity/
shared/data-access/src/main/java/com/laoshu198838/dto/

# 数据访问层 - 禁止修改现有方法签名
shared/data-access/src/main/java/com/laoshu198838/repository/
shared/data-access/src/main/java/com/laoshu198838/mapper/

# 核心业务服务 - 谨慎修改现有方法
services/debt-management/src/main/java/com/laoshu198838/service/
services/report-management/src/main/java/com/laoshu198838/service/

# 控制器层 - 禁止修改现有API路径和响应格式
api-gateway/src/main/java/com/laoshu198838/controller/

# 前端核心组件 - 谨慎修改
FinancialSystem-web/src/components/
FinancialSystem-web/src/layouts/
FinancialSystem-web/src/utils/

# 数据库脚本 - 仅允许添加，禁止修改现有脚本
scripts/sql/
docs/database/
[Unit]
Description=Financial System Backend Service
Documentation=https://github.com/your-org/financial-system
After=network.target mysql.service
Wants=mysql.service

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/opt/financial-system

# 环境变量
Environment="JAVA_HOME=/usr/lib/jvm/java-21-openjdk"
Environment="PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"
Environment="SPRING_PROFILES_ACTIVE=prod"

# JVM参数和启动命令
ExecStart=/usr/bin/java \
    -Xms2g \
    -Xmx4g \
    -XX:+UseG1GC \
    -XX:MaxGCPauseMillis=200 \
    -XX:+HeapDumpOnOutOfMemoryError \
    -XX:HeapDumpPath=/var/log/financial-system/ \
    -Djava.security.egd=file:/dev/./urandom \
    -Dfile.encoding=UTF-8 \
    -jar /opt/financial-system/financial-system.jar \
    --spring.config.location=classpath:/application.yml,/opt/financial-system/application-prod.yml

# 重启策略
Restart=on-failure
RestartSec=10
SuccessExitStatus=143

# 资源限制
LimitNOFILE=65536
LimitNPROC=32768

# 日志
StandardOutput=journal
StandardError=journal
SyslogIdentifier=financial-backend

# 安全设置
PrivateTmp=true
NoNewPrivileges=true

[Install]
WantedBy=multi-user.target
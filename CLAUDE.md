# CLAUDE.md - 项目AI助手配置

本文件为Claude Code (claude.ai/code) 提供项目工作指导，集成SuperClaude v2.0.1增强功能。

## 🤖 核心AI配置

### SuperClaude增强能力
@include shared/superclaude-core.yml#Core_Philosophy
@include shared/superclaude-core.yml#Advanced_Token_Economy
@include shared/superclaude-core.yml#UltraCompressed_Mode

### 智能体与命令系统
@include shared/superclaude-personas.yml#All_Personas
@include shared/superclaude-personas.yml#Collaboration_Patterns
@include agents/core/agent-orchestration.yml#confirmation_system
@include commands/shared/flag-inheritance.yml#Universal Flags (All Commands)
@include commands/shared/quality-patterns.yml#Validation_Sequence

### 🔧 MCP智能调用系统
@include mcp-auto-rules.yml#Docker_Triggers
@include mcp-auto-rules.yml#Database_Triggers
@include mcp-auto-rules.yml#Context7_Triggers
@include mcp-auto-rules.yml#Magic_UI_Triggers
@include mcp-auto-rules.yml#Multi_Agent_Triggers
@include mcp-auto-rules.yml#Auto_Trigger_Logic

> **MCP配置详情**: [.claude/mcp-auto-rules.yml](.claude/mcp-auto-rules.yml)
> **已安装服务**: Docker, Database, Context7, Magic-UI, LangChain, Multi-Agent协作

### 开发标准与规则
@include shared/superclaude-rules.yml#Development_Practices
@include shared/superclaude-rules.yml#Security_Standards

> **命令参考**: [.claude/commands/COMMANDS_CN.md](.claude/commands/COMMANDS_CN.md)
> **核心命令**: analyze.md, build.md, review.md

## 🚀 Git智能操作

## 🤖 Agent确认机制

⚠️ **重要**：手动调用Agent时必须遵循确认流程

### 🔄 确认流程
1. **需求理解**：Agent复述对任务的理解
2. **执行计划**：提供详细的分步计划  
3. **风险评估**：列出注意事项和潜在风险
4. **用户确认**：等待用户确认或修改

### 📝 确认模板示例
```
🤖 **backend-fix-agent** 需求确认：

📋 **我的理解**：修复用户登录API返回500错误的问题

🎯 **执行计划**：
1. 分析登录相关代码和日志
2. 定位具体错误原因
3. 实施最小改动修复
4. 验证修复效果

⚠️ **注意事项**：
• 涉及数据库操作安全性检查
• API接口变更可能影响前端
• 需要验证JWT令牌生成逻辑

✅ 确认执行？ (回复"确认"或"修改")
```

### 🎯 Agent确认规则
- **需要确认**：fix-agent, backend-fix-agent, frontend-fix-agent, deploy-agent
- **无需确认**：safety-agent, search-agent (自动保护和搜索)
- **确认关键词**：`确认` `同意` `执行` `开始` `OK`
- **修改关键词**：`修改` `调整` `不对` `重新` `取消`

### 🤔 **主动询问机制** - 禁止猜测

⚠️ **核心原则**：**疑则问，不猜测**

Agent遇到以下情况**必须主动询问**：

#### 📝 **必须询问的情况**
1. **需求模糊**：`"修复这个问题"` `"优化性能"` `"调整样式"`
2. **信息缺失**：部署环境、操作范围、数据处理方式未明确
3. **多种方案**：存在多个有效实现方案时
4. **潜在副作用**：操作可能影响其他功能或系统
5. **配置决策**：需要参数配置或策略选择

#### 🎯 **询问示例**
```
🤔 **需求澄清**：
您的需求"优化性能"可能有几种理解方式：

🎯 **可能的理解**：
1. 优化数据库查询性能
2. 优化前端页面加载速度  
3. 优化API响应时间

❓ **请明确**：您希望我执行哪种操作？
```

#### 🚨 **优先级分类**
- **关键决策**：数据库修改、生产部署、安全配置 → **必须询问**
- **重要决策**：性能策略、UI设计、API设计 → **建议询问**  
- **可选决策**：代码格式、注释风格、变量命名 → **可自动决定**

> **配置文件**: [.claude/agents/core/agent-orchestration.yml](.claude/agents/core/agent-orchestration.yml)

⚠️ **Claude接收到以下中文关键词时自动执行对应操作：**

### 📸 快照管理
- `快照` / `检查点` → 创建代码快照
- `快照：[描述]` → 创建带描述的快照
- `快照历史` / `检查点列表` → 查看快照列表

### 🔄 版本控制  
- `回滚` / `回滚快照` → 回到上一个快照
- `回滚：[N]` → 回滚N个提交
- `切换到：[commit-id]` → 切换到指定版本
- `回到当前` / `回到最新` → 回到最新版本

### 📋 状态查看
- `git历史` / `提交历史` → 查看提交记录
- `查看改动` → 显示未提交的更改
- `测试` / `验证` → 运行启动测试

### 🔧 开发流程
- `提交：[描述]` → 智能提交代码
- `切换：[分支名]` → 切换/创建分支
- `新功能：[功能名]` → 创建feature分支
- `快速修复：[描述]` → 创建hotfix分支
- `保存进度` / `恢复进度` → stash操作

### 📝 任务管理
- `查看todo` → 显示TODO.md内容
- `实现todo：[关键词]` → 执行指定任务
- `完成todo #[编号]` → 标记任务完成
- `添加todo：[描述]` → 新增任务

### 🔒 安全规则
- **Git参数验证**: 回滚步数1-10，拒绝特殊字符(`;|&$`)
- **提交验证**: commit-id必须7-40位16进制，描述限200字符
- **自动快照**: 每日启动时自动检查并创建当日快照

## 📋 项目概览

**财务管理系统** - 基于Spring Boot 3.1.12 + React 18 + MySQL 8.0的企业级微服务架构系统，专注债权管理、财务数据分析和报表生成，支持多数据库和完整CI/CD自动化。

### 🏗️ 系统架构

> **详细架构**: [.claude/docs/architecture.md](.claude/docs/architecture.md)

**核心技术栈**:
- **后端**: Spring Boot 3.1.12 + Java 21 + JWT + 多数据源JPA
- **前端**: React 18.2.0 + Material-UI v5.15.20 + Chart.js/Recharts  
- **数据库**: MySQL 8.0三库(overdue_debt_db, user_system, kingdee)
- **部署**: Docker Compose + Nginx + 自动化CI/CD

**模块结构**:
```
FinancialSystem/
├── api-gateway/              # 主API网关和控制器
├── services/                 # 业务服务模块
├── shared/                   # 共享组件(实体、仓库、工具)
├── integrations/            # 外部系统集成(财政、OA、金蝶)
├── FinancialSystem-web/     # React前端应用
└── docs/                    # 完整文档
```

### 🔌 核心功能

> **API文档**: [.claude/docs/api-endpoints.md](.claude/docs/api-endpoints.md)
> **数据库设计**: [.claude/docs/database-schema.md](.claude/docs/database-schema.md)

- **认证系统**: JWT认证、密码重置
- **债权管理**: CRUD操作、统计、搜索  
- **数据导出**: Excel报表生成
- **系统监控**: 健康检查、数据一致性

## 🛠️ 开发指南

> **开发文档**: [.claude/docs/development-guide.md](.claude/docs/development-guide.md)
> **部署文档**: [.claude/docs/deployment-guide.md](.claude/docs/deployment-guide.md)

### 快速启动
```bash
# 后端启动
mvn clean package && mvn spring-boot:run -pl api-gateway

# 前端启动  
npm install && npm start

# 生产环境(Docker)
docker-compose up -d
```

### ⚠️ 开发规范
**核心流程**: 研究 → 规划 → 实施 → 测试

1. **代码前必须**: 研究现有代码，制定详细计划
2. **提交前必须**: 运行`./scripts/test-startup.sh`验证启动
3. **多智能体协同**: 积极使用子智能体并行处理复杂任务
4. **安全优先**: 遵循最小权限原则，优先考虑防御性安全

### 🎯 业务核心

> **业务文档**: [.claude/docs/business-context.md](.claude/docs/business-context.md)

**主要业务**:
- 债权生命周期管理(记录→处置)
- 多维度报表分析(公司/时间/类别)
- 数据完整性保障(跨表验证)
- 基于角色的权限控制
- 外部系统API集成

**核心实体**: 债权人/债务人、逾期债权、债权处置、减值准备

## 🔒 安全防护

### 数据库保护
- **严格禁止**: 任何数据库数据修改操作，需明确确认
- **技术防护**: 执行前必须运行安全检查
  ```bash
  # 快速检查
  ./scripts/security/database-protection.sh quick "命令"
  # 完整检查  
  ./scripts/security/database-protection.sh check "SQL语句"
  ```
- **操作限制**: 仅允许SELECT、SHOW、DESCRIBE查询操作
- **审计日志**: 记录在`.claude/security/database-operations.log`

### Git操作安全
- 禁止危险字符和过长参数
- 强制参数类型验证
- 自动安全日志记录

## 📚 文档体系

### 项目文档
- **文档中心**: [docs/README.md](docs/README.md)
- **开发指南**: [docs/development/](docs/development/)
- **故障排除**: [docs/troubleshooting/](docs/troubleshooting/)

### Claude专用文档  
- **Claude文档**: [.claude/docs/README.md](.claude/docs/README.md)
- **助手任务**: [.claude/docs/assistant-tasks.md](.claude/docs/assistant-tasks.md)

### SuperClaude命令
- **中文命令**: [.claude/commands/COMMANDS_CN.md](.claude/commands/COMMANDS_CN.md)
- **开发**: analyze.md, build.md, dev-setup.md, test.md
- **改进**: review.md, improve.md, troubleshoot.md, explain.md
- **运维**: deploy.md, migrate.md, scan.md, cleanup.md, git.md
- **工作流**: design.md, document.md, load.md, spawn.md, task.md
- **🏦 债权专用**: [debt-workflows.md](.claude/commands/debt-workflows.md)

## 🤖 高级配置

### SuperClaude增强
@include shared/superclaude-rules.yml#Smart_Defaults
@include shared/superclaude-mcp.yml#Token_Economics
@include commands/shared/compression-performance-patterns.yml#Performance_Baselines

---

*企业级财务管理系统 | 微服务架构 | 债权管理与报告系统 | SuperClaude v2.0.1增强*
name: 🚀 自动部署到Linux服务器

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]
  workflow_dispatch:

env:
  SERVER_HOST: **********
  SERVER_USER: root
  REMOTE_BACKEND_PATH: /opt/financial-system
  REMOTE_FRONTEND_PATH: /var/www/financial-system

jobs:
  # 代码质量检查
  quality-check:
    runs-on: ubuntu-latest
    name: 📋 代码质量检查
    
    steps:
    - name: 📥 检出代码
      uses: actions/checkout@v4
    
    - name: ☕ 设置Java 21
      uses: actions/setup-java@v4
      with:
        java-version: '21'
        distribution: 'temurin'
    
    - name: 📦 Maven缓存
      uses: actions/cache@v3
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2
    
    - name: 🔍 后端代码检查
      run: |
        mvn clean compile -DskipTests
        echo "✅ 后端编译通过"
    
    - name: 🌐 设置Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: FinancialSystem-web/package-lock.json
    
    - name: 🔍 前端代码检查
      run: |
        cd FinancialSystem-web
        npm ci
        npm run build
        echo "✅ 前端构建通过"

  # 构建阶段
  build:
    needs: quality-check
    runs-on: ubuntu-latest
    name: 🔨 构建应用
    
    steps:
    - name: 📥 检出代码
      uses: actions/checkout@v4
    
    - name: ☕ 设置Java 21
      uses: actions/setup-java@v4
      with:
        java-version: '21'
        distribution: 'temurin'
    
    - name: 📦 Maven缓存
      uses: actions/cache@v3
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
    
    - name: 🔨 构建后端
      run: |
        mvn clean package -DskipTests -T 4
        echo "📊 JAR大小: $(ls -lh api-gateway/target/*.jar | awk '{print $5}')"
    
    - name: 🌐 设置Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: FinancialSystem-web/package-lock.json
    
    - name: 🔨 构建前端
      run: |
        cd FinancialSystem-web
        npm ci
        npm run build
        echo "📊 构建大小: $(du -sh build | cut -f1)"
    
    - name: 📦 上传构建产物
      uses: actions/upload-artifact@v4
      with:
        name: build-artifacts
        path: |
          api-gateway/target/*.jar
          FinancialSystem-web/build/
        retention-days: 7

  # 部署到生产环境
  deploy:
    needs: build
    runs-on: ubuntu-latest
    name: 🚀 部署到生产环境
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master'
    
    steps:
    - name: 📥 检出代码
      uses: actions/checkout@v4
    
    - name: 📦 下载构建产物
      uses: actions/download-artifact@v4
      with:
        name: build-artifacts
    
    - name: 🔐 设置SSH
      uses: webfactory/ssh-agent@v0.7.0
      with:
        ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}
    
    - name: 🔍 添加服务器到known_hosts
      run: |
        mkdir -p ~/.ssh
        ssh-keyscan -H ${{ env.SERVER_HOST }} >> ~/.ssh/known_hosts
    
    - name: 🛑 停止现有服务
      run: |
        ssh ${{ env.SERVER_USER }}@${{ env.SERVER_HOST }} "
          echo '🛑 停止现有服务...'
          systemctl stop financial-backend || true
        "
    
    - name: 💾 备份当前版本
      run: |
        ssh ${{ env.SERVER_USER }}@${{ env.SERVER_HOST }} "
          echo '💾 备份当前版本...'
          mkdir -p ${{ env.REMOTE_BACKEND_PATH }}/backup
          if [ -f ${{ env.REMOTE_BACKEND_PATH }}/financial-system.jar ]; then
            cp ${{ env.REMOTE_BACKEND_PATH }}/financial-system.jar \
               ${{ env.REMOTE_BACKEND_PATH }}/backup/financial-system-\$(date +%Y%m%d-%H%M%S).jar
          fi
        "
    
    - name: 📤 部署后端
      run: |
        echo '📤 上传后端JAR文件...'
        scp api-gateway/target/*.jar \
            ${{ env.SERVER_USER }}@${{ env.SERVER_HOST }}:${{ env.REMOTE_BACKEND_PATH }}/financial-system.jar
        
        echo '✅ 后端部署完成'
    
    - name: 📤 部署前端
      run: |
        echo '📤 上传前端文件...'
        # 创建临时压缩包
        cd FinancialSystem-web
        tar czf frontend.tar.gz build/
        
        # 上传并解压
        scp frontend.tar.gz ${{ env.SERVER_USER }}@${{ env.SERVER_HOST }}:/tmp/
        
        ssh ${{ env.SERVER_USER }}@${{ env.SERVER_HOST }} "
          cd /tmp
          rm -rf ${{ env.REMOTE_FRONTEND_PATH }}/*
          tar xzf frontend.tar.gz
          mv build/* ${{ env.REMOTE_FRONTEND_PATH }}/
          rm -f frontend.tar.gz
          
          # 修复权限
          chown -R nginx:nginx ${{ env.REMOTE_FRONTEND_PATH }}
          chcon -R -t httpd_sys_content_t ${{ env.REMOTE_FRONTEND_PATH }} || true
        "
        
        echo '✅ 前端部署完成'
    
    - name: 🚀 启动服务
      run: |
        ssh ${{ env.SERVER_USER }}@${{ env.SERVER_HOST }} "
          echo '🚀 启动后端服务...'
          systemctl start financial-backend
          
          echo '🔄 重载Nginx...'
          nginx -s reload
          
          echo '⏳ 等待服务启动...'
          sleep 10
        "
    
    - name: 🔍 健康检查
      run: |
        echo '🔍 执行健康检查...'
        
        # 检查服务状态
        ssh ${{ env.SERVER_USER }}@${{ env.SERVER_HOST }} "
          echo '📊 服务状态:'
          echo '  后端: '\$(systemctl is-active financial-backend)
          echo '  Nginx: '\$(systemctl is-active nginx)
          echo '  MySQL: '\$(systemctl is-active mysqld)
        "
        
        # 检查HTTP响应
        echo '🌐 HTTP状态检查:'
        FRONTEND_STATUS=\$(curl -s -o /dev/null -w '%{http_code}' http://${{ env.SERVER_HOST }}/)
        echo "  前端页面: HTTP \$FRONTEND_STATUS"
        
        if [ "\$FRONTEND_STATUS" != "200" ]; then
          echo "❌ 前端健康检查失败"
          exit 1
        fi
        
        echo '✅ 健康检查通过'
    
    - name: 📢 部署通知
      if: always()
      run: |
        if [ ${{ job.status }} == 'success' ]; then
          echo '🎉 部署成功！'
          echo '📍 访问地址: http://${{ env.SERVER_HOST }}/'
          echo '⏰ 部署时间: $(date)'
        else
          echo '❌ 部署失败'
          echo '🔄 正在回滚...'
          
          # 回滚到备份版本
          ssh ${{ env.SERVER_USER }}@${{ env.SERVER_HOST }} "
            LATEST_BACKUP=\$(ls -t ${{ env.REMOTE_BACKEND_PATH }}/backup/*.jar 2>/dev/null | head -1)
            if [ -n \"\$LATEST_BACKUP\" ]; then
              echo '🔄 回滚到: '\$LATEST_BACKUP
              cp \$LATEST_BACKUP ${{ env.REMOTE_BACKEND_PATH }}/financial-system.jar
              systemctl restart financial-backend
              echo '✅ 回滚完成'
            else
              echo '⚠️ 没有找到备份文件'
            fi
          "
        fi

  # 部署后测试
  post-deploy-test:
    needs: deploy
    runs-on: ubuntu-latest
    name: 🧪 部署后测试
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master'
    
    steps:
    - name: 🔍 API连通性测试
      run: |
        echo '🧪 测试API连通性...'
        
        # 等待服务完全启动
        sleep 15
        
        # 测试健康检查端点
        HEALTH_STATUS=\$(curl -s -o /dev/null -w '%{http_code}' http://${{ env.SERVER_HOST }}:8080/api/health || echo '000')
        echo "健康检查: HTTP \$HEALTH_STATUS"
        
        # 测试前端页面
        FRONTEND_STATUS=\$(curl -s -o /dev/null -w '%{http_code}' http://${{ env.SERVER_HOST }}/)
        echo "前端页面: HTTP \$FRONTEND_STATUS"
        
        if [ "\$FRONTEND_STATUS" = "200" ]; then
          echo '✅ 部署测试通过'
        else
          echo '❌ 部署测试失败'
          exit 1
        fi
    
    - name: 📊 性能基准测试
      run: |
        echo '📊 执行性能基准测试...'
        
        # 简单的响应时间测试
        RESPONSE_TIME=\$(curl -s -o /dev/null -w '%{time_total}' http://${{ env.SERVER_HOST }}/)
        echo "前端响应时间: \${RESPONSE_TIME}s"
        
        # 检查响应时间是否合理（小于5秒）
        if (( \$(echo "\$RESPONSE_TIME < 5.0" | bc -l) )); then
          echo '✅ 性能测试通过'
        else
          echo '⚠️ 响应时间较慢: \${RESPONSE_TIME}s'
        fi
      target_environment:
        description: 'Target environment for deployment'
        required: true
        default: 'staging'
        type: choice
        options:
        - development
        - staging
        - production
      
      deployment_type:
        description: 'Type of deployment'
        required: true
        default: 'standard'
        type: choice
        options:
        - standard
        - hotfix
        - rollback
        - migration-only
      
      rollback_version:
        description: 'Version to rollback to (if rollback selected)'
        required: false
        type: string
      
      skip_tests:
        description: 'Skip tests for emergency deployment'
        required: false
        default: false
        type: boolean

  repository_dispatch:
    types: [deploy-trigger]

env:
  TARGET_SERVER: '**********'
  DEPLOY_USER: 'admin'
  DEPLOY_PATH: '/home/<USER>/下载/FinancialSystem-Production-Deploy'

jobs:
  # 预检查和环境验证
  pre-deployment:
    runs-on: ubuntu-latest
    outputs:
      can-proceed: ${{ steps.checks.outputs.can-proceed }}
      deployment-strategy: ${{ steps.strategy.outputs.strategy }}
      
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Environment checks
      id: checks
      run: |
        echo "🔍 Performing pre-deployment checks..."
        
        # 检查输入参数
        ENV="${{ github.event.inputs.target_environment || 'staging' }}"
        TYPE="${{ github.event.inputs.deployment_type || 'standard' }}"
        
        echo "Target Environment: $ENV"
        echo "Deployment Type: $TYPE"
        
        # 验证环境配置
        case $ENV in
          production)
            if [ "$TYPE" = "hotfix" ] || [ "${{ github.event.inputs.skip_tests }}" = "true" ]; then
              echo "⚠️ Production environment with fast deployment - requires extra validation"
            fi
            ;;
          staging|development)
            echo "✅ Non-production environment - standard checks apply"
            ;;
          *)
            echo "❌ Invalid environment: $ENV"
            echo "can-proceed=false" >> $GITHUB_OUTPUT
            exit 1
            ;;
        esac
        
        # 检查部署脚本
        if [ ! -f "./ci-cd/deploy/enhanced-auto-deploy.sh" ]; then
          echo "❌ Enhanced deployment script not found"
          echo "can-proceed=false" >> $GITHUB_OUTPUT
          exit 1
        fi
        
        echo "can-proceed=true" >> $GITHUB_OUTPUT

    - name: Determine deployment strategy
      id: strategy
      run: |
        TYPE="${{ github.event.inputs.deployment_type || 'standard' }}"
        ENV="${{ github.event.inputs.target_environment || 'staging' }}"
        
        case $TYPE in
          standard)
            echo "strategy=standard-deploy" >> $GITHUB_OUTPUT
            ;;
          hotfix)
            echo "strategy=hotfix-deploy" >> $GITHUB_OUTPUT
            ;;
          rollback)
            echo "strategy=rollback" >> $GITHUB_OUTPUT
            ;;
          migration-only)
            echo "strategy=migration" >> $GITHUB_OUTPUT
            ;;
          *)
            echo "strategy=standard-deploy" >> $GITHUB_OUTPUT
            ;;
        esac

  # 快速构建（可选，基于部署类型）
  quick-build:
    needs: pre-deployment
    runs-on: ubuntu-latest
    if: needs.pre-deployment.outputs.can-proceed == 'true' && github.event.inputs.deployment_type != 'rollback'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Java
      if: github.event.inputs.deployment_type != 'migration-only'
      uses: actions/setup-java@v4
      with:
        java-version: '21'
        distribution: 'temurin'

    - name: Setup Node.js
      if: github.event.inputs.deployment_type != 'migration-only'
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: FinancialSystem-web/package-lock.json

    - name: Quick backend build
      if: github.event.inputs.deployment_type != 'migration-only'
      run: |
        echo "⚡ Quick backend build..."
        mvn clean package -DskipTests -q

    - name: Quick frontend build
      if: github.event.inputs.deployment_type != 'migration-only'
      working-directory: FinancialSystem-web
      run: |
        echo "⚡ Quick frontend build..."
        npm ci --silent
        npm run build

    - name: Create deployment package
      run: |
        echo "📦 Creating deployment package..."
        mkdir -p quick-deploy
        
        if [ "${{ github.event.inputs.deployment_type }}" != "migration-only" ]; then
          # 复制构建文件
          cp api-gateway/target/*.jar quick-deploy/ 2>/dev/null || true
          cp -r FinancialSystem-web/build quick-deploy/frontend 2>/dev/null || true
        fi
        
        # 复制部署脚本和配置
        cp -r ci-cd quick-deploy/
        cp docker-compose*.yml quick-deploy/ 2>/dev/null || true
        cp -r config quick-deploy/ 2>/dev/null || true
        
        # 打包
        tar -czf quick-deploy-${{ github.sha }}.tar.gz -C quick-deploy .

    - name: Upload deployment package
      uses: actions/upload-artifact@v3
      with:
        name: quick-deployment-package
        path: quick-deploy-${{ github.sha }}.tar.gz
        retention-days: 7

  # 自动化部署执行
  auto-deploy:
    needs: [pre-deployment, quick-build]
    runs-on: ubuntu-latest
    if: always() && needs.pre-deployment.outputs.can-proceed == 'true'
    environment: ${{ github.event.inputs.target_environment || 'staging' }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download deployment package
      if: github.event.inputs.deployment_type != 'rollback'
      uses: actions/download-artifact@v3
      with:
        name: quick-deployment-package
      continue-on-error: true

    - name: Setup SSH key
      run: |
        echo "🔑 Setting up SSH connection..."
        mkdir -p ~/.ssh
        echo "${{ secrets.DEPLOY_SSH_KEY || 'dummy-key' }}" > ~/.ssh/id_rsa || echo "⚠️ SSH key not configured"
        chmod 600 ~/.ssh/id_rsa 2>/dev/null || true
        ssh-keyscan -H ${{ env.TARGET_SERVER }} >> ~/.ssh/known_hosts 2>/dev/null || echo "⚠️ SSH keyscan failed"

    - name: Execute deployment
      run: |
        echo "🚀 Starting automated deployment..."
        
        STRATEGY="${{ needs.pre-deployment.outputs.deployment-strategy }}"
        ENV="${{ github.event.inputs.target_environment || 'staging' }}"
        
        echo "Deployment Strategy: $STRATEGY"
        echo "Target Environment: $ENV"
        
        # 解压部署包（如果存在）
        if [ -f "quick-deploy-${{ github.sha }}.tar.gz" ]; then
          tar -xzf quick-deploy-${{ github.sha }}.tar.gz
        fi
        
        # 根据策略执行部署
        case $STRATEGY in
          standard-deploy)
            echo "📦 Standard deployment process..."
            if [ -f "./ci-cd/deploy/enhanced-auto-deploy.sh" ]; then
              chmod +x ./ci-cd/deploy/enhanced-auto-deploy.sh
              ./ci-cd/deploy/enhanced-auto-deploy.sh $ENV
            fi
            ;;
            
          hotfix-deploy)
            echo "🚨 Hotfix deployment process..."
            if [ -f "./ci-cd/deploy/auto-deploy-trigger.sh" ]; then
              chmod +x ./ci-cd/deploy/auto-deploy-trigger.sh
              export SKIP_TESTS="${{ github.event.inputs.skip_tests }}"
              ./ci-cd/deploy/auto-deploy-trigger.sh $ENV hotfix
            fi
            ;;
            
          rollback)
            echo "↩️ Rollback deployment process..."
            ROLLBACK_VERSION="${{ github.event.inputs.rollback_version }}"
            if [ -f "./ci-cd/deploy/quick-rollback.sh" ]; then
              chmod +x ./ci-cd/deploy/quick-rollback.sh
              ./ci-cd/deploy/quick-rollback.sh "$ROLLBACK_VERSION"
            fi
            ;;
            
          migration)
            echo "🗄️ Database migration only..."
            if [ -f "./ci-cd/deploy/mysql-sync-simple.sh" ]; then
              chmod +x ./ci-cd/deploy/mysql-sync-simple.sh
              ./ci-cd/deploy/mysql-sync-simple.sh
            fi
            ;;
            
          *)
            echo "❌ Unknown deployment strategy: $STRATEGY"
            exit 1
            ;;
        esac

    - name: Post-deployment verification
      run: |
        echo "🔍 Post-deployment verification..."
        
        # 执行健康检查
        if [ -f "./ci-cd/deploy/health-check-advanced.sh" ]; then
          chmod +x ./ci-cd/deploy/health-check-advanced.sh
          
          # 等待服务启动
          echo "⏳ Waiting for services to start..."
          sleep 30
          
          # 执行健康检查
          if ./ci-cd/deploy/health-check-advanced.sh; then
            echo "✅ Health check passed"
          else
            echo "❌ Health check failed"
            
            # 如果是生产环境，考虑自动回滚
            if [ "${{ github.event.inputs.target_environment }}" = "production" ]; then
              echo "🚨 Production deployment failed, considering rollback..."
              if [ -f "./ci-cd/deploy/quick-rollback.sh" ]; then
                echo "↩️ Executing automatic rollback..."
                chmod +x ./ci-cd/deploy/quick-rollback.sh
                ./ci-cd/deploy/quick-rollback.sh auto
              fi
            fi
            
            exit 1
          fi
        fi

    - name: Generate deployment report
      if: always()
      run: |
        echo "📊 Generating deployment report..."
        
        # 创建部署报告
        cat > deployment-report.md << EOF
        # 🚀 Deployment Report
        
        **Environment:** ${{ github.event.inputs.target_environment || 'staging' }}
        **Type:** ${{ github.event.inputs.deployment_type || 'standard' }}
        **Commit:** ${{ github.sha }}
        **Date:** $(date -u)
        **Status:** ${{ job.status }}
        
        ## 🔗 Access Information
        
        EOF
        
        case "${{ github.event.inputs.target_environment || 'staging' }}" in
          production)
            cat >> deployment-report.md << EOF
        - **Frontend:** http://${{ env.TARGET_SERVER }}/
        - **Backend:** http://${{ env.TARGET_SERVER }}:8080/
        - **Health Check:** http://${{ env.TARGET_SERVER }}:8080/actuator/health
        - **API Documentation:** http://${{ env.TARGET_SERVER }}:8080/swagger-ui.html
        EOF
            ;;
          staging)
            cat >> deployment-report.md << EOF
        - **Frontend:** http://staging.financial-system.local/
        - **Backend:** http://staging.financial-system.local:8080/
        - **Health Check:** http://staging.financial-system.local:8080/actuator/health
        EOF
            ;;
          development)
            cat >> deployment-report.md << EOF
        - **Frontend:** http://dev.financial-system.local/
        - **Backend:** http://dev.financial-system.local:8080/
        - **Health Check:** http://dev.financial-system.local:8080/actuator/health
        EOF
            ;;
        esac
        
        cat >> deployment-report.md << EOF
        
        ## 📋 Deployment Details
        
        - **Strategy Used:** ${{ needs.pre-deployment.outputs.deployment-strategy }}
        - **Skip Tests:** ${{ github.event.inputs.skip_tests || 'false' }}
        - **Rollback Version:** ${{ github.event.inputs.rollback_version || 'N/A' }}
        
        ## 🛠️ Quick Commands
        
        \`\`\`bash
        # Check deployment status
        ssh ${{ env.DEPLOY_USER }}@${{ env.TARGET_SERVER }} "docker ps"
        
        # View logs
        ssh ${{ env.DEPLOY_USER }}@${{ env.TARGET_SERVER }} "docker logs financial-backend"
        
        # Emergency rollback
        ./ci-cd/deploy/quick-rollback.sh
        \`\`\`
        EOF
        
        echo "📄 Deployment Report:"
        cat deployment-report.md

    - name: Upload deployment report
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: deployment-report-${{ github.event.inputs.target_environment }}-${{ github.run_number }}
        path: deployment-report.md
        retention-days: 30

  # 部署结果通知
  notify-result:
    needs: [pre-deployment, auto-deploy]
    runs-on: ubuntu-latest
    if: always()
    
    steps:
    - name: Send notification
      run: |
        echo "📧 Sending deployment notification..."
        
        STATUS="${{ needs.auto-deploy.result }}"
        ENV="${{ github.event.inputs.target_environment || 'staging' }}"
        TYPE="${{ github.event.inputs.deployment_type || 'standard' }}"
        
        if [ "$STATUS" = "success" ]; then
          echo "✅ Deployment to $ENV successful!"
          echo "🎉 Type: $TYPE"
          echo "🔗 Commit: ${{ github.sha }}"
          echo "⏰ Duration: $(date -u)"
          
          # 这里可以添加Slack/Teams/Email通知
          # curl -X POST $WEBHOOK_URL -d "{'text': 'Deployment successful!'}"
          
        else
          echo "❌ Deployment to $ENV failed!"
          echo "🚨 Type: $TYPE"
          echo "🔗 Commit: ${{ github.sha }}"
          echo "💥 Check logs for details"
          
          # 这里可以添加失败通知
          # curl -X POST $WEBHOOK_URL -d "{'text': 'Deployment failed!'}"
        fi
        
        echo "📊 Full Pipeline Status:"
        echo "   Pre-checks: ${{ needs.pre-deployment.result }}"
        echo "   Deployment: ${{ needs.auto-deploy.result }}"
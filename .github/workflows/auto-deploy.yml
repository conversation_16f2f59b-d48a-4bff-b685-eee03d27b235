name: FinancialSystem Auto Deploy

on:
  workflow_dispatch:
    inputs:
      target_environment:
        description: 'Target environment for deployment'
        required: true
        default: 'staging'
        type: choice
        options:
        - development
        - staging
        - production
      
      deployment_type:
        description: 'Type of deployment'
        required: true
        default: 'standard'
        type: choice
        options:
        - standard
        - hotfix
        - rollback
        - migration-only
      
      rollback_version:
        description: 'Version to rollback to (if rollback selected)'
        required: false
        type: string
      
      skip_tests:
        description: 'Skip tests for emergency deployment'
        required: false
        default: false
        type: boolean

  repository_dispatch:
    types: [deploy-trigger]

env:
  TARGET_SERVER: '**********'
  DEPLOY_USER: 'admin'
  DEPLOY_PATH: '/home/<USER>/下载/FinancialSystem-Production-Deploy'

jobs:
  # 预检查和环境验证
  pre-deployment:
    runs-on: ubuntu-latest
    outputs:
      can-proceed: ${{ steps.checks.outputs.can-proceed }}
      deployment-strategy: ${{ steps.strategy.outputs.strategy }}
      
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Environment checks
      id: checks
      run: |
        echo "🔍 Performing pre-deployment checks..."
        
        # 检查输入参数
        ENV="${{ github.event.inputs.target_environment || 'staging' }}"
        TYPE="${{ github.event.inputs.deployment_type || 'standard' }}"
        
        echo "Target Environment: $ENV"
        echo "Deployment Type: $TYPE"
        
        # 验证环境配置
        case $ENV in
          production)
            if [ "$TYPE" = "hotfix" ] || [ "${{ github.event.inputs.skip_tests }}" = "true" ]; then
              echo "⚠️ Production environment with fast deployment - requires extra validation"
            fi
            ;;
          staging|development)
            echo "✅ Non-production environment - standard checks apply"
            ;;
          *)
            echo "❌ Invalid environment: $ENV"
            echo "can-proceed=false" >> $GITHUB_OUTPUT
            exit 1
            ;;
        esac
        
        # 检查部署脚本
        if [ ! -f "./ci-cd/deploy/enhanced-auto-deploy.sh" ]; then
          echo "❌ Enhanced deployment script not found"
          echo "can-proceed=false" >> $GITHUB_OUTPUT
          exit 1
        fi
        
        echo "can-proceed=true" >> $GITHUB_OUTPUT

    - name: Determine deployment strategy
      id: strategy
      run: |
        TYPE="${{ github.event.inputs.deployment_type || 'standard' }}"
        ENV="${{ github.event.inputs.target_environment || 'staging' }}"
        
        case $TYPE in
          standard)
            echo "strategy=standard-deploy" >> $GITHUB_OUTPUT
            ;;
          hotfix)
            echo "strategy=hotfix-deploy" >> $GITHUB_OUTPUT
            ;;
          rollback)
            echo "strategy=rollback" >> $GITHUB_OUTPUT
            ;;
          migration-only)
            echo "strategy=migration" >> $GITHUB_OUTPUT
            ;;
          *)
            echo "strategy=standard-deploy" >> $GITHUB_OUTPUT
            ;;
        esac

  # 快速构建（可选，基于部署类型）
  quick-build:
    needs: pre-deployment
    runs-on: ubuntu-latest
    if: needs.pre-deployment.outputs.can-proceed == 'true' && github.event.inputs.deployment_type != 'rollback'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Java
      if: github.event.inputs.deployment_type != 'migration-only'
      uses: actions/setup-java@v4
      with:
        java-version: '21'
        distribution: 'temurin'

    - name: Setup Node.js
      if: github.event.inputs.deployment_type != 'migration-only'
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: FinancialSystem-web/package-lock.json

    - name: Quick backend build
      if: github.event.inputs.deployment_type != 'migration-only'
      run: |
        echo "⚡ Quick backend build..."
        mvn clean package -DskipTests -q

    - name: Quick frontend build
      if: github.event.inputs.deployment_type != 'migration-only'
      working-directory: FinancialSystem-web
      run: |
        echo "⚡ Quick frontend build..."
        npm ci --silent
        npm run build

    - name: Create deployment package
      run: |
        echo "📦 Creating deployment package..."
        mkdir -p quick-deploy
        
        if [ "${{ github.event.inputs.deployment_type }}" != "migration-only" ]; then
          # 复制构建文件
          cp api-gateway/target/*.jar quick-deploy/ 2>/dev/null || true
          cp -r FinancialSystem-web/build quick-deploy/frontend 2>/dev/null || true
        fi
        
        # 复制部署脚本和配置
        cp -r ci-cd quick-deploy/
        cp docker-compose*.yml quick-deploy/ 2>/dev/null || true
        cp -r config quick-deploy/ 2>/dev/null || true
        
        # 打包
        tar -czf quick-deploy-${{ github.sha }}.tar.gz -C quick-deploy .

    - name: Upload deployment package
      uses: actions/upload-artifact@v3
      with:
        name: quick-deployment-package
        path: quick-deploy-${{ github.sha }}.tar.gz
        retention-days: 7

  # 自动化部署执行
  auto-deploy:
    needs: [pre-deployment, quick-build]
    runs-on: ubuntu-latest
    if: always() && needs.pre-deployment.outputs.can-proceed == 'true'
    environment: ${{ github.event.inputs.target_environment || 'staging' }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download deployment package
      if: github.event.inputs.deployment_type != 'rollback'
      uses: actions/download-artifact@v3
      with:
        name: quick-deployment-package
      continue-on-error: true

    - name: Setup SSH key
      run: |
        echo "🔑 Setting up SSH connection..."
        mkdir -p ~/.ssh
        echo "${{ secrets.DEPLOY_SSH_KEY || 'dummy-key' }}" > ~/.ssh/id_rsa || echo "⚠️ SSH key not configured"
        chmod 600 ~/.ssh/id_rsa 2>/dev/null || true
        ssh-keyscan -H ${{ env.TARGET_SERVER }} >> ~/.ssh/known_hosts 2>/dev/null || echo "⚠️ SSH keyscan failed"

    - name: Execute deployment
      run: |
        echo "🚀 Starting automated deployment..."
        
        STRATEGY="${{ needs.pre-deployment.outputs.deployment-strategy }}"
        ENV="${{ github.event.inputs.target_environment || 'staging' }}"
        
        echo "Deployment Strategy: $STRATEGY"
        echo "Target Environment: $ENV"
        
        # 解压部署包（如果存在）
        if [ -f "quick-deploy-${{ github.sha }}.tar.gz" ]; then
          tar -xzf quick-deploy-${{ github.sha }}.tar.gz
        fi
        
        # 根据策略执行部署
        case $STRATEGY in
          standard-deploy)
            echo "📦 Standard deployment process..."
            if [ -f "./ci-cd/deploy/enhanced-auto-deploy.sh" ]; then
              chmod +x ./ci-cd/deploy/enhanced-auto-deploy.sh
              ./ci-cd/deploy/enhanced-auto-deploy.sh $ENV
            fi
            ;;
            
          hotfix-deploy)
            echo "🚨 Hotfix deployment process..."
            if [ -f "./ci-cd/deploy/auto-deploy-trigger.sh" ]; then
              chmod +x ./ci-cd/deploy/auto-deploy-trigger.sh
              export SKIP_TESTS="${{ github.event.inputs.skip_tests }}"
              ./ci-cd/deploy/auto-deploy-trigger.sh $ENV hotfix
            fi
            ;;
            
          rollback)
            echo "↩️ Rollback deployment process..."
            ROLLBACK_VERSION="${{ github.event.inputs.rollback_version }}"
            if [ -f "./ci-cd/deploy/quick-rollback.sh" ]; then
              chmod +x ./ci-cd/deploy/quick-rollback.sh
              ./ci-cd/deploy/quick-rollback.sh "$ROLLBACK_VERSION"
            fi
            ;;
            
          migration)
            echo "🗄️ Database migration only..."
            if [ -f "./ci-cd/deploy/mysql-sync-simple.sh" ]; then
              chmod +x ./ci-cd/deploy/mysql-sync-simple.sh
              ./ci-cd/deploy/mysql-sync-simple.sh
            fi
            ;;
            
          *)
            echo "❌ Unknown deployment strategy: $STRATEGY"
            exit 1
            ;;
        esac

    - name: Post-deployment verification
      run: |
        echo "🔍 Post-deployment verification..."
        
        # 执行健康检查
        if [ -f "./ci-cd/deploy/health-check-advanced.sh" ]; then
          chmod +x ./ci-cd/deploy/health-check-advanced.sh
          
          # 等待服务启动
          echo "⏳ Waiting for services to start..."
          sleep 30
          
          # 执行健康检查
          if ./ci-cd/deploy/health-check-advanced.sh; then
            echo "✅ Health check passed"
          else
            echo "❌ Health check failed"
            
            # 如果是生产环境，考虑自动回滚
            if [ "${{ github.event.inputs.target_environment }}" = "production" ]; then
              echo "🚨 Production deployment failed, considering rollback..."
              if [ -f "./ci-cd/deploy/quick-rollback.sh" ]; then
                echo "↩️ Executing automatic rollback..."
                chmod +x ./ci-cd/deploy/quick-rollback.sh
                ./ci-cd/deploy/quick-rollback.sh auto
              fi
            fi
            
            exit 1
          fi
        fi

    - name: Generate deployment report
      if: always()
      run: |
        echo "📊 Generating deployment report..."
        
        # 创建部署报告
        cat > deployment-report.md << EOF
        # 🚀 Deployment Report
        
        **Environment:** ${{ github.event.inputs.target_environment || 'staging' }}
        **Type:** ${{ github.event.inputs.deployment_type || 'standard' }}
        **Commit:** ${{ github.sha }}
        **Date:** $(date -u)
        **Status:** ${{ job.status }}
        
        ## 🔗 Access Information
        
        EOF
        
        case "${{ github.event.inputs.target_environment || 'staging' }}" in
          production)
            cat >> deployment-report.md << EOF
        - **Frontend:** http://${{ env.TARGET_SERVER }}/
        - **Backend:** http://${{ env.TARGET_SERVER }}:8080/
        - **Health Check:** http://${{ env.TARGET_SERVER }}:8080/actuator/health
        - **API Documentation:** http://${{ env.TARGET_SERVER }}:8080/swagger-ui.html
        EOF
            ;;
          staging)
            cat >> deployment-report.md << EOF
        - **Frontend:** http://staging.financial-system.local/
        - **Backend:** http://staging.financial-system.local:8080/
        - **Health Check:** http://staging.financial-system.local:8080/actuator/health
        EOF
            ;;
          development)
            cat >> deployment-report.md << EOF
        - **Frontend:** http://dev.financial-system.local/
        - **Backend:** http://dev.financial-system.local:8080/
        - **Health Check:** http://dev.financial-system.local:8080/actuator/health
        EOF
            ;;
        esac
        
        cat >> deployment-report.md << EOF
        
        ## 📋 Deployment Details
        
        - **Strategy Used:** ${{ needs.pre-deployment.outputs.deployment-strategy }}
        - **Skip Tests:** ${{ github.event.inputs.skip_tests || 'false' }}
        - **Rollback Version:** ${{ github.event.inputs.rollback_version || 'N/A' }}
        
        ## 🛠️ Quick Commands
        
        \`\`\`bash
        # Check deployment status
        ssh ${{ env.DEPLOY_USER }}@${{ env.TARGET_SERVER }} "docker ps"
        
        # View logs
        ssh ${{ env.DEPLOY_USER }}@${{ env.TARGET_SERVER }} "docker logs financial-backend"
        
        # Emergency rollback
        ./ci-cd/deploy/quick-rollback.sh
        \`\`\`
        EOF
        
        echo "📄 Deployment Report:"
        cat deployment-report.md

    - name: Upload deployment report
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: deployment-report-${{ github.event.inputs.target_environment }}-${{ github.run_number }}
        path: deployment-report.md
        retention-days: 30

  # 部署结果通知
  notify-result:
    needs: [pre-deployment, auto-deploy]
    runs-on: ubuntu-latest
    if: always()
    
    steps:
    - name: Send notification
      run: |
        echo "📧 Sending deployment notification..."
        
        STATUS="${{ needs.auto-deploy.result }}"
        ENV="${{ github.event.inputs.target_environment || 'staging' }}"
        TYPE="${{ github.event.inputs.deployment_type || 'standard' }}"
        
        if [ "$STATUS" = "success" ]; then
          echo "✅ Deployment to $ENV successful!"
          echo "🎉 Type: $TYPE"
          echo "🔗 Commit: ${{ github.sha }}"
          echo "⏰ Duration: $(date -u)"
          
          # 这里可以添加Slack/Teams/Email通知
          # curl -X POST $WEBHOOK_URL -d "{'text': 'Deployment successful!'}"
          
        else
          echo "❌ Deployment to $ENV failed!"
          echo "🚨 Type: $TYPE"
          echo "🔗 Commit: ${{ github.sha }}"
          echo "💥 Check logs for details"
          
          # 这里可以添加失败通知
          # curl -X POST $WEBHOOK_URL -d "{'text': 'Deployment failed!'}"
        fi
        
        echo "📊 Full Pipeline Status:"
        echo "   Pre-checks: ${{ needs.pre-deployment.result }}"
        echo "   Deployment: ${{ needs.auto-deploy.result }}"
name: FinancialSystem CI/CD Pipeline

on:
  push:
    branches: [ main, develop, 'feature/*', 'hotfix/*' ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Target deployment environment'
        required: true
        default: 'development'
        type: choice
        options:
        - development
        - staging
        - production

env:
  JAVA_VERSION: '21'
  NODE_VERSION: '18'
  REGISTRY: ghcr.io
  IMAGE_NAME: financial-system

jobs:
  # 代码质量检查 - 所有分支
  code-quality:
    runs-on: ubuntu-latest
    outputs:
      should-deploy: ${{ steps.check-branch.outputs.should-deploy }}
      target-env: ${{ steps.check-branch.outputs.target-env }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Setup Java
      uses: actions/setup-java@v4
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: FinancialSystem-web/package-lock.json

    - name: <PERSON><PERSON>ven dependencies
      uses: actions/cache@v3
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2

    - name: Check branch strategy
      id: check-branch
      run: |
        branch=${GITHUB_REF#refs/heads/}
        echo "Current branch: $branch"
        
        case $branch in
          main)
            echo "should-deploy=true" >> $GITHUB_OUTPUT
            echo "target-env=production" >> $GITHUB_OUTPUT
            ;;
          develop)
            echo "should-deploy=true" >> $GITHUB_OUTPUT
            echo "target-env=staging" >> $GITHUB_OUTPUT
            ;;
          feature/*|hotfix/*)
            echo "should-deploy=false" >> $GITHUB_OUTPUT
            echo "target-env=development" >> $GITHUB_OUTPUT
            ;;
          *)
            echo "should-deploy=false" >> $GITHUB_OUTPUT
            echo "target-env=development" >> $GITHUB_OUTPUT
            ;;
        esac

    - name: Backend - Compile and Test
      run: |
        echo "🔨 Compiling backend..."
        mvn clean compile -DskipTests
        echo "🧪 Running backend tests..."
        mvn test

    - name: Frontend - Install and Test
      working-directory: FinancialSystem-web
      run: |
        echo "📦 Installing frontend dependencies..."
        npm ci
        echo "🔍 Running frontend linting..."
        npm run lint || true
        echo "🧪 Running frontend tests..."
        npm test -- --watchAll=false || true

    - name: Security Scan
      run: |
        echo "🔒 Running security scans..."
        # 依赖漏洞扫描
        mvn org.owasp:dependency-check-maven:check || true
        # 前端依赖审计
        cd FinancialSystem-web && npm audit --audit-level moderate || true

  # 构建阶段
  build:
    needs: code-quality
    runs-on: ubuntu-latest
    if: needs.code-quality.outputs.should-deploy == 'true'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Java
      uses: actions/setup-java@v4
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: FinancialSystem-web/package-lock.json

    - name: Cache Maven dependencies
      uses: actions/cache@v3
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2

    - name: Build Backend
      run: |
        echo "🔨 Building backend application..."
        mvn clean package -DskipTests
        ls -la api-gateway/target/

    - name: Build Frontend
      working-directory: FinancialSystem-web
      run: |
        echo "🔨 Building frontend application..."
        npm ci
        npm run build
        ls -la build/

    - name: Create deployment package
      run: |
        echo "📦 Creating deployment package..."
        mkdir -p deployment-package
        
        # 复制后端JAR文件
        cp api-gateway/target/*.jar deployment-package/
        
        # 复制前端build文件
        cp -r FinancialSystem-web/build deployment-package/frontend
        
        # 复制部署脚本
        cp -r ci-cd deployment-package/
        
        # 复制Docker相关文件
        cp docker-compose*.yml deployment-package/ || true
        cp Dockerfile* deployment-package/ || true
        
        # 复制配置文件
        cp -r config deployment-package/ || true
        
        # 创建部署包
        tar -czf financial-system-${{ github.sha }}.tar.gz -C deployment-package .
        
        echo "📊 Package contents:"
        tar -tzf financial-system-${{ github.sha }}.tar.gz | head -20

    - name: Upload deployment package
      uses: actions/upload-artifact@v3
      with:
        name: financial-system-deployment
        path: financial-system-${{ github.sha }}.tar.gz
        retention-days: 30

  # 集成测试
  integration-test:
    needs: [code-quality, build]
    runs-on: ubuntu-latest
    if: needs.code-quality.outputs.should-deploy == 'true'
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: test_password
          MYSQL_DATABASE: test_db
        ports:
          - 3306:3306
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download deployment package
      uses: actions/download-artifact@v3
      with:
        name: financial-system-deployment

    - name: Setup test environment
      run: |
        echo "🏗️ Setting up test environment..."
        # 解压部署包
        tar -xzf financial-system-${{ github.sha }}.tar.gz
        
        # 启动测试环境
        echo "🚀 Starting test services..."
        # 这里可以添加具体的集成测试逻辑
        
    - name: Run integration tests
      run: |
        echo "🧪 Running integration tests..."
        # 添加集成测试脚本
        if [ -f "./ci-cd/test/test-complete-pipeline.sh" ]; then
          chmod +x ./ci-cd/test/test-complete-pipeline.sh
          ./ci-cd/test/test-complete-pipeline.sh
        else
          echo "⚠️ Integration test script not found, skipping..."
        fi

  # 部署到目标环境
  deploy:
    needs: [code-quality, build, integration-test]
    runs-on: ubuntu-latest
    if: needs.code-quality.outputs.should-deploy == 'true'
    environment: ${{ needs.code-quality.outputs.target-env }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download deployment package
      uses: actions/download-artifact@v3
      with:
        name: financial-system-deployment

    - name: Deploy to ${{ needs.code-quality.outputs.target-env }}
      run: |
        echo "🚀 Deploying to ${{ needs.code-quality.outputs.target-env }} environment..."
        
        # 解压部署包
        tar -xzf financial-system-${{ github.sha }}.tar.gz
        
        # 根据环境选择部署策略
        case "${{ needs.code-quality.outputs.target-env }}" in
          production)
            echo "🏭 Production deployment with safety checks..."
            if [ -f "./ci-cd/deploy/enhanced-auto-deploy.sh" ]; then
              chmod +x ./ci-cd/deploy/enhanced-auto-deploy.sh
              ./ci-cd/deploy/enhanced-auto-deploy.sh production
            else
              echo "❌ Production deployment script not found!"
              exit 1
            fi
            ;;
          staging)
            echo "🧪 Staging deployment..."
            if [ -f "./ci-cd/deploy/auto-deploy-trigger.sh" ]; then
              chmod +x ./ci-cd/deploy/auto-deploy-trigger.sh
              ./ci-cd/deploy/auto-deploy-trigger.sh staging
            else
              echo "❌ Staging deployment script not found!"
              exit 1
            fi
            ;;
          development)
            echo "🔧 Development deployment..."
            if [ -f "./ci-cd/deploy/simple-deploy.sh" ]; then
              chmod +x ./ci-cd/deploy/simple-deploy.sh
              ./ci-cd/deploy/simple-deploy.sh development
            else
              echo "⚠️ Development deployment script not found, using basic deployment"
            fi
            ;;
        esac

    - name: Post-deployment verification
      run: |
        echo "🔍 Verifying deployment..."
        
        # 健康检查
        if [ -f "./ci-cd/deploy/health-check-advanced.sh" ]; then
          chmod +x ./ci-cd/deploy/health-check-advanced.sh
          ./ci-cd/deploy/health-check-advanced.sh
        fi
        
        # 显示部署信息
        echo "✅ Deployment completed successfully!"
        echo "📊 Environment: ${{ needs.code-quality.outputs.target-env }}"
        echo "🔗 Commit: ${{ github.sha }}"
        echo "🌐 Access URLs:"
        
        case "${{ needs.code-quality.outputs.target-env }}" in
          production)
            echo "   Frontend: http://**********/"
            echo "   Backend: http://**********:8080/"
            echo "   Health: http://**********:8080/actuator/health"
            ;;
          staging)
            echo "   Frontend: http://staging.financial-system.local/"
            echo "   Backend: http://staging.financial-system.local:8080/"
            ;;
          development)
            echo "   Frontend: http://dev.financial-system.local/"
            echo "   Backend: http://dev.financial-system.local:8080/"
            ;;
        esac

  # 通知
  notify:
    needs: [code-quality, build, integration-test, deploy]
    runs-on: ubuntu-latest
    if: always()
    
    steps:
    - name: Notify deployment result
      run: |
        if [ "${{ needs.deploy.result }}" == "success" ]; then
          echo "✅ Deployment successful!"
          # 这里可以添加成功通知逻辑（Slack, Teams, Email等）
        else
          echo "❌ Deployment failed!"
          # 这里可以添加失败通知逻辑
        fi
        
        echo "📊 Pipeline Summary:"
        echo "   Code Quality: ${{ needs.code-quality.result }}"
        echo "   Build: ${{ needs.build.result }}"
        echo "   Integration Test: ${{ needs.integration-test.result }}"
        echo "   Deploy: ${{ needs.deploy.result }}"
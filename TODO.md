# TODO.md - FinancialSystem项目任务清单

## 🚀 待开发功能

### [#001] 逾期债权数据导出
**描述**：将逾期债权数据导出到经营调度会看板中
**详细需求**：
- [ ] 角色管理
  - 支持角色：ADMIN，只有这个角色的用户可以导出
  - 具体需求：
  - 1.我把excel模版存在根目录，你可以根据实际情况将他复制到需要的地方，excel模版名称为经营调度会看板模版
  - 2.当用户传入的年份为2025年的时候，从减值准备表中根据上一年（2024年）和12月份，筛选出本月末余额的汇总数，存入到模版中的B6
  - 3.根据传入的年份和月份，利用现有的数据导出中心，导出的表9中的数据，表9的每月新增数据之和为本年累计新增数据，这个数据存入B9，累计清收数据之后为对应的新增债权累计清收数据，其中有一个
  - 有一个地方的逻辑需要处理就是每个债权的清收处置金额不能大于当年新增金额，如果实际清收处置金额大于当年新增金额，这笔债权的处置金额为新增金额，超出部分为存量债权处置金额，
  - 表9的每月新增数据按照管理公司进行汇总，形成管理公司、新增债权金额和累计回收金额，按照新增金额的降序排列，按顺序分别存入I22，J22，K22，不断的往下存入excel中；
  - 4.根据传入的年份和月份，从减值准备表的本月处置债权筛选，对应年份，累计到传入月份的债权之和-上面的新增债权处置之和=存量债权本年累计处置，将这个值存入D6，再利用同样的逻辑求出上月的存量
  - 债权本年累计清收金额，本月累计清收金额-上月累计清收金额=本月清收金额，将这个金额存入C6；
  - 5.将2和4中筛选出的数据，均按照管理公司进行汇总，保留管理公司、期初存量债权、存量债权累计清收处置金额，按照期初存量债权降序排列，然后写入B22、C22、D22，不断往下存；
  - 6.根据传入的年份和月份参数，从处置表筛选出对应年份累计到传入月份的现金处置、分期还款、资产抵债和其他方式，将现金处置之和存入B14，分期还款存入
  - B13，资产抵债和其他方式之和存入B17
  - 7.根据传入的年份和月份，从减值准备表中筛选本月处置债权列数据，保留管理公司、债务人和本月处置债权列，然后利用年份去减值准备表中筛选上年12月的本月末余额数据，利用年份和月份从减值准备表
  - 筛选累计至本月新增债权的和，将这三个筛选结果按照（从减值准备表中筛选本月处置债权列数据，保留管理公司、债务人和本月处置债权列）这个数据进行左侧拼接，仅保留此筛选数据所存在的行，
  - 再将拼接的结果拆分成两个表，一个存量债权处置表，一个新增债权处置表，列为管理公司、债务人、处置金额，拆分逻辑如果处置金额小于本年累计新增金额，处置金额就全部为新增债权处置表，如果
  - 大于本年累计新增金额，就将超出部分归到存量债权处置表中，如果没有当年新增债权，就全部归为存量债权处置金额，将存量债权处置表中处置金额按照降序排列的前80%且处置金额大于100债权列示到N5、O5、P5，多行向下填充
  - 新增债权处置表数据列示到R5、S5、T5，多行就不断向下填充。
  - 8.根据传入的年份和月份数据，从减值准备表中筛选本月新增债权数据，列示管理公司、债务人和本月新增债权，按照新增债权降序排列，列示前80%区间且新增债权金额大于100的，填入N15、O15、P15，多行向下填充
  - 9.导出中心的导出的逾期债权表9，根据筛选出来的数据家一列新增债权汇总数和新增债权余额数据，其中新增债权汇总数=每个债权每月新增债权金额相加，余额为汇总数-处置金额。按照余额进行降序排列，填入R15、S15、T51，多行就不断向下填充
  - 10.还有一个问题要特别注意，我可能还没有
  - 解决表9中处置债权金额超过新增金额的问题，帮我看一下解决了没有，如果没有解决帮我解决一下，如果处置金额超过了新增金额，就将超出部分剔除，仅将=新增金额的处置金额数据填入到表9对应的行，这个需求属于另外需求，要
  - 特别处理。 
-


### [#002] 数据导出功能增强
**描述**：优化现有导出功能，支持更多格式和批量操作
**优先级**：中
**预计工时**：2天
**详细需求**：
- [ ] 支持批量导出（最多1000条）
- [ ] 自定义导出模板
- [ ] 添加进度条显示
- [ ] 支持格式：Excel, CSV, PDF
**技术方案**：
- 使用Apache POI处理Excel
- 异步任务队列处理大批量导出
- WebSocket推送进度
**验收标准**：
- 导出1000条数据时间 < 30秒
- 支持断点续传
- 内存占用优化

## 🐛 待修复问题

### [BUG-001] 登录页面在移动端显示异常
**严重程度**：中
**影响范围**：所有移动端用户
**复现步骤**：
1. 使用手机访问登录页
2. 横屏切换到竖屏
3. 输入框被键盘遮挡
**修复方案**：调整响应式布局

### [BUG-002] Excel导出日期格式错误
**严重程度**：低
**影响范围**：数据导出功能
**问题描述**：日期显示为数字格式
**修复方案**：设置正确的单元格格式

## 💡 改进想法

- [ ] 添加暗黑模式支持
- [ ] 实现数据实时同步功能
- [ ] 优化首页加载速度
- [ ] 添加快捷键支持
- [ ] 国际化支持（i18n）

## 📅 版本规划

### v1.2.0 (下个版本 - 目标：2周内)
- [ ] 完成权限管理基础功能
- [ ] 修复所有已知bug
- [ ] 性能优化：首页加载时间 < 2秒

### v1.3.0 (计划中)
- [ ] 高级数据分析功能
- [ ] API性能优化
- [ ] 移动端APP开发

### v2.0.0 (长期规划)
- [ ] 微服务架构重构
- [ ] 引入消息队列
- [ ] 分布式部署支持

## 📝 技术债务

- [ ] 重构认证模块（代码复杂度过高）
  - 当前圈复杂度：15
  - 目标：< 10
- [ ] 统一错误处理机制
  - 实现全局异常处理器
  - 标准化错误响应格式
- [ ] 提升单元测试覆盖率
  - 当前：45%
  - 目标：80%
- [ ] 优化数据库查询
  - 添加必要的索引
  - 优化慢查询（> 1秒）
- [ ] 代码规范统一
  - 配置ESLint规则
  - 统一代码格式化

## 🔄 更新记录

### 2025-01-31
- 创建初始TODO.md文件
- 添加示例任务和模板结构
- ✅ 完成Git工作流程优化实施
  - 更新CLAUDE.md添加所有简化命令
  - 创建TODO.md模板和联动机制
  - 增强git.md命令支持智能分支和TODO集成
  - ✅ 创建quick-deploy.sh一键部署脚本 (已移动到ci-cd/deploy/)
  - 创建auto-branch.sh智能分支管理脚本
  - 配置.claude/settings.json个人开发者模式

---

**使用说明**：
1. 任务编号格式：[#XXX] 用于快速定位
2. 优先级：高/中/低
3. 完成任务后使用 ~~删除线~~ 标记，并添加完成日期
4. 定期清理已完成的任务到归档文件
5. 使用 `实现todo #001` 命令可以直接开始执行对应任务
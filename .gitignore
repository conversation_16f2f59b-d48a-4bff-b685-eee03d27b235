### IntelliJ IDEA ###
out/
!**/src/main/**/out/
!**/src/test/**/out/

### Eclipse ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache
bin/
!**/src/main/**/bin/
!**/src/test/**/bin/

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

### VS Code ###
.vscode/

### Windsurf ###
.windsurf/
.windsurfrules

### Mac OS ###
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
JDK-21/

### Synology NAS ###
**/@eaDir/

### Maven ###
target/
*.iml
**/target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties

### Logs ###
*.log
app_log*.txt

### Syncthing ###
.syncthing.*
*.tmp

### IDE ###
.idea/
*.iws
*.ipr

### Spring Boot ###
*.pid

### Node.js / 前端依赖 ###
node_modules/
**/node_modules/

### 已删除的重复模块目录 - 防止重新创建 ###
/common/
/data-access/
/data-processing/
/kingdee/
/account/
/audit/
/webservice/
/Treasury/
/business-modules/
/infrastructure/
/api/

### IntelliJ IDEA 自动生成目录防护 ###
**/business-modules/
business-modules

### 自动生成的目录 - 防止重新创建 ###
build/
backups/
**/build/
**/out/
**/bin/

### IDE缓存和生成文件 ###
**/*.iml
**/target/
.idea/workspace.xml
.idea/tasks.xml
.idea/shelf/
.idea/workspace-*.xml
.idea/AugmentWebviewStateStore.xml

### 备份和临时文件 ###
backup*/
temp/
tmp/
*.tmp
*.bak
*.backup
*~

### 构建输出目录 ###
**/target/
**/build/
**/dist/
**/out/

### Syncthing和文件同步产生的冲突文件 ###
*.sync-conflict-*

### Java编译文件 ###
*.class
/com/


### Java编译输出文件 - 严格排除 ###
/com/
/debug/
**/*.class
!/src/**/*.class

### 日志文件管理 ###
logs/**/*.log
logs/**/*.out
!logs/**/.gitkeep

### 临时和调试文件 ###
debug*/
temp*/
*.tmp

### 测试输出文件 ###
test_*.xlsx
**/test_*.xlsx
*.test.xlsx
export_test_*.xlsx

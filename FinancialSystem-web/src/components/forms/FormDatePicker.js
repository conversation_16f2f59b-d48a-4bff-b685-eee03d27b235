import React, { useState, useRef, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Box, Paper, Typography, IconButton, Grid } from '@mui/material';
import { CalendarToday, ChevronLeft, ChevronRight, Schedule } from '@mui/icons-material';
import {
  colors,
  typography,
  spacing,
  dimensions,
  shadows,
  transitions,
  zIndex,
} from '../../constants/styleConstants';

const FormDatePicker = ({
  label,
  value,
  onChange,
  required = false,
  disabled = false,
  error,
  placeholder = '请选择日期',
  name,
  // Date picker modes
  mode = 'date', // 'date' or 'datetime'
  // Date validation
  minDate,
  maxDate,
  // Date format
  dateFormat = 'YYYY-MM-DD',
  displayFormat = 'YYYY年MM月DD日',
  // Style options
  fullWidth = true,
  size = 'medium',
  // Callbacks
  onFocus,
  onBlur,
  // Additional props
  ...props
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [viewDate, setViewDate] = useState(new Date(value || new Date()));
  const [selectedDate, setSelectedDate] = useState(value ? new Date(value) : null);
  const [selectedTime, setSelectedTime] = useState(
    value
      ? {
        hours: new Date(value).getHours(),
        minutes: new Date(value).getMinutes(),
      }
      : { hours: 0, minutes: 0 },
  );

  const datePickerRef = useRef(null);

  // Handle click outside to close calendar
  useEffect(() => {
    const handleClickOutside = event => {
      if (datePickerRef.current && !datePickerRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Format date for display
  const formatDateForDisplay = date => {
    if (!date) {
      return '';
    }

    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');

    if (mode === 'datetime') {
      return `${year}年${month}月${day}日 ${hours}:${minutes}`;
    }
    return `${year}年${month}月${day}日`;
  };

  // Format date for value
  const formatDateForValue = date => {
    if (!date) {
      return '';
    }

    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');

    if (mode === 'datetime') {
      return `${year}-${month}-${day} ${hours}:${minutes}`;
    }
    return `${year}-${month}-${day}`;
  };

  // Check if date is valid
  const isValidDate = date => {
    if (!date) {
      return true;
    }

    const d = new Date(date);
    if (isNaN(d.getTime())) {
      return false;
    }

    if (minDate && d < new Date(minDate)) {
      return false;
    }
    if (maxDate && d > new Date(maxDate)) {
      return false;
    }

    return true;
  };

  // Handle date selection
  const handleDateSelect = date => {
    const newDate = new Date(date);

    if (mode === 'datetime') {
      newDate.setHours(selectedTime.hours, selectedTime.minutes, 0, 0);
    } else {
      newDate.setHours(0, 0, 0, 0);
    }

    if (isValidDate(newDate)) {
      setSelectedDate(newDate);
      const formattedValue = formatDateForValue(newDate);

      const mockEvent = {
        target: { name, value: formattedValue },
      };
      onChange(mockEvent);

      if (mode === 'date') {
        setIsOpen(false);
      }
    }
  };

  // Handle time change
  const handleTimeChange = (field, value) => {
    const newTime = { ...selectedTime, [field]: parseInt(value) };
    setSelectedTime(newTime);

    if (selectedDate) {
      const newDate = new Date(selectedDate);
      newDate.setHours(newTime.hours, newTime.minutes, 0, 0);

      if (isValidDate(newDate)) {
        setSelectedDate(newDate);
        const formattedValue = formatDateForValue(newDate);

        const mockEvent = {
          target: { name, value: formattedValue },
        };
        onChange(mockEvent);
      }
    }
  };

  // Navigate months
  const navigateMonth = direction => {
    const newDate = new Date(viewDate);
    newDate.setMonth(newDate.getMonth() + direction);
    setViewDate(newDate);
  };

  // Generate calendar days
  const generateCalendarDays = () => {
    const year = viewDate.getFullYear();
    const month = viewDate.getMonth();

    const firstDay = new Date(year, month, 1);
    new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());

    const days = [];
    const currentDate = new Date(startDate);

    for (let i = 0; i < 42; i++) {
      const isCurrentMonth = currentDate.getMonth() === month;
      const isSelected =
        selectedDate &&
        currentDate.getTime() ===
          new Date(
            selectedDate.getFullYear(),
            selectedDate.getMonth(),
            selectedDate.getDate(),
          ).getTime();
      const isToday = currentDate.getTime() === new Date().setHours(0, 0, 0, 0);
      const isDisabled = !isValidDate(currentDate);

      days.push({
        date: new Date(currentDate),
        day: currentDate.getDate(),
        isCurrentMonth,
        isSelected,
        isToday,
        isDisabled,
      });

      currentDate.setDate(currentDate.getDate() + 1);
    }

    return days;
  };

  const handleToggle = () => {
    if (disabled) {
      return;
    }

    setIsOpen(!isOpen);
    if (!isOpen && onFocus) {
      onFocus({ target: { name, value } });
    }
    if (isOpen && onBlur) {
      onBlur({ target: { name, value } });
    }
  };

  const getInputStyles = () => ({
    width: fullWidth ? '100%' : 'auto',
    padding: size === 'small' ? `${spacing.xs} ${spacing.sm}` : `${spacing.sm} ${spacing.md}`,
    borderRadius: dimensions.borderRadius.base,
    border: `${dimensions.borderWidth.base} solid ${
      error ? colors.error.main : colors.border.main
    }`,
    fontSize: size === 'small' ? typography.fontSize.xs : typography.fontSize.sm,
    fontFamily: typography.fontFamily.base,
    height: size === 'small' ? '29px' : dimensions.height.input,
    boxSizing: 'border-box',
    backgroundColor: disabled ? colors.background.light : colors.background.paper,
    color: disabled ? colors.text.disabled : colors.text.primary,
    cursor: disabled ? 'not-allowed' : 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    position: 'relative',
    transition: transitions.all,
    opacity: disabled ? 0.6 : 1,
    boxShadow: error ? `0 0 0 1px ${colors.error.main}` : 'none',

    '&:hover': {
      borderColor: disabled ? colors.border.main : error ? colors.error.dark : colors.border.dark,
    },

    '&:focus': {
      outline: 'none',
      borderColor: error ? colors.error.main : colors.primary.main,
      boxShadow: error ? `0 0 0 1px ${colors.error.main}` : `0 0 0 1px ${colors.primary.main}`,
    },
  });

  const getCalendarStyles = () => ({
    position: 'absolute',
    top: '100%',
    left: 0,
    zIndex: zIndex.dropdown,
    marginTop: '2px',
    minWidth: '280px',
    backgroundColor: colors.background.paper,
    boxShadow: shadows.dropdown,
    borderRadius: dimensions.borderRadius.base,
    border: `${dimensions.borderWidth.base} solid ${colors.border.light}`,
    padding: spacing.md,
  });

  const getDayStyles = day => ({
    width: '36px',
    height: '36px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: day.isDisabled ? 'not-allowed' : 'pointer',
    fontSize: typography.fontSize.sm,
    borderRadius: '50%',
    transition: transitions.all,
    backgroundColor: day.isSelected
      ? colors.primary.main
      : day.isToday
        ? colors.primary.light
        : 'transparent',
    color: day.isSelected
      ? colors.primary.contrastText
      : day.isToday
        ? colors.primary.main
        : day.isCurrentMonth
          ? colors.text.primary
          : colors.text.disabled,
    opacity: day.isDisabled ? 0.4 : 1,

    '&:hover': {
      backgroundColor: day.isSelected
        ? colors.primary.dark
        : day.isDisabled
          ? 'transparent'
          : colors.background.hover,
    },
  });

  const getLabelStyles = () => ({
    display: 'block',
    marginBottom: spacing.xs,
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    color: error ? colors.error.main : colors.text.primary,
    fontFamily: typography.fontFamily.base,
  });

  const calendarDays = generateCalendarDays();

  return (
    <Box sx={{ marginBottom: spacing.md, position: 'relative' }} ref={datePickerRef}>
      <Typography variant="body2" component="label" sx={getLabelStyles()}>
        {label}
        {required && <span style={{ color: colors.error.main, marginLeft: '2px' }}>*</span>}
      </Typography>

      <Box
        onClick={handleToggle}
        role="button"
        aria-expanded={isOpen}
        aria-disabled={disabled}
        tabIndex={disabled ? -1 : 0}
        sx={getInputStyles()}
        {...props}
      >
        <span style={{ flex: 1, textAlign: 'left' }}>
          {formatDateForDisplay(selectedDate) || placeholder}
        </span>
        <Box sx={{ marginLeft: spacing.sm, display: 'flex', alignItems: 'center' }}>
          {mode === 'datetime' ? <Schedule /> : <CalendarToday />}
        </Box>
      </Box>

      {isOpen && !disabled && (
        <Paper sx={getCalendarStyles()}>
          {/* Calendar Header */}
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              marginBottom: spacing.md,
            }}
          >
            <IconButton onClick={() => navigateMonth(-1)} size="small">
              <ChevronLeft />
            </IconButton>
            <Typography variant="subtitle1" sx={{ fontWeight: typography.fontWeight.medium }}>
              {viewDate.getFullYear()}年{viewDate.getMonth() + 1}月
            </Typography>
            <IconButton onClick={() => navigateMonth(1)} size="small">
              <ChevronRight />
            </IconButton>
          </Box>

          {/* Weekday Headers */}
          <Grid container spacing={0} sx={{ marginBottom: spacing.sm }}>
            {['日', '一', '二', '三', '四', '五', '六'].map(day => (
              <Grid item xs key={day} sx={{ display: 'flex', justifyContent: 'center' }}>
                <Box
                  sx={{
                    width: '36px',
                    height: '24px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: typography.fontSize.xs,
                    fontWeight: typography.fontWeight.medium,
                    color: colors.text.secondary,
                  }}
                >
                  {day}
                </Box>
              </Grid>
            ))}
          </Grid>

          {/* Calendar Days */}
          <Grid container spacing={0}>
            {calendarDays.map((day, index) => (
              <Grid item xs key={index} sx={{ display: 'flex', justifyContent: 'center' }}>
                <Box
                  onClick={() => !day.isDisabled && handleDateSelect(day.date)}
                  sx={getDayStyles(day)}
                >
                  {day.day}
                </Box>
              </Grid>
            ))}
          </Grid>

          {/* Time Picker for datetime mode */}
          {mode === 'datetime' && (
            <Box
              sx={{
                marginTop: spacing.md,
                paddingTop: spacing.md,
                borderTop: `1px solid ${colors.border.light}`,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: spacing.sm,
              }}
            >
              <select
                value={selectedTime.hours}
                onChange={e => handleTimeChange('hours', e.target.value)}
                style={{
                  padding: `${spacing.xs} ${spacing.sm}`,
                  borderRadius: dimensions.borderRadius.base,
                  border: `1px solid ${colors.border.main}`,
                  fontSize: typography.fontSize.sm,
                }}
              >
                {Array.from({ length: 24 }, (_, i) => (
                  <option key={i} value={i}>
                    {String(i).padStart(2, '0')}
                  </option>
                ))}
              </select>
              <span>:</span>
              <select
                value={selectedTime.minutes}
                onChange={e => handleTimeChange('minutes', e.target.value)}
                style={{
                  padding: `${spacing.xs} ${spacing.sm}`,
                  borderRadius: dimensions.borderRadius.base,
                  border: `1px solid ${colors.border.main}`,
                  fontSize: typography.fontSize.sm,
                }}
              >
                {Array.from({ length: 60 }, (_, i) => (
                  <option key={i} value={i}>
                    {String(i).padStart(2, '0')}
                  </option>
                ))}
              </select>
            </Box>
          )}
        </Paper>
      )}

      {error && (
        <Typography
          variant="caption"
          sx={{
            color: colors.error.main,
            display: 'block',
            marginTop: spacing.xs,
            fontSize: typography.fontSize.xs,
          }}
        >
          {error}
        </Typography>
      )}
    </Box>
  );
};

FormDatePicker.propTypes = {
  label: PropTypes.string.isRequired,
  value: PropTypes.string,
  onChange: PropTypes.func.isRequired,
  required: PropTypes.bool,
  disabled: PropTypes.bool,
  error: PropTypes.string,
  placeholder: PropTypes.string,
  name: PropTypes.string,
  // Date picker modes
  mode: PropTypes.oneOf(['date', 'datetime']),
  // Date validation
  minDate: PropTypes.string,
  maxDate: PropTypes.string,
  // Date format
  dateFormat: PropTypes.string,
  displayFormat: PropTypes.string,
  // Style options
  fullWidth: PropTypes.bool,
  size: PropTypes.oneOf(['small', 'medium']),
  // Callbacks
  onFocus: PropTypes.func,
  onBlur: PropTypes.func,
};

export default FormDatePicker;

import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Box, Typography } from '@mui/material';
import {
  colors,
  typography,
  spacing,
  dimensions,
  transitions,
} from '../../constants/styleConstants';

const FormInput = ({
  label,
  value,
  onChange,
  required = false,
  disabled = false,
  error,
  placeholder,
  type = 'text',
  name,
  // Number formatting options
  isNumberFormat = false,
  decimalPlaces = 2,
  // Validation options
  minValue,
  maxValue,
  minLength,
  maxLength,
  // Style options
  fullWidth = true,
  size = 'medium',
  // Callbacks
  onFocus,
  onBlur,
  onWarning = () => {},
  // Additional props
  ...props
}) => {
  const [decimalWarning, setDecimalWarning] = useState('');
  const [validationError, setValidationError] = useState('');

  // Validate input on value change
  useEffect(() => {
    let currentWarning = '';
    let currentValidationError = '';

    if (value !== undefined && value !== null && value !== '') {
      // Number format validation
      if (isNumberFormat && type === 'number') {
        const valueStr = String(value);
        if (valueStr.includes('.')) {
          const decimalPart = valueStr.split('.')[1];
          if (decimalPart && decimalPart.length > decimalPlaces) {
            currentWarning = `请保留${decimalPlaces}位小数`;
          }
        }
      }

      // Range validation for numbers
      if (type === 'number' && !isNaN(value)) {
        const numValue = parseFloat(value);
        if (minValue !== undefined && numValue < minValue) {
          currentValidationError = `值不能小于 ${minValue}`;
        } else if (maxValue !== undefined && numValue > maxValue) {
          currentValidationError = `值不能大于 ${maxValue}`;
        }
      }

      // Length validation for text
      if (type === 'text' || type === 'email' || type === 'password') {
        const strValue = String(value);
        if (minLength !== undefined && strValue.length < minLength) {
          currentValidationError = `长度不能少于 ${minLength} 个字符`;
        } else if (maxLength !== undefined && strValue.length > maxLength) {
          currentValidationError = `长度不能超过 ${maxLength} 个字符`;
        }
      }

      // Email validation
      if (type === 'email' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
          currentValidationError = '请输入有效的邮箱地址';
        }
      }
    }

    if (decimalWarning !== currentWarning) {
      setDecimalWarning(currentWarning);
      onWarning(label, currentWarning);
    }

    setValidationError(currentValidationError);
  }, [
    value,
    isNumberFormat,
    decimalPlaces,
    type,
    label,
    minValue,
    maxValue,
    minLength,
    maxLength,
    decimalWarning,
    onWarning,
  ]);

  const handleChange = e => {
    onChange(e);
  };

  const handleBlur = e => {
    // Auto-format numbers on blur
    if (isNumberFormat && type === 'number' && e.target.value) {
      try {
        const originalValue = e.target.value;
        const hasMultipleDecimals =
          originalValue.includes('.') &&
          originalValue.split('.')[1] &&
          originalValue.split('.')[1].length > decimalPlaces;

        if (hasMultipleDecimals) {
          const rounded = parseFloat(parseFloat(originalValue).toFixed(decimalPlaces));

          const simulatedEvent = {
            target: {
              value: rounded,
              name: e.target.name,
            },
            type: 'blur',
          };
          onChange(simulatedEvent);
        }
      } catch (err) {
        // eslint-disable-next-line no-console
        console.error('四舍五入处理出错:', err);
      }
    }

    if (onBlur) {
      onBlur(e);
    }
  };

  const handleFocus = e => {
    if (onFocus) {
      onFocus(e);
    }
  };

  // Get dynamic styles based on state
  const getInputStyles = () => {
    const baseStyles = {
      width: fullWidth ? '100%' : 'auto',
      padding: size === 'small' ? `${spacing.xs} ${spacing.sm}` : `${spacing.sm} ${spacing.md}`,
      borderRadius: dimensions.borderRadius.base,
      border: `${dimensions.borderWidth.base} solid ${colors.border.main}`,
      fontSize: size === 'small' ? typography.fontSize.xs : typography.fontSize.sm,
      fontFamily: typography.fontFamily.base,
      height: size === 'small' ? '29px' : dimensions.height.input,
      boxSizing: 'border-box',
      transition: transitions.all,
      outline: 'none',
      backgroundColor: disabled ? colors.background.light : colors.background.paper,
      color: disabled ? colors.text.disabled : colors.text.primary,
      cursor: disabled ? 'not-allowed' : 'text',
    };

    if (error || validationError) {
      baseStyles.borderColor = colors.error.main;
      baseStyles.boxShadow = `0 0 0 1px ${colors.error.main}`;
    }

    return baseStyles;
  };

  const getLabelStyles = () => ({
    display: 'block',
    marginBottom: spacing.xs,
    fontSize: typography.fontSize.sm,
    fontWeight: typography.fontWeight.medium,
    color: error || validationError ? colors.error.main : colors.text.primary,
    fontFamily: typography.fontFamily.base,
  });

  return (
    <Box sx={{ marginBottom: spacing.md }}>
      <Typography variant="body2" component="label" sx={getLabelStyles()}>
        {label}
        {required && <span style={{ color: colors.error.main, marginLeft: '2px' }}>*</span>}
      </Typography>

      <input
        type={type}
        name={name}
        value={value || ''}
        onChange={handleChange}
        onBlur={handleBlur}
        onFocus={handleFocus}
        required={required}
        disabled={disabled}
        placeholder={placeholder}
        style={getInputStyles()}
        {...props}
      />

      {/* Error Messages */}
      {(error || validationError) && (
        <Typography
          variant="caption"
          sx={{
            color: colors.error.main,
            display: 'block',
            marginTop: spacing.xs,
            fontSize: typography.fontSize.xs,
          }}
        >
          {error || validationError}
        </Typography>
      )}

      {/* Warning Messages */}
      {decimalWarning && !error && !validationError && (
        <Typography
          variant="caption"
          sx={{
            color: colors.warning.dark,
            display: 'block',
            marginTop: spacing.xs,
            fontSize: typography.fontSize.xs,
          }}
        >
          {decimalWarning}
        </Typography>
      )}
    </Box>
  );
};

FormInput.propTypes = {
  label: PropTypes.string.isRequired,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  onChange: PropTypes.func.isRequired,
  required: PropTypes.bool,
  disabled: PropTypes.bool,
  error: PropTypes.string,
  placeholder: PropTypes.string,
  type: PropTypes.oneOf(['text', 'number', 'password', 'email', 'tel', 'url']),
  name: PropTypes.string,
  // Number formatting
  isNumberFormat: PropTypes.bool,
  decimalPlaces: PropTypes.number,
  // Validation
  minValue: PropTypes.number,
  maxValue: PropTypes.number,
  minLength: PropTypes.number,
  maxLength: PropTypes.number,
  // Style options
  fullWidth: PropTypes.bool,
  size: PropTypes.oneOf(['small', 'medium']),
  // Callbacks
  onFocus: PropTypes.func,
  onBlur: PropTypes.func,
  onWarning: PropTypes.func,
};

export default FormInput;

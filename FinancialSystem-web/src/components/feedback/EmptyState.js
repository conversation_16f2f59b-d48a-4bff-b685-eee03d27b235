/**
 * EmptyState Component
 *
 * Displays empty data states with proper messaging
 * Supports different empty state types (no data, no search results, no access)
 * Has customizable illustrations and actions
 * Uses consistent styling and typography from unified style constants
 */

import PropTypes from 'prop-types';
import { Box, Typography, Button } from '@mui/material';
import {
  Inbox as InboxIcon,
  Search as SearchIcon,
  Lock as LockIcon,
  Error as ErrorIcon,
  CloudOff as CloudOffIcon,
  FilterList as FilterIcon,
  Add as AddIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

// Import unified style constants
import { colors, spacing, typography, transitions } from 'constants/styleConstants';

// Styled components
const EmptyStateContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  padding: spacing['4xl'],
  textAlign: 'center',
  minHeight: '300px',
}));

const EmptyStateIcon = styled(Box)(({ theme, stateType }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  width: '80px',
  height: '80px',
  borderRadius: '50%',
  marginBottom: spacing.xl,
  transition: transitions.all,

  ...(stateType === 'no-data' && {
    backgroundColor: colors.grey[100],
    color: colors.grey[400],
  }),

  ...(stateType === 'no-search' && {
    backgroundColor: colors.info.light,
    color: colors.info.main,
  }),

  ...(stateType === 'no-access' && {
    backgroundColor: colors.warning.light,
    color: colors.warning.main,
  }),

  ...(stateType === 'error' && {
    backgroundColor: colors.error.light,
    color: colors.error.main,
  }),

  ...(stateType === 'offline' && {
    backgroundColor: colors.grey[200],
    color: colors.grey[500],
  }),

  ...(stateType === 'no-filter' && {
    backgroundColor: colors.secondary.light,
    color: colors.secondary.main,
  }),
}));

const EmptyStateTitle = styled(Typography)(({ theme }) => ({
  fontSize: typography.fontSize.xl,
  fontWeight: typography.fontWeight.semibold,
  color: colors.text.primary,
  marginBottom: spacing.sm,
}));

const EmptyStateDescription = styled(Typography)(({ theme }) => ({
  fontSize: typography.fontSize.base,
  color: colors.text.secondary,
  lineHeight: typography.lineHeight.normal,
  marginBottom: spacing.xl,
  maxWidth: '400px',
}));

const EmptyStateActions = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  gap: spacing.sm,
  alignItems: 'center',

  [theme.breakpoints.up('sm')]: {
    flexDirection: 'row',
    gap: spacing.md,
  },
}));

const ActionButton = styled(Button)(({ theme, variant }) => ({
  minWidth: '120px',
  height: '36px',
  textTransform: 'none',
  fontSize: typography.fontSize.sm,
  fontWeight: typography.fontWeight.medium,
  transition: transitions.all,

  ...(variant === 'primary' && {
    backgroundColor: colors.primary.main,
    color: colors.primary.contrastText,
    '&:hover': {
      backgroundColor: colors.primary.dark,
    },
  }),

  ...(variant === 'secondary' && {
    backgroundColor: 'transparent',
    color: colors.text.secondary,
    border: `1px solid ${colors.border.main}`,
    '&:hover': {
      backgroundColor: colors.background.hover,
    },
  }),
}));

// Empty state type configurations
const EMPTY_STATE_CONFIGS = {
  'no-data': {
    icon: InboxIcon,
    defaultTitle: '暂无数据',
    defaultDescription: '当前没有任何数据可显示',
    primaryAction: { label: '添加数据', icon: AddIcon, variant: 'primary' },
    secondaryAction: { label: '刷新', icon: RefreshIcon, variant: 'secondary' },
  },
  'no-search': {
    icon: SearchIcon,
    defaultTitle: '没有找到结果',
    defaultDescription: '请尝试调整搜索关键词或筛选条件',
    primaryAction: { label: '清除搜索', icon: SearchIcon, variant: 'primary' },
    secondaryAction: { label: '重置筛选', icon: FilterIcon, variant: 'secondary' },
  },
  'no-access': {
    icon: LockIcon,
    defaultTitle: '没有访问权限',
    defaultDescription: '您没有权限查看此内容，请联系管理员申请权限',
    primaryAction: { label: '申请权限', icon: LockIcon, variant: 'primary' },
    secondaryAction: { label: '返回', icon: RefreshIcon, variant: 'secondary' },
  },
  error: {
    icon: ErrorIcon,
    defaultTitle: '加载失败',
    defaultDescription: '数据加载失败，请检查网络连接或稍后重试',
    primaryAction: { label: '重新加载', icon: RefreshIcon, variant: 'primary' },
    secondaryAction: { label: '返回', icon: RefreshIcon, variant: 'secondary' },
  },
  offline: {
    icon: CloudOffIcon,
    defaultTitle: '网络连接异常',
    defaultDescription: '请检查您的网络连接状态',
    primaryAction: { label: '重试', icon: RefreshIcon, variant: 'primary' },
    secondaryAction: null,
  },
  'no-filter': {
    icon: FilterIcon,
    defaultTitle: '筛选结果为空',
    defaultDescription: '当前筛选条件下没有匹配的数据',
    primaryAction: { label: '清除筛选', icon: FilterIcon, variant: 'primary' },
    secondaryAction: { label: '查看全部', icon: InboxIcon, variant: 'secondary' },
  },
};

const EmptyState = ({
  type = 'no-data',
  title = '',
  description = '',
  illustration = null,
  actions = null,
  onPrimaryAction = null,
  onSecondaryAction = null,
  primaryActionText = '',
  secondaryActionText = '',
  showActions = true,
  className = '',
  ...props
}) => {
  const config = EMPTY_STATE_CONFIGS[type] || EMPTY_STATE_CONFIGS['no-data'];
  const IconComponent = config.icon;

  const emptyTitle = title || config.defaultTitle;
  const emptyDescription = description || config.defaultDescription;

  const primaryAction = config.primaryAction
    ? {
      label: primaryActionText || config.primaryAction.label,
      icon: config.primaryAction.icon,
      variant: config.primaryAction.variant,
    }
    : null;

  const secondaryAction = config.secondaryAction
    ? {
      label: secondaryActionText || config.secondaryAction.label,
      icon: config.secondaryAction.icon,
      variant: config.secondaryAction.variant,
    }
    : null;

  const handlePrimaryAction = () => {
    if (onPrimaryAction) {
      onPrimaryAction();
    }
  };

  const handleSecondaryAction = () => {
    if (onSecondaryAction) {
      onSecondaryAction();
    }
  };

  return (
    <EmptyStateContainer className={className} {...props}>
      {illustration ? (
        <Box sx={{ mb: spacing.xl }}>{illustration}</Box>
      ) : (
        <EmptyStateIcon stateType={type}>
          <IconComponent fontSize="large" />
        </EmptyStateIcon>
      )}

      <EmptyStateTitle variant="h6">{emptyTitle}</EmptyStateTitle>

      <EmptyStateDescription variant="body2">{emptyDescription}</EmptyStateDescription>

      {showActions && (actions || primaryAction || secondaryAction) && (
        <EmptyStateActions>
          {actions ? (
            actions
          ) : (
            <>
              {primaryAction && (
                <ActionButton
                  variant={primaryAction.variant}
                  onClick={handlePrimaryAction}
                  startIcon={<primaryAction.icon fontSize="small" />}
                >
                  {primaryAction.label}
                </ActionButton>
              )}

              {secondaryAction && (
                <ActionButton
                  variant={secondaryAction.variant}
                  onClick={handleSecondaryAction}
                  startIcon={<secondaryAction.icon fontSize="small" />}
                >
                  {secondaryAction.label}
                </ActionButton>
              )}
            </>
          )}
        </EmptyStateActions>
      )}
    </EmptyStateContainer>
  );
};

EmptyState.propTypes = {
  /**
   * Type of empty state
   */
  type: PropTypes.oneOf(['no-data', 'no-search', 'no-access', 'error', 'offline', 'no-filter']),

  /**
   * Custom title (optional, uses default based on type)
   */
  title: PropTypes.string,

  /**
   * Custom description (optional, uses default based on type)
   */
  description: PropTypes.string,

  /**
   * Custom illustration component
   */
  illustration: PropTypes.node,

  /**
   * Custom actions component (overrides default actions)
   */
  actions: PropTypes.node,

  /**
   * Callback for primary action
   */
  onPrimaryAction: PropTypes.func,

  /**
   * Callback for secondary action
   */
  onSecondaryAction: PropTypes.func,

  /**
   * Custom text for primary action button
   */
  primaryActionText: PropTypes.string,

  /**
   * Custom text for secondary action button
   */
  secondaryActionText: PropTypes.string,

  /**
   * Whether to show action buttons
   */
  showActions: PropTypes.bool,

  /**
   * Additional CSS class name
   */
  className: PropTypes.string,
};

EmptyState.defaultProps = {
  type: 'no-data',
  title: '',
  description: '',
  illustration: null,
  actions: null,
  onPrimaryAction: null,
  onSecondaryAction: null,
  primaryActionText: '',
  secondaryActionText: '',
  showActions: true,
  className: '',
};

export default EmptyState;

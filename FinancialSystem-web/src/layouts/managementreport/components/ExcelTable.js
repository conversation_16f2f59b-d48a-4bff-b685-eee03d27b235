import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
} from '@mui/material';
import PropTypes from 'prop-types';

// 样式定义
const headerCellStyle = {
  backgroundColor: '#f5f5f5',
  fontWeight: 'bold',
  border: '1px solid #e0e0e0',
  padding: '6px',
  textAlign: 'center',
};

const bodyCellStyle = {
  border: '1px solid #e0e0e0',
  padding: '6px',
  height: '29px',
};

function ExcelTable({ headers, data }) {
  // 检查是否有数据
  if (!headers || !data || headers.length === 0 || data.length === 0) {
    return (
      <Paper sx={{ p: 2, textAlign: 'center' }}>
        <Typography variant="body1">无数据可显示</Typography>
      </Paper>
    );
  }

  // 表格单元格内容格式化
  const formatCellContent = value => {
    if (value === undefined || value === null) {
      return '';
    }

    // 如果是数字，使用千分位分隔符格式化
    if (typeof value === 'number') {
      return value.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      });
    }

    return value;
  };

  // 确定单元格对齐方式
  const getCellAlignment = (value, colIndex) => {
    // 如果是第一列（序号列），居中对齐
    if (colIndex === 0) {
      return 'center';
    }

    // 如果是数字，右对齐
    if (typeof value === 'number') {
      return 'right';
    }

    // 默认左对齐
    return 'left';
  };

  return (
    <TableContainer component={Paper} sx={{ maxHeight: 'calc(100vh - 400px)', overflow: 'auto' }}>
      <Table stickyHeader sx={{ minWidth: 1000 }}>
        <TableHead>
          <TableRow>
            {headers.map((header, index) => (
              <TableCell
                key={index}
                sx={{
                  ...headerCellStyle,
                  width: index === 0 ? 50 : index === 1 ? 300 : 120,
                }}
              >
                {header}
              </TableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {data.map((row, rowIndex) => (
            <TableRow key={rowIndex}>
              {row.map((cell, cellIndex) => (
                <TableCell
                  key={cellIndex}
                  sx={bodyCellStyle}
                  align={getCellAlignment(cell, cellIndex)}
                >
                  {formatCellContent(cell)}
                </TableCell>
              ))}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
}

ExcelTable.propTypes = {
  headers: PropTypes.array.isRequired,
  data: PropTypes.array.isRequired,
};

export default ExcelTable;

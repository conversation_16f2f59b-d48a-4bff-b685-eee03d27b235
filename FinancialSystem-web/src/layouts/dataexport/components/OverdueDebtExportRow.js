import React, { useState } from 'react';
import {
  Card,
  Grid,
  FormControl,
  Select,
  MenuItem,
  TextField,
  Snackbar,
  Alert,
  CircularProgress,
  useTheme,
  alpha,
  Fade,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  LinearProgress,
  InputLabel,
} from '@mui/material';
import {
  FileDownload as FileDownloadIcon,
  Preview as PreviewIcon,
  Close as CloseIcon,
} from '@mui/icons-material';

// Material Dashboard 2 React components
import MDBox from 'components/MDBox';
import MDButton from 'components/MDButton';
import MDTypography from 'components/MDTypography';
import StandardBarChart from 'components/charts/StandardBarChart';
import EnhancedGenericDataTable from 'components/tables/EnhancedGenericDataTable';

// 导出服务
import { exportCompleteOverdueReport } from '../services/exportService';

/**
 * 逾期债权清收统计表导出行组件
 * 提供年份、月份、金额限制选择和一键导出功能，采用现代化美观设计
 */
const OverdueDebtExportRow = () => {
  const theme = useTheme();

  // 状态管理
  const [year, setYear] = useState(new Date().getFullYear().toString());
  const [month, setMonth] = useState((new Date().getMonth() + 1).toString());
  const [maxAmount, setMaxAmount] = useState('10');
  const [isExporting, setIsExporting] = useState(false);
  const [previewOpen, setPreviewOpen] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [notification, setNotification] = useState({
    open: false,
    message: '',
    severity: 'success',
  });

  // 预览数据
  const [previewData] = useState([
    {
      id: 1,
      companyName: '上海ABC有限公司',
      amount: 150.5,
      dueDate: '2024-06-15',
      overdueDay: 24,
      category: '贸易欠款',
    },
    {
      id: 2,
      companyName: '北京XYZ实业集团',
      amount: 280.3,
      dueDate: '2024-06-08',
      overdueDay: 31,
      category: '服务费用',
    },
    {
      id: 3,
      companyName: '深圳DEF科技公司',
      amount: 95.8,
      dueDate: '2024-06-20',
      overdueDay: 19,
      category: '贸易欠款',
    },
  ]);

  const previewColumns = [
    { Header: '公司名称', accessor: 'companyName', width: '30%' },
    { Header: '金额(万元)', accessor: 'amount', width: '15%' },
    { Header: '到期日期', accessor: 'dueDate', width: '15%' },
    { Header: '逾期天数', accessor: 'overdueDay', width: '15%' },
    { Header: '类别', accessor: 'category', width: '25%' },
  ];

  const previewChartData = {
    labels: ['贸易欠款', '服务费用', '租赁费用', '其他'],
    datasets: [
      {
        label: '数量分布',
        data: [450, 280, 320, 200],
        backgroundColor: [
          'rgba(54, 162, 235, 0.8)',
          'rgba(255, 99, 132, 0.8)',
          'rgba(255, 205, 86, 0.8)',
          'rgba(75, 192, 192, 0.8)',
        ],
      },
    ],
  };

  // 预览功能
  const handlePreview = () => {
    setPreviewOpen(true);
  };

  const handlePreviewClose = () => {
    setPreviewOpen(false);
  };

  const handleExportFromPreview = () => {
    setPreviewOpen(false);
    handleExport();
  };

  // 生成年份选项（2020-2030）
  const yearOptions = [];
  for (let i = 2020; i <= 2030; i++) {
    yearOptions.push(i.toString());
  }

  // 月份选项
  const monthOptions = [
    { value: '1', label: '1月' },
    { value: '2', label: '2月' },
    { value: '3', label: '3月' },
    { value: '4', label: '4月' },
    { value: '5', label: '5月' },
    { value: '6', label: '6月' },
    { value: '7', label: '7月' },
    { value: '8', label: '8月' },
    { value: '9', label: '9月' },
    { value: '10', label: '10月' },
    { value: '11', label: '11月' },
    { value: '12', label: '12月' },
  ];

  // 处理导出
  const handleExport = async () => {
    if (isExporting) {
      return;
    }

    // 简单的参数验证
    if (!year || !month || !maxAmount) {
      setNotification({
        open: true,
        message: '请完善所有参数后再导出',
        severity: 'warning',
      });
      return;
    }

    if (parseFloat(maxAmount) <= 0) {
      setNotification({
        open: true,
        message: '最大金额必须大于0',
        severity: 'warning',
      });
      return;
    }

    setIsExporting(true);
    setExportProgress(0);

    try {
      const result = await exportCompleteOverdueReport(year, month, maxAmount, progress => {
        setExportProgress(progress);
      });

      setNotification({
        open: true,
        message: `报表导出成功！文件名：${result.fileName}`,
        severity: 'success',
      });
    } catch (error) {
      console.error('导出失败:', error);
      setNotification({
        open: true,
        message: error.message || '导出失败，请重试',
        severity: 'error',
      });
    } finally {
      setIsExporting(false);
      setExportProgress(0);
    }
  };

  // 关闭通知
  const handleCloseNotification = () => {
    setNotification({ ...notification, open: false });
  };

  return (
    <>
      <Card
        sx={{
          borderRadius: '16px',
          boxShadow: '0 2px 12px 0 rgba(0,0,0,.08)',
          transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
          border: '1px solid rgba(0,0,0,0.05)',
          '&:hover': {
            boxShadow: '0 8px 25px 0 rgba(0,0,0,.15)',
            transform: 'translateY(-2px)',
          },
        }}
      >
        <MDBox p={4}>
          {/* 卡片头部 */}
          <MDBox display="flex" alignItems="center" mb={4}>
            <MDBox
              bgColor="success"
              variant="gradient"
              borderRadius="xl"
              shadow="md"
              p={2.5}
              mr={3}
              sx={{
                background: 'linear-gradient(135deg, #4CAF50 0%, #45a049 100%)',
              }}
            >
              <FileDownloadIcon sx={{ color: 'white', fontSize: '28px' }} />
            </MDBox>
            <MDBox>
              <MDTypography variant="h5" fontWeight="bold" color="dark">
                逾期债权明细表导出
              </MDTypography>
              <MDTypography variant="body2" color="text" sx={{ mt: 0.5, opacity: 0.8 }}>
                导出指定条件的逾期债权详细数据
              </MDTypography>
            </MDBox>
          </MDBox>

          {/* 参数选择区域 */}
          <Grid container spacing={3} alignItems="flex-end">
            {/* 年份选择 */}
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth variant="outlined" size="medium">
                <InputLabel>年份</InputLabel>
                <Select
                  value={year}
                  onChange={e => setYear(e.target.value)}
                  disabled={isExporting}
                  sx={{ height: '56px' }}
                >
                  {yearOptions.map(year => (
                    <MenuItem key={year} value={year}>
                      {year}年
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* 月份选择 */}
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth variant="outlined" size="medium">
                <InputLabel>月份</InputLabel>
                <Select
                  value={month}
                  onChange={e => setMonth(e.target.value)}
                  disabled={isExporting}
                  sx={{ height: '56px' }}
                >
                  {monthOptions.map(month => (
                    <MenuItem key={month.value} value={month.value}>
                      {month.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* 最大金额 */}
            <Grid item xs={12} sm={6} md={3}>
              <TextField
                fullWidth
                variant="outlined"
                label="最大金额 (万元)"
                type="number"
                value={maxAmount}
                onChange={e => setMaxAmount(e.target.value)}
                disabled={isExporting}
                inputProps={{ min: 0, step: 0.1 }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    height: '56px',
                  },
                }}
              />
            </Grid>

            {/* 操作按钮 */}
            <Grid item xs={12} sm={6} md={3}>
              <MDBox display="flex" gap={1} height="56px" alignItems="center">
                <MDButton
                  variant="outlined"
                  color="info"
                  onClick={handlePreview}
                  disabled={isExporting}
                  sx={{ height: '36px', flex: 1 }}
                >
                  <PreviewIcon sx={{ mr: 1 }} />
                  预览
                </MDButton>
                <MDButton
                  variant="gradient"
                  color="success"
                  onClick={handleExport}
                  disabled={isExporting}
                  sx={{ height: '36px', flex: 1, minWidth: '80px' }}
                >
                  {isExporting ? (
                    <CircularProgress size={16} color="inherit" />
                  ) : (
                    <>
                      <FileDownloadIcon sx={{ mr: 1, fontSize: '18px' }} />
                      导出
                    </>
                  )}
                </MDButton>
              </MDBox>
            </Grid>
          </Grid>
        </MDBox>
      </Card>

      {/* 导出进度指示器 */}
      {isExporting && (
        <MDBox
          position="fixed"
          top={0}
          left={0}
          width="100%"
          height="100%"
          bgcolor="rgba(0, 0, 0, 0.5)"
          display="flex"
          alignItems="center"
          justifyContent="center"
          zIndex={9999}
        >
          <Card sx={{ p: 4, borderRadius: '12px', maxWidth: '400px' }}>
            <MDBox textAlign="center">
              <CircularProgress size={60} color="info" sx={{ mb: 2 }} />
              <MDTypography variant="h6" mb={1}>
                正在导出数据...
              </MDTypography>
              <MDTypography variant="body2" color="text">
                请稍候，正在处理您的导出请求
              </MDTypography>
              <LinearProgress
                variant="determinate"
                value={exportProgress}
                sx={{ mt: 2, height: '8px', borderRadius: '4px' }}
              />
              <MDTypography variant="caption" color="text" mt={1}>
                {exportProgress}% 完成
              </MDTypography>
            </MDBox>
          </Card>
        </MDBox>
      )}

      {/* 预览模态窗口 */}
      <Dialog
        open={previewOpen}
        onClose={handlePreviewClose}
        maxWidth="lg"
        fullWidth
        PaperProps={{
          sx: { borderRadius: '12px', minHeight: '70vh' },
        }}
      >
        <DialogTitle>
          <MDBox display="flex" alignItems="center" justifyContent="space-between">
            <MDBox display="flex" alignItems="center">
              <PreviewIcon sx={{ color: 'info.main', mr: 1 }} />
              <MDTypography variant="h6">数据预览</MDTypography>
            </MDBox>
            <IconButton onClick={handlePreviewClose}>
              <CloseIcon />
            </IconButton>
          </MDBox>
        </DialogTitle>

        <DialogContent>
          <MDBox mb={2}>
            <StandardBarChart
              icon={{ component: 'assessment', color: 'primary' }}
              title="数据分布概览"
              description="按分类统计的数据分布"
              chart={previewChartData}
              height="300px"
            />
          </MDBox>

          <EnhancedGenericDataTable
            table={{
              columns: previewColumns,
              rows: previewData,
            }}
            showToolbar={false}
            entriesPerPage={false}
            canSearch={true}
            showColumnsButton={true}
            showRefreshButton={false}
          />
        </DialogContent>

        <DialogActions>
          <MDButton variant="outlined" color="secondary" onClick={handlePreviewClose}>
            关闭
          </MDButton>
          <MDButton variant="gradient" color="success" onClick={handleExportFromPreview}>
            <FileDownloadIcon sx={{ mr: 1 }} />
            确认导出
          </MDButton>
        </DialogActions>
      </Dialog>

      {/* 通知组件 */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
        TransitionComponent={Fade}
      >
        <Alert
          onClose={handleCloseNotification}
          severity={notification.severity}
          variant="filled"
          sx={{
            width: '100%',
            borderRadius: 3,
            boxShadow: `0 8px 32px ${alpha(
              notification.severity === 'success'
                ? theme.palette.success.main
                : notification.severity === 'warning'
                  ? theme.palette.warning.main
                  : theme.palette.error.main,
              0.3,
            )}`,
            '& .MuiAlert-icon': {
              fontSize: '1.5rem',
            },
            '& .MuiAlert-message': {
              fontSize: '1rem',
              fontWeight: 600,
            },
          }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </>
  );
};

export default OverdueDebtExportRow;

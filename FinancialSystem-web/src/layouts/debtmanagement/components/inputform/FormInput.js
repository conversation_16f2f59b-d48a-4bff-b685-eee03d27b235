import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Box, Typography } from '@mui/material';

const FormInput = ({
  label,
  value,
  onChange,
  required,
  error,
  placeholder,
  type = 'text',
  isNumberFormat = false, // 是否为需要四舍五入的数字输入
  decimalPlaces = 2, // 小数位数
  warnings = {}, // 警告信息对象
  onWarning = () => {}, // 警告回调
}) => {
  // 内部状态用于追踪是否显示小数位警告
  const [decimalWarning, setDecimalWarning] = useState('');
  // 检查输入的数字是否超过指定的小数位数
  useEffect(() => {
    // 仅当值变化时才执行检查
    if (isNumberFormat && type === 'number' && value) {
      const valueStr = String(value);
      let currentWarning = '';

      if (valueStr.includes('.')) {
        const decimalPart = valueStr.split('.')[1];
        if (decimalPart && decimalPart.length > decimalPlaces) {
          currentWarning = `请保留${decimalPlaces}位小数`;
        }
      }

      if (decimalWarning !== currentWarning) {
        setDecimalWarning(currentWarning);
        // 仅当警告状态改变时才通知父组件
        onWarning(label, currentWarning);
      }
    }
  }, [value, isNumberFormat, decimalPlaces, type, label, decimalWarning, onWarning]);

  // 保持onWarning的引用不触发useEffect

  // 处理数字输入变化，四舍五入在失去焦点时进行
  const handleChange = e => {
    // 直接传递原始事件对象
    onChange(e);
  };

  // 处理失去焦点事件，当离开输入框时立即四舍五入为两位小数
  const handleBlur = e => {
    if (isNumberFormat && type === 'number' && e.target.value) {
      try {
        const originalValue = e.target.value;
        const hasMultipleDecimals =
          originalValue.includes('.') &&
          originalValue.split('.')[1] &&
          originalValue.split('.')[1].length > decimalPlaces;

        // 执行四舍五入处理
        const rounded = parseFloat(parseFloat(originalValue).toFixed(decimalPlaces));

        // 如果值发生变化，记录一下
        if (parseFloat(originalValue) !== rounded || hasMultipleDecimals) {
        }

        // 创建一个模拟事件对象以传递给onChange
        const simulatedEvent = {
          target: {
            value: rounded,
            name: e.target.name,
          },
          type: 'blur',
        };

        // 调用onChange回调传递四舍五入后的值
        onChange(simulatedEvent);
      } catch (err) {
        console.error('四舍五入处理出错:', err);
      }
    }
  };

  return (
    <Box sx={{ mb: 1.5 }}>
      <Typography
        variant="body2"
        component="label"
        sx={{
          display: 'block',
          mb: 0.5,
          fontSize: '13px',
          fontWeight: 500,
          color: '#333',
        }}
      >
        {label} {required && <span style={{ color: '#d32f2f' }}>*</span>}
      </Typography>
      <input
        type={type}
        value={value}
        onChange={handleChange}
        onBlur={handleBlur}
        required={required}
        placeholder={placeholder}
        style={{
          width: '100%',
          padding: '7px 12px',
          borderRadius: '4px',
          border: '1px solid #ccc',
          height: '36px',
          boxSizing: 'border-box',
          fontSize: '14px',
        }}
      />
      {error && (
        <Typography variant="caption" sx={{ color: '#d32f2f', display: 'block', mt: 0.5 }}>
          {error}
        </Typography>
      )}
      {decimalWarning && (
        <Typography variant="caption" sx={{ color: '#f57c00', display: 'block', mt: 0.5 }}>
          {decimalWarning}
        </Typography>
      )}
      {warnings[label] && !decimalWarning && (
        <Typography variant="caption" sx={{ color: '#f57c00', display: 'block', mt: 0.5 }}>
          {warnings[label]}
        </Typography>
      )}
    </Box>
  );
};
// 添加 PropTypes 校验
FormInput.propTypes = {
  label: PropTypes.string.isRequired,
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  onChange: PropTypes.func.isRequired,
  required: PropTypes.bool,
  error: PropTypes.string,
  placeholder: PropTypes.string,
  type: PropTypes.string,
  isNumberFormat: PropTypes.bool,
  decimalPlaces: PropTypes.number,
  warnings: PropTypes.object,
  onWarning: PropTypes.func,
};
export default FormInput;

import React, { useState } from 'react';
import PropTypes from 'prop-types';
import ChartJS from '../../../utils/chartConfig';
import ChartDataLabels from 'chartjs-plugin-datalabels';
import { Chart } from 'react-chartjs-2';
import { Box } from '@mui/material';
import DisposalMethodsDetailModal from './DisposalMethodsDetailModal';
import {
  createSafeDatalabelsConfig,
  createSafeTooltipCallbacks,
} from '../../../utils/chartDataHelpers';

// 注册 DataLabels 插件
ChartJS.register(ChartDataLabels);

/**
 * 存量债权清收处置方式统计图表组件
 * @param {Object} props
 * @param {Array} props.data - 处置方式数据数组，每项包含 method, amount, percentage
 * @param {String} props.title - 图表标题
 * @param {Function} props.onDetailClick - 详细信息按钮点击回调
 * @returns {JSX.Element}
 */
const DisposalMethodsChart = ({ data, title = '存量债权清收方式', onDetailClick }) => {
  const [detailModalOpen, setDetailModalOpen] = useState(false);

  // 添加错误边界处理
  try {
    // 增强数据验证 - 确保data是数组
    let rawData = data;
    if (!Array.isArray(rawData)) {
      console.warn('DisposalMethodsChart: data is not an array, trying to convert:', rawData);
      if (rawData && typeof rawData === 'object') {
        // 如果是对象，尝试提取数组属性
        rawData = rawData.data || rawData.list || rawData.items || [];
      } else {
        rawData = [];
      }
    }

    // 数据验证和过滤
    const filteredData = (rawData || [])
      .filter(item => {
        // 确保数据项结构正确
        if (!item || typeof item !== 'object') {
          console.warn('DisposalMethodsChart: Invalid item structure:', item);
          return false;
        }
        // 确保method和amount字段存在且有效
        const method = item.method || item.处置方式;
        const amount = typeof item.amount === 'number' ? item.amount : parseFloat(item.amount) || 0;
        return method && amount > 0;
      })
      .map(item => ({
        method: item.method || item.处置方式 || '未知方式',
        amount: typeof item.amount === 'number' ? item.amount : parseFloat(item.amount) || 0,
        percentage:
          typeof item.percentage === 'number' ? item.percentage : parseFloat(item.percentage) || 0,
      }));

    // 如果没有数据，显示暂无数据提示
    if (!filteredData || filteredData.length === 0) {
      return (
        <Box
          className="p-6 bg-white rounded-lg shadow-md"
          sx={{
            position: 'relative',
            width: '100%',
            maxWidth: '100%',
            marginBottom: '20px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '400px',
            overflow: 'hidden',
          }}
        >
          <Box sx={{ textAlign: 'center', color: 'text.secondary' }}>
            <Box sx={{ fontSize: '16px', mb: 1 }}>{title}</Box>
            <Box sx={{ fontSize: '14px' }}>暂无数据</Box>
          </Box>
        </Box>
      );
    }

    // 提取标签和数据（数据已经是万元单位，直接使用）
    const labels = filteredData.map(item => item.method);
    const amounts = filteredData.map(item => {
      const amount = item.amount;
      // 确保amount是有效数字，如果不是则返回0
      return typeof amount === 'number' && !isNaN(amount) && isFinite(amount) ? amount : 0;
    });

    // 绿色系配色（4种不同深浅）
    const greenColors = [
      'rgba(46, 125, 50, 0.8)', // 深绿色
      'rgba(76, 175, 80, 0.8)', // 标准绿色
      'rgba(129, 199, 132, 0.8)', // 浅绿色
      'rgba(165, 214, 167, 0.8)', // 更浅绿色
    ];

    const greenBorderColors = [
      'rgba(46, 125, 50, 1)',
      'rgba(76, 175, 80, 1)',
      'rgba(129, 199, 132, 1)',
      'rgba(165, 214, 167, 1)',
    ];

    // 确保数据长度一致且有效
    if (labels.length !== amounts.length) {
      console.error('DisposalMethodsChart: labels and amounts length mismatch', {
        labels: labels.length,
        amounts: amounts.length,
      });
      throw new Error('数据长度不一致');
    }

    // 图表数据配置 - 只显示柱状图，去掉线条
    const chartData = {
      labels,
      datasets: [
        {
          type: 'bar',
          label: '金额',
          data: amounts.map((value, index) => ({
            x: labels[index],
            y: value,
          })),
          backgroundColor: filteredData.map((_, index) => greenColors[index % greenColors.length]),
          borderColor: filteredData.map(
            (_, index) => greenBorderColors[index % greenBorderColors.length],
          ),
          borderWidth: 1,
          borderRadius: 4,
          borderSkipped: false,
          maxBarThickness: 48,
          // 启用自动解析，让Chart.js正确处理数据
          parsing: {
            xAxisKey: 'x',
            yAxisKey: 'y',
          },
        },
      ],
    };

    // 图表选项配置
    const options = {
      responsive: true,
      maintainAspectRatio: false,
      interaction: {
        intersect: false,
        mode: 'index',
      },
      plugins: {
        legend: {
          display: false,
        },
        title: {
          display: true,
          text: title,
          font: {
            size: 16,
            weight: 'bold',
          },
          padding: {
            top: 10,
            bottom: 20,
          },
        },
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleFont: {
            size: 14,
            weight: 'medium',
          },
          bodyFont: {
            size: 12,
          },
          cornerRadius: 8,
          padding: 12,
          displayColors: true,
          callbacks: createSafeTooltipCallbacks({ unit: '万元', decimals: 2 }),
        },
        datalabels: createSafeDatalabelsConfig({
          formatter: value => Math.round(value).toString(),
        }),
      },
      scales: {
        x: {
          display: true,
          grid: {
            display: false,
          },
          ticks: {
            font: {
              size: 12,
            },
            maxRotation: labels.length > 4 ? 45 : 0,
            minRotation: labels.length > 4 ? 45 : 0,
          },
        },
        y: {
          type: 'linear',
          display: true,
          position: 'left',
          title: {
            display: false,
          },
          beginAtZero: true,
          ticks: {
            callback: function (value) {
              return value.toFixed(0);
            },
            // 让Chart.js自动计算合适的刻度间隔
            maxTicksLimit: 8,
            // 确保刻度值是整数
            stepSize: undefined, // 让Chart.js自动计算
          },
        },
      },
    };

    return (
      <Box
        className="p-6 bg-white rounded-lg shadow-md"
        sx={{
          position: 'relative',
          width: '100%',
          maxWidth: '100%',
          marginBottom: '20px',
          overflow: 'hidden',
        }}
      >
        {/* 图表容器 */}
        <Box
          sx={{
            height: '350px',
            width: '100%',
            pt: 2, // 为按钮留出空间
          }}
        >
          <Chart type="bar" data={chartData} options={options} />
        </Box>

        {/* 详细信息弹窗 */}
        <DisposalMethodsDetailModal
          open={detailModalOpen}
          onClose={() => setDetailModalOpen(false)}
          data={filteredData}
        />
      </Box>
    );
  } catch (error) {
    console.error('DisposalMethodsChart rendering error:', error);
    return (
      <Box
        className="p-6 bg-white rounded-lg shadow-md"
        sx={{
          position: 'relative',
          width: '100%',
          maxWidth: '100%',
          marginBottom: '20px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          height: '400px',
          overflow: 'hidden',
        }}
      >
        <Box sx={{ textAlign: 'center', color: 'error.main' }}>
          <Box sx={{ fontSize: '16px', mb: 1 }}>{title}</Box>
          <Box sx={{ fontSize: '14px' }}>图表加载出错，请刷新页面重试</Box>
        </Box>
      </Box>
    );
  }
};

DisposalMethodsChart.propTypes = {
  data: PropTypes.arrayOf(
    PropTypes.shape({
      method: PropTypes.string.isRequired,
      amount: PropTypes.number.isRequired,
      percentage: PropTypes.number.isRequired,
    }),
  ),
  title: PropTypes.string,
  onDetailClick: PropTypes.func,
};

DisposalMethodsChart.defaultProps = {
  data: [],
  title: '存量债权清收方式',
};

export default DisposalMethodsChart;

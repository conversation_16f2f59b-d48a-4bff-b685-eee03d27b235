/**
 * Unified Style Constants for FinancialSystem
 *
 * This file contains all the style constants used throughout the application
 * to ensure consistency and maintainability.
 */

// Color Palette
export const colors = {
  // Primary Colors
  primary: {
    main: '#1976d2',
    light: '#42a5f5',
    dark: '#1565c0',
    contrastText: '#ffffff',
  },

  // Secondary Colors
  secondary: {
    main: '#283593',
    light: '#3f51b5',
    dark: '#1a237e',
    contrastText: '#ffffff',
  },

  // Status Colors
  success: {
    main: '#4caf50',
    light: '#66bb6a',
    dark: '#43a047',
    contrastText: '#ffffff',
  },

  error: {
    main: '#f44336',
    light: '#e57373',
    dark: '#d32f2f',
    contrastText: '#ffffff',
  },

  warning: {
    main: '#ff9800',
    light: '#ffb74d',
    dark: '#f57c00',
    contrastText: 'rgba(0, 0, 0, 0.87)',
  },

  info: {
    main: '#2196f3',
    light: '#64b5f6',
    dark: '#1976d2',
    contrastText: '#ffffff',
  },

  // Text Colors
  text: {
    primary: '#333333',
    secondary: '#757575',
    disabled: '#9e9e9e',
    hint: '#9e9e9e',
  },

  // Background Colors
  background: {
    default: '#f5f5f5',
    paper: '#ffffff',
    light: '#f9f9f9',
    hover: '#f5f5f5',
    selected: '#f5f9ff',
  },

  // Border Colors
  border: {
    main: '#e0e0e0',
    light: '#f0f0f0',
    dark: '#bdbdbd',
    input: '#d2d6da',
  },

  // Additional Colors
  grey: {
    50: '#fafafa',
    100: '#f5f5f5',
    200: '#eeeeee',
    300: '#e0e0e0',
    400: '#bdbdbd',
    500: '#9e9e9e',
    600: '#757575',
    700: '#616161',
    800: '#424242',
    900: '#212121',
  },
};

// Typography
export const typography = {
  // Font Family
  fontFamily: {
    base: '"Roboto", "Helvetica", "Arial", sans-serif',
    mono: '"Roboto Mono", "Courier New", monospace',
  },

  // Font Sizes
  fontSize: {
    xs: '12px',
    sm: '13px',
    base: '14px',
    md: '15px',
    lg: '16px',
    xl: '18px',
    '2xl': '20px',
    '3xl': '24px',
    '4xl': '28px',
    '5xl': '32px',
  },

  // Font Weights
  fontWeight: {
    light: 300,
    regular: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
  },

  // Line Heights
  lineHeight: {
    tight: 1.2,
    normal: 1.5,
    relaxed: 1.75,
    loose: 2,
  },
};

// Spacing System
export const spacing = {
  // Base spacing unit (8px)
  unit: 8,

  // Spacing values
  xs: '4px',
  sm: '8px',
  md: '12px',
  lg: '16px',
  xl: '20px',
  '2xl': '24px',
  '3xl': '32px',
  '4xl': '40px',
  '5xl': '48px',
  '6xl': '64px',

  // Common patterns
  formField: '16px',
  sectionGap: '24px',
  containerPadding: '16px',
};

// Common Dimensions
export const dimensions = {
  // Heights
  height: {
    input: '36px',
    inputLarge: '43px',
    button: '32px',
    buttonLarge: '36px',
    navbar: '64px',
    toolbar: '48px',
    tableRow: '36px',
  },

  // Widths
  width: {
    sidebar: '250px',
    sidebarCollapsed: '64px',
    maxContent: '1200px',
    formNarrow: '400px',
    formMedium: '600px',
    formWide: '800px',
  },

  // Border Radius
  borderRadius: {
    none: '0',
    sm: '2px',
    base: '4px',
    md: '6px',
    lg: '8px',
    xl: '12px',
    full: '9999px',
  },

  // Border Width
  borderWidth: {
    none: '0',
    thin: '1px',
    base: '1px',
    thick: '2px',
  },
};

// Box Shadows
export const shadows = {
  none: 'none',
  xs: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
  sm: '0 2px 4px 0 rgba(0, 0, 0, 0.06)',
  base: '0 2px 8px 0 rgba(0, 0, 0, 0.1)',
  md: '0 4px 12px 0 rgba(0, 0, 0, 0.08)',
  lg: '0 8px 16px 0 rgba(0, 0, 0, 0.1)',
  xl: '0 12px 24px 0 rgba(0, 0, 0, 0.12)',

  // Specific shadows
  card: '0 2px 8px 0 rgba(0, 0, 0, 0.1)',
  hover: '0 4px 12px 0 rgba(0, 0, 0, 0.15)',
  input: '0 2px 4px 0 rgba(0, 0, 0, 0.05)',
  button: '0 2px 4px 0 rgba(0, 0, 0, 0.1)',
  dropdown: '0 4px 16px 0 rgba(0, 0, 0, 0.12)',
  modal: '0 8px 32px 0 rgba(0, 0, 0, 0.16)',
};

// Transitions
export const transitions = {
  // Durations
  duration: {
    fast: '150ms',
    base: '250ms',
    slow: '350ms',
    slower: '500ms',
  },

  // Easing functions
  easing: {
    linear: 'linear',
    easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
    easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
    easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    sharp: 'cubic-bezier(0.4, 0, 0.6, 1)',
  },

  // Common transitions
  all: 'all 250ms cubic-bezier(0.4, 0, 0.2, 1)',
  colors:
    'background-color 250ms cubic-bezier(0.4, 0, 0.2, 1), color 250ms cubic-bezier(0.4, 0, 0.2, 1)',
  opacity: 'opacity 250ms cubic-bezier(0.4, 0, 0.2, 1)',
  shadow: 'box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1)',
  transform: 'transform 250ms cubic-bezier(0.4, 0, 0.2, 1)',
};

// Z-index values
export const zIndex = {
  dropdown: 1000,
  sticky: 1020,
  fixed: 1030,
  modalBackdrop: 1040,
  modal: 1050,
  popover: 1060,
  tooltip: 1070,
  toast: 1080,
};

// Breakpoints
export const breakpoints = {
  xs: '0px',
  sm: '600px',
  md: '960px',
  lg: '1280px',
  xl: '1920px',
};

// Common Patterns
export const patterns = {
  // Form patterns
  form: {
    fieldSpacing: spacing.md,
    labelSpacing: spacing.xs,
    sectionSpacing: spacing['2xl'],
    inputHeight: dimensions.height.input,
  },

  // Card patterns
  card: {
    padding: spacing.lg,
    borderRadius: dimensions.borderRadius.base,
    shadow: shadows.card,
  },

  // Table patterns
  table: {
    rowHeight: dimensions.height.tableRow,
    cellPadding: spacing.sm,
    headerBackground: colors.grey[100],
  },

  // Button patterns
  button: {
    height: dimensions.height.button,
    paddingHorizontal: spacing.lg,
    borderRadius: dimensions.borderRadius.base,
  },
};

// Export all constants as a single object for convenience
const styleConstants = {
  colors,
  typography,
  spacing,
  dimensions,
  shadows,
  transitions,
  zIndex,
  breakpoints,
  patterns,
};

export default styleConstants;

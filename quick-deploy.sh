#!/bin/bash

###############################################################################
# 快速部署脚本 - 适用于日常更新
###############################################################################

SERVER="root@**********"
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${GREEN}🚀 快速部署开始${NC}"

# 1. 快速构建
echo "📦 构建项目..."
mvn clean package -DskipTests -T 4
cd FinancialSystem-web && npm run build && cd ..

# 2. 上传文件
echo "📤 上传文件..."
scp api-gateway/target/api-gateway-*.jar $SERVER:/opt/financial-system/financial-system-new.jar
rsync -avz --delete FinancialSystem-web/build/ $SERVER:/var/www/financial-system/

# 3. 远程执行
echo "🔄 重启服务..."
ssh $SERVER << 'EOF'
    # 备份当前版本
    cp /opt/financial-system/financial-system.jar /opt/financial-system/financial-system.bak
    
    # 切换新版本
    mv /opt/financial-system/financial-system-new.jar /opt/financial-system/financial-system.jar
    
    # 重启服务
    systemctl restart financial-backend
    nginx -s reload
    
    # 检查状态
    sleep 5
    if systemctl is-active financial-backend > /dev/null; then
        echo "✅ 后端服务启动成功"
    else
        echo "❌ 后端服务启动失败，回滚..."
        mv /opt/financial-system/financial-system.bak /opt/financial-system/financial-system.jar
        systemctl restart financial-backend
    fi
EOF

echo -e "${GREEN}✨ 部署完成！${NC}"
echo "访问: http://**********"